package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface InvoiceStatusEnum {
    Integer INVOICE_STATUS_CREATED = 0;
    Integer INVOICE_STATUS_PROCESSING = 1;
    Integer INVOICE_STATUS_COMPLETED = 2;
    Integer INVOICE_STATUS_REMOVED = 3;

    /**
     * invoice type
     */
    String TYPE_NOSHOW = "noshow";

    String TYPE_APPOINTMENT = "appointment";
    String NO_SHOW_FEE_SERVICE_NAME = "No show fee";

    /**
     * online pay链接类型
     */
    Byte TYPE_ONLINE_INVOICE = 1;

    Byte TYPE_ONLINE_DEPOSIT = 2;
}
