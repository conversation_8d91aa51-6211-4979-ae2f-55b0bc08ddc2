moego:
  field-mapping-rules:
    # 通用的规则
    general:
      - path-pattern: $..customerId
        mapper-name: CUSTOMER_ID
      - path-pattern: $..customerIdList
        mapper-name: CUSTOMER_ID
        is-list: true
      - path-pattern: $..customerAddressId
        mapper-name: CUSTOMER_ADDRESS_ID
      - path-pattern: $..petId
        mapper-name: PET_ID
      - path-pattern: $..staffId
        mapper-name: STAFF_ID
      - path-pattern: $..staffIdList
        mapper-name: STAFF_ID
        is-list: true
      - path-pattern: $..tokenStaffId
        mapper-name: STAFF_ID
      - path-pattern: $..serviceId
        mapper-name: GROOMING_SERVICE_ID
      - path-pattern: $..serviceType
        mapper-name: GROOMING_SERVICE_TYPE
      - path-pattern: $..createTime
        mapper-name: TIMESTAMP
      - path-pattern: $..updateTime
        mapper-name: TIMESTAMP
    # 针对特定的类的补充规则
    extra:
      - class-names:
          - com.moego.server.grooming.activity.dto.AppointmentRepeatParamsLogDTO
          - com.moego.server.grooming.params.AppointmentRepeatModifyParams
          - com.moego.server.grooming.params.AppointmentBlockParams
          - com.moego.server.grooming.params.AppointmentRepeatParams
          - com.moego.server.grooming.params.AppointmentParams
        exclude-general: false
        rules:
          - path-pattern: $.repeatApplyType
            mapper-name: APPOINTMENT_REPEAT_APPLY_TYPE
          - path-pattern: $..repeatType
            mapper-name: APPOINTMENT_REPEAT_APPLY_TYPE
          - path-pattern: $..source
            mapper-name: APPOINTMENT_SOURCE
          - path-pattern: $..createdById
            mapper-name: STAFF_ID
          - path-pattern: $..operatorId
            mapper-name: STAFF_ID
          - path-pattern: $..scheduleType
            mapper-name: APPOINTMENT_SCHEDULE_TYPE
          - path-pattern: $..workMode
            mapper-name: APPOINTMENT_WORK_MODE
          - path-pattern: $..isWaitingList
            mapper-name: BOOLEAN
          - path-pattern: $..isPaid
            mapper-name: APPOINTMENT_PAY_STATUS
          - path-pattern: $..status
            mapper-name: APPOINTMENT_STATUS
          - path-pattern: $..appointmentStartTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $..startTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $..endTime
            mapper-name: MINUTE_TO_TIME
      - class-names:
        - com.moego.server.grooming.mapperbean.MoeGroomingAppointment
        exclude-general: true
        rules:
          - path-pattern: $.customerId
            mapper-name: CUSTOMER_ID
          - path-pattern: $.customerAddressId
            mapper-name: CUSTOMER_ADDRESS_ID
          - path-pattern: $.isWaitingList
            mapper-name: BOOLEAN
          - path-pattern: $.moveWaitingBy
            mapper-name: STAFF_ID
          - path-pattern: $.status
            mapper-name: APPOINTMENT_STATUS
          - path-pattern: $.isPaid
            mapper-name: APPOINTMENT_PAY_STATUS
          - path-pattern: $.noShow
            mapper-name: BOOLEAN
          - path-pattern: $.cancelByType
            mapper-name: APPOINTMENT_CANCEL_BY_TYPE
          - path-pattern: $.cancelBy
            mapper-name: STAFF_ID
          - path-pattern: $.confirmByType
            mapper-name: APPOINTMENT_CONFIRM_BY_TYPE
          - path-pattern: $.confirmBy
            mapper-name: STAFF_ID
          - path-pattern: $.createdById
            mapper-name: STAFF_ID
          - path-pattern: $.source
            mapper-name: APPOINTMENT_SOURCE
          - path-pattern: $.isBlock
            mapper-name: BOOLEAN
          - path-pattern: $.isDeprecate
            mapper-name: BOOLEAN
          - path-pattern: $.scheduleType
            mapper-name: APPOINTMENT_SCHEDULE_TYPE
          - path-pattern: $.appointmentStartTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $.appointmentEndTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $.oldAppointmentStartTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $.oldAppointmentEndTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $.createTime
            mapper-name: TIMESTAMP
          - path-pattern: $.updateTime
            mapper-name: TIMESTAMP
          - path-pattern: $.checkInTime
            mapper-name: TIMESTAMP
          - path-pattern: $.checkOutTime
            mapper-name: TIMESTAMP
          - path-pattern: $.confirmedTime
            mapper-name: TIMESTAMP
          - path-pattern: $.canceledTime
            mapper-name: TIMESTAMP
      - class-names:
          - com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices
        exclude-general: true
        rules:
          - path-pattern: $.customerId
            mapper-name: CUSTOMER_ID
          - path-pattern: $.createBy
            mapper-name: STAFF_ID
          - path-pattern: $.petId
            mapper-name: PET_ID
          - path-pattern: $.serviceType
            mapper-name: GROOMING_SERVICE_TYPE
          - path-pattern: $.saveType
            mapper-name: GROOMING_CUSTOMER_SERVICE_SAVE_TYPE
          - path-pattern: $.status
            mapper-name: STATUS
      - class-names:
          - com.moego.server.grooming.params.TransferAppointmentParams
        exclude-general: true
        rules:
          - path-pattern: $.sourceStaffId
            mapper-name: STAFF_ID
          - path-pattern: $.targetStaffId
            mapper-name: STAFF_ID
      - class-names:
          - com.moego.server.grooming.params.IdParams
          - com.moego.server.grooming.params.EditCommentsParams
          - com.moego.server.grooming.params.EditIdParams
          - com.moego.server.grooming.params.CancelParams
          - com.moego.server.grooming.params.ConfirmParams
          - com.moego.server.grooming.params.ColorEditParams
        exclude-general: true
        rules:
          - path-pattern: $.accountId
            mapper-name: STAFF_ID
          - path-pattern: $.noShow
            mapper-name: BOOLEAN
          - path-pattern: $.cancelByType
            mapper-name: APPOINTMENT_CANCEL_BY_TYPE
          - path-pattern: $.repeatType
            mapper-name: APPOINTMENT_REPEAT_APPLY_TYPE
          - path-pattern: $.confirmByType
            mapper-name: APPOINTMENT_CONFIRM_BY_TYPE
          - path-pattern: $.isPaid
            mapper-name: APPOINTMENT_PAY_STATUS
      - class-names:
          - com.moego.server.grooming.params.EditRepeatParams
          - com.moego.server.grooming.params.AddRepeatParams
          - com.moego.server.grooming.params.BlockRepeatParams
        exclude-general: false
        rules:
          - path-pattern: $..type
            mapper-name: APPOINTMENT_REPEAT_END_TYPE
          - path-pattern: $..repeatType
            mapper-name: APPOINTMENT_REPEAT_FREQUENCY_TYPE
          - path-pattern: $.source
            mapper-name: APPOINTMENT_SOURCE
          - path-pattern: $.startTime
            mapper-name: MINUTE_TO_TIME
          - path-pattern: $.endTime
            mapper-name: MINUTE_TO_TIME
      - class-names:
          - com.moego.server.grooming.service.dto.ServiceSaveDto
          - com.moego.server.grooming.service.dto.ServiceUpdateDto
        exclude-general: false
        rules:
          - path-pattern: $.breedFilter
            mapper-name: GROOMING_SERVICE_FILTER_SCOPE
          - path-pattern: $.weightFilter
            mapper-name: GROOMING_SERVICE_FILTER_SCOPE
          - path-pattern: $.coatFilter
            mapper-name: GROOMING_SERVICE_FILTER_SCOPE
          - path-pattern: $.type
            mapper-name: GROOMING_SERVICE_TYPE
          - path-pattern: $.inactive
            mapper-name: BOOLEAN
          - path-pattern: $.bookOnlineAvailable
            mapper-name: BOOLEAN
          - path-pattern: $.isAllStaff
            mapper-name: BOOLEAN
          - path-pattern: $.showBasePrice
            mapper-name: GROOMING_SERVICE_SHOW_PRICE
          - path-pattern: $..petTypeId
            mapper-name: PET_TYPE
      - class-names:
          - com.moego.server.customer.mapperbean.MoePetVaccine
          - com.moego.server.customer.params.VaccineBindingSaveVo
          - com.moego.server.customer.web.vo.VaccineBindingUpdateVo
        exclude-general: true
        rules:
          - path-pattern: $.petId
            mapper-name: PET_ID
#          - path-pattern: $.vaccineId
#            mapper-name: TODO
          - path-pattern: $.status
            mapper-name: STATUS
          - path-pattern: $.type
            mapper-name: PET_VACCINE_TYPE
      - class-names:
          - com.moego.server.customer.mapperbean.MoePetType
        exclude-general: true
        rules:
          - path-pattern: $.isAvailable
            mapper-name: BOOLEAN
          - path-pattern: $.bookOnlineAvailable
            mapper-name: BOOLEAN
      - class-names:
          - com.moego.server.customer.params.PetBreedParams
        exclude-general: true
        rules:
          - path-pattern: $.petTypeId
            mapper-name: PET_TYPE
      - class-names:
          - com.moego.server.customer.web.vo.CustomerAddByContactVo
          - com.moego.server.customer.params.UpdateCustomerInfoParams
          - com.moego.server.customer.params.CustomerContactUpdateVo
          - com.moego.server.customer.web.vo.CustomerAddressAddVo
          - com.moego.server.customer.web.vo.CustomerAddressUpdateVo
          - com.moego.server.customer.params.CustomerBatchUpdateParams
          - com.moego.server.customer.params.PreferredTipConfigParams
          - com.moego.server.customer.mapperbean.MoeBusinessCustomer
          - com.moego.server.customer.mapperbean.MoeCustomerAddress
          - com.moego.server.customer.mapperbean.MoeCustomerContact
        exclude-general: false
        rules:
          - path-pattern: $..customerContactId
            mapper-name: CUSTOMER_CONTACT_ID
          - path-pattern: $.primaryContactId
            mapper-name: CUSTOMER_CONTACT_ID
          - path-pattern: $..isPrimary
            mapper-name: BOOLEAN
          - path-pattern: $.inactive
            mapper-name: BOOLEAN
          - path-pattern: $.isBlockMessage
            mapper-name: BOOLEAN
          - path-pattern: $.isBlockOnlineBooking
            mapper-name: BOOLEAN
          - path-pattern: $.sendAutoEmail
            mapper-name: BOOLEAN
          - path-pattern: $.sendAutoMessage
            mapper-name: BOOLEAN
          - path-pattern: $.isUnsubscribed
            mapper-name: BOOLEAN
          - path-pattern: $.enable
            mapper-name: BOOLEAN
          - path-pattern: $.status
            mapper-name: STATUS
          - path-pattern: $.addressId
            mapper-name: CUSTOMER_ADDRESS_ID
          - path-pattern: $.tipType
            mapper-name: CUSTOMER_PREFERRED_TIP_TYPE
          - path-pattern: $.preferredGroomerId
            mapper-name: STAFF_ID
          - path-pattern: $.preferredFrequencyType
            mapper-name: CUSTOMER_FREQUENCY_TYPE
          - path-pattern: $.type
            mapper-name: CUSTOMER_CONTACT_TYPE
          - path-pattern: $.apptReminderByList
            mapper-name: CUSTOMER_REMINDER_RECEIVE_TYPE
            is-list: true
          - path-pattern: $.createBy
            mapper-name: STAFF_ID
          - path-pattern: $.updateBy
            mapper-name: STAFF_ID
      - class-names:
          - com.moego.server.customer.params.CustomerPetAddParams
          - com.moego.server.customer.params.CustomerPetUpdateParams
          - com.moego.server.customer.service.params.PetLifeStatusParams
          - com.moego.server.customer.service.params.UpdateMedicalInfoParams
          - com.moego.server.customer.mapperbean.MoeCustomerPet
        exclude-general: false
        rules:
          - path-pattern: $.id
            mapper-name: PET_ID
          - path-pattern: $.petTypeId
            mapper-name: PET_TYPE
          - path-pattern: $.breedMix
            mapper-name: BOOLEAN
          - path-pattern: gender
            mapper-name: PET_GENDER
          - path-pattern: $.lifeStatus
            mapper-name: PET_LIFE_STATUS
          - path-pattern: $.status
            mapper-name: STATUS
          - path-pattern: $.expiryNotification
            mapper-name: BOOLEAN
#          - path-pattern: $..vaccineId
#            mapper-name: TODO
      - class-names:
          - com.moego.server.message.params.MessageReminderParams
        exclude-general: true
        rules:
          - path-pattern: $.reminderType
            mapper-name: REMINDER_TYPE
          - path-pattern: $.beforeDay
            mapper-name: REMINDER_BEFORE_DAY
      - class-names:
          - com.moego.idl.api.auto_message.v1.UpdateAppointmentAutoMessageParams
          - com.moego.idl.api.auto_message.v1.UpdatePayAutoMessageParams
          - com.moego.idl.api.auto_message.v1.UpdateAppointmentReminderParams
          - com.moego.idl.api.auto_message.v1.UpdateReminderParams
        exclude-general: true
        rules:
          - path-pattern: $.id
            mapper-name: AUTO_MESSAGE_ID

      - class-names:
          - com.moego.server.message.params.MoeBusinessAutoMessageTemplateParams
        exclude-general: true
        rules:
          - path-pattern: $.type
            mapper-name: AUTO_MESSAGE_TYPE
          - path-pattern: $.status
            mapper-name: STATUS
      - class-names:
          - com.moego.server.message.mapperbean.MoeBusinessAutoReply
        exclude-general: true
        rules:
          - path-pattern: $.status
            mapper-name: STATUS
      - class-names:
          - com.moego.server.message.params.MoeBusinessReviewBoosterParams
        exclude-general: true
        rules:
          - path-pattern: $.enableAuto
            mapper-name: BOOLEAN
      - class-names:
          - com.moego.idl.service.order.v1.CreateOrderRequest
          - com.moego.idl.service.order.v1.UpdateOrderRequest
          - com.moego.idl.service.order.v1.UpdateOrderIncrRequest
        rules:
          - path-pattern: $..createBy
            mapper-name: STAFF_ID
          - path-pattern: $..updateBy
            mapper-name: STAFF_ID
          - path-pattern: $..applyBy
            mapper-name: STAFF_ID
          - path-pattern: $..taxId
            mapper-name: TAX_ID
          - path-pattern: $.order.status
            mapper-name: ORDER_STATUS
      - class-names:
          - com.moego.server.retail.service.params.PackageParams
          - com.moego.server.retail.service.params.UpdatePackageParams
        exclude-general: true
        rules:
          - path-pattern: $.taxId
            mapper-name: TAX_ID
      - class-names:
          - com.moego.server.grooming.params.PurchasedPackage
        rules:
          - path-pattern: $.purchaseTime
            mapper-name: TIMESTAMP
          - path-pattern: $.startTime
            mapper-name: TIMESTAMP
          - path-pattern: $.endTime
            mapper-name: TIMESTAMP
      - class-names:
          - com.moego.server.grooming.params.InvoiceAmountVo
        rules:
          - path-pattern: $.invoiceId
            mapper-name: ORDER_ID
          - path-pattern: $.lastModifiedTime
            mapper-name: TIMESTAMP
      - class-names:
          - com.moego.server.retail.service.params.SetDiscountParams
        rules:
          - path-pattern: $.orderId
            mapper-name: ORDER_ID
      - class-names:
          - com.moego.server.retail.service.params.SaveOrderRetailItemsParams
        rules:
          - path-pattern: $.orderId
            mapper-name: ORDER_ID
          - path-pattern: $..taxId
            mapper-name: TAX_ID
