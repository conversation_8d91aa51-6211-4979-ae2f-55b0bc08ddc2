package com.moego.svc.activitylog.server.service.informer;

import static com.moego.svc.activitylog.server.util.Options.justDo;

import com.moego.svc.activitylog.server.service.model.ActivityLogNameInfo;
import com.moego.svc.activitylog.server.service.model.ActivityLogOperator;
import com.moego.svc.activitylog.server.service.model.ActivityLogResource;

/**
 * Has operator informer.
 *
 * @param <Resource> resource type
 * @param <Operator> operator type
 * <AUTHOR>
 */
public interface HasOperatorInformer<Resource, Operator> extends Informer<Resource>, HasOperator<Operator> {

    @Override
    default ActivityLogNameInfo fetch(String resourceId, String operatorId) {
        Resource resource = resourceId != null ? justDo(() -> resource(resourceId)) : null;
        Operator operator = operatorId != null ? justDo(() -> operator(operatorId)) : null;
        return new ActivityLogNameInfo(
                resource != null ? new ActivityLogResource(resourceId, resourceType(), resourceName(resource)) : null,
                operator != null ? new ActivityLogOperator(operatorId, operatorName(operator)) : null,
                null);
    }
}
