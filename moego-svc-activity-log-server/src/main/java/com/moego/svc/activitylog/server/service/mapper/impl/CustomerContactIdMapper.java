package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.customer.client.ICustomerContactClient;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class CustomerContactIdMapper implements Mapper<CustomerContactDto> {

    private final ICustomerContactClient customerContactApi;

    @Override
    public Map<String, String> map(Set<String> contactIds) {
        Map<String, String> result = new HashMap<>(contactIds.size());

        for (String contactId : contactIds) {
            try {
                var contactIdInt = Integer.parseInt(contactId);
                if (contactIdInt <= 0) {
                    continue;
                }
                var contact = customerContactApi.getCustomerContactById(contactIdInt);
                if (contact != null) {
                    var name = getName(contact);
                    result.put(contactId, name);
                }
            } catch (Exception e) {
                log.error("Failed to get customer contact name for customer contact id: {}", contactId, e);
            }
        }
        return result;
    }

    @Override
    public String getName(CustomerContactDto contact) {
        return String.join(", ", contact.getName(), contact.getPhoneNumber(), contact.getEmail(), contact.getTitle());
    }
}
