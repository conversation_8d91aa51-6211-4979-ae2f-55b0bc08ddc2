package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SquareCardInformer extends AbstractStaffOperatorCustomerOwnerInformer<String> {

    @Override
    public String resourceType() {
        return ResourceType.SQUARE_CARD.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String resource(String resourceId) {
        return resourceId;
    }

    @Override
    public String getOwnerId(String customerId) {
        return customerId;
    }

    @Override
    public String resourceName(String s) {
        return "Square Card";
    }
}
