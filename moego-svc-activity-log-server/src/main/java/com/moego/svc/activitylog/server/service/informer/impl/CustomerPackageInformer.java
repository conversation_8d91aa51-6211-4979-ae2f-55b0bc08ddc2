package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.grooming.client.IGroomingPackageClient;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.params.GetPackageParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CUSTOMER_PACKAGE}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CustomerPackageInformer extends AbstractStaffOperatorCustomerOwnerInformer<GroomingPackageDTO> {

    private final IGroomingPackageClient packageApi;

    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_PACKAGE.toString();
    }

    @Override
    public String resourceName(GroomingPackageDTO packageDTO) {
        return packageDTO.getPackageName();
    }

    @Override
    public GroomingPackageDTO resource(String resourceId) {
        var request = new GetPackageParams(Integer.parseInt(resourceId));
        return packageApi.getPackage(request).packageDTO();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(GroomingPackageDTO packageDTO) {
        return String.valueOf(packageDTO.getCustomerId());
    }
}
