package com.moego.svc.activitylog.server.service.informer;

import com.moego.svc.activitylog.server.service.model.ActivityLogNameInfo;

/**
 * {@link Informer} use to fetch activity log name info.
 *
 * <AUTHOR>
 */
public interface Informer<Resource> extends HasResource<Resource> {

    /**
     * Check whether support this resource type.
     *
     * @param resourceType resource type
     * @return whether support this resource type
     */
    default boolean support(String resourceType) {
        String rt = resourceType();
        return rt != null && rt.equalsIgnoreCase(resourceType);
    }

    /**
     * Get activity log name info.
     *
     * @param resourceId resource id
     * @param operatorId operator id
     * @return activity log name info, never null
     */
    ActivityLogNameInfo fetch(String resourceId, String operatorId);

    @Override
    default Resource resource(String resourceId) {
        return null;
    }

    @Override
    default String resourceName(Resource resource) {
        return null;
    }
}
