package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.message.api.IMessageThreadService;
import com.moego.server.message.dto.MessageThreadDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#CHAT}, aka message thread.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ChatInformer extends AbstractStaffOperatorCustomerOwnerInformer<MessageThreadDTO> {

    private final IMessageThreadService messageThreadApi;

    @Override
    public String resourceType() {
        return ResourceType.CHAT.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(MessageThreadDTO messageThreadDTO) {
        return Optional.ofNullable(messageThreadDTO.getCustomerId())
                .map(String::valueOf)
                .orElse(null);
    }

    @Override
    public MessageThreadDTO resource(String resourceId) {
        return messageThreadApi.getMessageThreadById(Integer.valueOf(resourceId));
    }

    @Override
    public String resourceName(MessageThreadDTO messageThreadDTO) {
        return "Chat with %s %s".formatted(messageThreadDTO.getFirstName(), messageThreadDTO.getLastName());
    }
}
