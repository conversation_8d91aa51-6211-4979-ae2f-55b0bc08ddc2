package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

@Component
public class MessageTemplateInformer extends AbstractStaffOperatorInformer<Object> {

    @Override
    public String resourceType() {
        return ResourceType.MESSAGE_TEMPLATE.toString();
    }
}
