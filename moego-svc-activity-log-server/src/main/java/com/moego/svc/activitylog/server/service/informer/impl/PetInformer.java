package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * For resource type {@link ResourceType#PET}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PetInformer extends AbstractStaffOperatorCustomerOwnerInformer<CustomerPetDetailDTO> {

    private final IPetService petApi;

    @Override
    public String getOwnerId(CustomerPetDetailDTO pet) {
        return String.valueOf(pet.getCustomerId());
    }

    @Override
    public String resourceType() {
        return ResourceType.PET.toString();
    }

    @Override
    public CustomerPetDetailDTO resource(String resourceId) {
        List<CustomerPetDetailDTO> pets = petApi.getCustomerPetListByIdList(List.of(Integer.parseInt(resourceId)));
        return !CollectionUtils.isEmpty(pets) ? pets.get(0) : null;
    }

    @Override
    public String resourceName(CustomerPetDetailDTO resource) {
        return resource.getPetName();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
