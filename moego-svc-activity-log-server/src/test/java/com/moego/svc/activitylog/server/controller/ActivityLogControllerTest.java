package com.moego.svc.activitylog.server.controller;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Comparator;
import java.util.List;
import org.junit.jupiter.api.Test;

class ActivityLogControllerTest {

    @Test
    void testParallelStream() {
        List<Integer> list = List.of(1, 2, 3, 4, 5).parallelStream().toList();
        assertThat(list).containsExactly(1, 2, 3, 4, 5);

        list = List.of(1, 2, 3, 4, 5).parallelStream()
                .sorted(Comparator.comparing(Integer::intValue).reversed())
                .toList();
        assertThat(list).containsExactly(5, 4, 3, 2, 1);
    }
}
