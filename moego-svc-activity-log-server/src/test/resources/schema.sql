CREATE TABLE all_activity_log
(
    id                   VARCHAR(255) NOT NULL,
    business_id          BIGINT,
    operator_id          BIGINT,
    operator_name        VA<PERSON>HA<PERSON>(255),
    `action`             VARCHAR(255),
    resource_type        VA<PERSON>HAR(255),
    resource_id          BIGINT,
    resource_name        <PERSON><PERSON><PERSON><PERSON>(255),
    owner_id             BIGINT,
    owner_name           VA<PERSON><PERSON><PERSON>(255),
    `time`               TIMESTAMP,
    details              VARCHAR(255),
    is_root              BOOLEAN,
    request_id           VARCHAR(255),
    created_at           TIMESTAMP,
    company_id           BIGINT,
    CONSTRAINT pk_activitylog PRIMARY KEY (id)
);
