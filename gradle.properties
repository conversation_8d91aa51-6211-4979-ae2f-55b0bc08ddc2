# spring
springBootVersion=3.1.4
springCloudVersion=2022.0.4
springDependencyManagementVersion=1.1.3

# see https://springdoc.org
springdocVersion=2.2.0

grpcVersion=1.58.0
protobufVersion=3.24.4
pgvVersion=1.0.2
sentryVersion=6.28.0
pulsarClientVersion=2.11.1
classpathReplacerVersion=2.1.2
# https://github.com/growthbook/growthbook-sdk-java
growthBookVersion=0.9.91

# https://central.sonatype.com/artifact/org.mybatis.spring.boot/mybatis-spring-boot
mybatisVersion=3.5.17
# https://central.sonatype.com/artifact/org.mybatis.spring.boot/mybatis-spring-boot
# https://github.com/mybatis/spring-boot-starter/releases/tag/mybatis-spring-boot-3.0.2
mybatisBootStarterVersion=3.0.2
# https://github.com/mybatis/generator/releases
mybatisGeneratorCoreVersion=1.4.2
# https://github.com/mybatis/mybatis-dynamic-sql/releases
mybatisDynamicSqlVersion=1.5.2
# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
# https://github.com/pagehelper/pagehelper-spring-boot/releases/
pagehelperSpringBootStarterVersion=2.0.0

# code quality
spotlessVersion=6.22.0
spotbugsVersion=5.2.1
spotbugsAnnotationsVersion=4.7.3

# plugin
protobufGradlePluginVersion=0.9.4

org.gradle.jvmargs=-Xmx4g
org.gradle.caching=true
org.gradle.cache.lockTimeout=1200000
