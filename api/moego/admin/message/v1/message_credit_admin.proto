syntax = "proto3";

package moego.admin.message.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.message.v1";

// MessageCreditAdminApi is the service that provides APIs for managing message credits.
service MessageCreditAdminApi {
  // GetMessageCredit gets the message credit of a company.
  rpc GetMessageCredit(GetMessageCreditParams) returns (GetMessageCreditResult);
  // AddMessageCredit adds message credit to a company.
  rpc AddMessageCredit(AddMessageCreditParams) returns (AddMessageCreditResult);
}

// GetMessageCreditParams is the request message for getting the message credit of a company.
message GetMessageCreditParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetMessageCreditResult is the response message for getting the message credit of a company.
message GetMessageCreditResult {
  // Message credit details.
  MessageCredit message_credit = 1;
}

// AddMessageCreditParams is the request message for adding message credit to a company.
message AddMessageCreditParams {
  // Company ID.
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The email credits to add.
  optional EmailCreditUpdateDef email_credit = 2;

  // The SMS credits to add.
  optional SmsCreditUpdateDef sms_credit = 3;
}

// Defines the values to add to the email credits.
message EmailCreditUpdateDef {
  // The amount of purchased email credits to add.
  optional int32 purchased_credits = 1;
}

// Defines the values to add to the SMS credits.
message SmsCreditUpdateDef {
  // The amount of purchased SMS credits to add.
  optional int32 purchased_credits = 1;
}

// AddMessageCreditResult is the response message for adding message credit to a company.
message AddMessageCreditResult {
  // Message credit details.
  MessageCredit message_credit = 1;
}

// MessageCredit defines the message credit details for a company.
message MessageCredit {
  // ID.
  int32 id = 1;
  // Company ID.
  int64 company_id = 2;
  // Cycle begin time.
  google.protobuf.Timestamp cycle_begin_time = 3;
  // Cycle end time.
  google.protobuf.Timestamp cycle_end_time = 4;
  // Email credit details.
  EmailCredit email_credit = 5;
  // SMS credit details.
  SmsCredit sms_credit = 6;
}

// EmailCredit defines the email credit details.
message EmailCredit {
  // The number of remained email credits.
  // remained_credits = subscription_credits + purchased_credits + leftover_purchased_credits - used_credits
  int32 remained_credits = 1;
  // The number of credits from the subscription for the current cycle.
  // These credits do not roll over to the next cycle.
  int32 subscription_credits = 2;
  // The number of additionally purchased credits for the current cycle.
  int32 purchased_credits = 3;
  // The number of unused purchased credits rolled over from the previous cycle.
  int32 leftover_purchased_credits = 4;
  // The total number of used credits in the current cycle.
  int32 used_credits = 5;
}

// SmsCredit defines the SMS credit details.
message SmsCredit {
  // The number of remained SMS credits.
  // remained_credits = (subscription_credits + purchased_credits + leftover_purchased_credits) - (used_two_way_credits + used_auto_message_credits + used_call_credits)
  int32 remained_credits = 1;
  // The number of credits from the subscription for the current cycle.
  // These credits do not roll over to the next cycle.
  int32 subscription_credits = 2;
  // The number of additionally purchased credits for the current cycle.
  int32 purchased_credits = 3;
  // The number of unused purchased credits rolled over from the previous cycle.
  int32 leftover_purchased_credits = 4;
  // The number of used credits for two-way messaging.
  int32 used_two_way_credits = 5;
  // The number of used credits for automated messages.
  int32 used_auto_message_credits = 6;
  // The number of used credits for calls.
  int32 used_call_credits = 7;
}
