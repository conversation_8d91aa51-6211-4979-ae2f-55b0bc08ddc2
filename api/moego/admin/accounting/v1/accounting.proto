// @since 2024-10-24 16:44:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.accounting.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "moego/models/accounting/v1/accounting_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/accounting/v1;accountingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.accounting.v1";

// compensate request
message CompensateRequest {
  // compensate type
  enum CompensateType {
    // unspecified
    COMPENSATE_TYPE_UNSPECIFIED = 0;
    // by time range
    BY_TIME_RANGE = 1;
    // by id
    BY_ID = 2;
  }
  // entity compensate
  message EntityCompensate {
    // entity type
    moego.models.accounting.v1.SyncEntityType entity_type = 1;
    // entity id
    string entity_id = 2;
  }
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // compensate type
  CompensateType compensate_type = 2;
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 3;
  // oneof
  oneof compensate_target {
    // time range
    google.type.Interval time_range = 5;
    // entity id
    EntityCompensate entity_compensate = 6;
  }
}

// AccountingService
service AccountingService {
  // compensate
  rpc Compensate(CompensateRequest) returns (google.protobuf.Empty);
}
