// @since 2023-08-17 14:05:33
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.account.v1;

import "google/type/date.proto";
import "moego/models/account/v1/session_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.account.v1";

// describe sessions request
message DescribeSessionsParams {
  // account identifier
  oneof identifier {
    option (validate.required) = true;

    // session id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];

    // account id
    int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
  }

  // source
  optional string source = 4 [(validate.rules).string = {
    in: [
      "business",
      "customer",
      "mis"
    ]
  }];

  // only describe active sessions, deleted or expired sessions will be ignored
  optional bool only_active = 5;

  // impersonator type, optional, default is 0
  // - 0 is exclude all impersonator sessions
  // - 1 is include all impersonator sessions
  // - 2 is only query impersonator sessions
  optional int32 impersonator_type = 6;

  // order by, field name should be in SessionModel
  optional moego.utils.v2.OrderBy order_by = 14;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// describe sessions result
message DescribeSessionsResult {
  // sessions
  repeated moego.models.account.v1.SessionModel sessions = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// batch delete sessions params
message BatchDeleteSessionsParams {
  // ids
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch delete sessions result
message BatchDeleteSessionsResult {}

// batch update sessions params
message BatchUpdateSessionsParams {
  // ids
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // max age, in units of seconds
  optional int64 max_age = 2 [(validate.rules).int64 = {gte: 0}];
}

// batch update sessions result
message BatchUpdateSessionsResult {}

// create session archive task params
message CreateSessionArchiveTaskParams {
  // start id
  int64 start_id = 1 [(validate.rules).int64.gte = 0];
  // end id
  int64 end_id = 2 [(validate.rules).int64.gte = 0];
  // step
  int32 step = 3 [(validate.rules).int32.gt = 0];
  // max date
  google.type.Date max_date = 4 [(validate.rules).message.required = true];
}

// create session archive task result
message CreateSessionArchiveTaskResult {
  // task id
  int64 id = 1;
}

// update session archive task status params
message UpdateSessionArchiveTaskStatusParams {
  // task id
  int64 id = 1;
  // status
  string status = 2;
}

// update session archive task status result
message UpdateSessionArchiveTaskStatusResult {}

// the session admin service
service SessionService {
  // describe sessions
  rpc DescribeSessions(DescribeSessionsParams) returns (DescribeSessionsResult);

  // batch delete sessions
  rpc BatchDeleteSessions(BatchDeleteSessionsParams) returns (BatchDeleteSessionsResult);
  // batch update sessions
  rpc BatchUpdateSessions(BatchUpdateSessionsParams) returns (BatchUpdateSessionsResult);

  // create session archive task
  rpc CreateSessionArchiveTask(CreateSessionArchiveTaskParams) returns (CreateSessionArchiveTaskResult);

  // update session archive task status
  rpc UpdateSessionArchiveTaskStatus(UpdateSessionArchiveTaskStatusParams) returns (UpdateSessionArchiveTaskStatusResult);
}
