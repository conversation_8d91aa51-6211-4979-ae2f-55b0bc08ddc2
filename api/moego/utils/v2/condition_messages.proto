// @since 2022-05-30 17:23:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v2;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2;utilsV2";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v2";

// operator
enum Operator {
  // unspecified
  OPERATOR_UNSPECIFIED = 0;
  // equal
  OPERATOR_EQ = 1;
  // not equal
  OPERATOR_NE = 2;
  // greater than
  OPERATOR_GT = 3;
  // greater than or equal
  OPERATOR_GE = 4;
  // less than
  OPERATOR_LT = 5;
  // less than or equal
  OPERATOR_LE = 6;
  // like
  OPERATOR_LIKE = 7;
  // not like
  OPERATOR_NOT_LIKE = 8;
  // prefix like
  OPERATOR_PREFIX_LIKE = 9;
  // prefix not like
  OPERATOR_PREFIX_NOT_LIKE = 10;
  // suffix like
  OPERATOR_SUFFIX_LIKE = 11;
  // suffix not like
  OPERATOR_SUFFIX_NOT_LIKE = 12;
  // in
  OPERATOR_IN = 13;
  // not in
  OPERATOR_NOT_IN = 14;
  // between
  OPERATOR_BETWEEN = 15;
  // not between
  OPERATOR_NOT_BETWEEN = 16;
}

// wrapper of number(s)
message NumberWrapper {
  // value
  optional int64 value = 1;
  // values
  repeated int64 values = 2;
}

// wrapper of string(s)
message StringWrapper {
  // value
  optional string value = 1;
  // values
  repeated string values = 2;
}

// wrapper of time
message TimeWrapper {
  // timestamp
  optional google.protobuf.Timestamp timestamp = 1;
  // date
  optional google.protobuf.Timestamp date = 2;
}

// wrapper of money
message MoneyWrapper {
  // value
  optional google.type.Money value = 1;
  // values
  repeated google.type.Money values = 2;
}

// predicate
message Predicate {
  // field name
  string field_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];

  // operator
  Operator operator = 2 [(validate.rules).enum = {defined_only: true}];

  // value, required
  oneof value {
    option (validate.required) = true;
    // number value(s)
    NumberWrapper number = 5;
    // string value(s)
    StringWrapper string = 6;
    // time wrapper
    TimeWrapper time = 7;
    // money wrapper
    MoneyWrapper money = 8;
  }

  // with predicate, optional
  oneof with_predicate {
    option (validate.required) = false;
    // and
    Predicate and = 3;
    // or
    Predicate or = 4;
  }
}

// order by
message OrderBy {
  // field name
  string field_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // asc, if not set, default is true
  optional bool asc = 2;
}

// Range
message Range {
  // start
  optional int64 start = 1;
  // end
  optional int64 end = 2;
}

// StringDateRange represents a range of dates
message StringDateRange {
  // start date
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}
