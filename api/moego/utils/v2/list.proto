syntax = "proto3";

package moego.utils.v2;

import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2;utilsV2";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v2";

/**
   * A list of int64 values.
   used for:
   1. As map values
   2. Differentiate whether the frontend did not pass a value or passed an empty array.
*/
message Int64List {
  // The list of values
  repeated int64 values = 1;
}

// Int32List represents a list of int32 values
message Int32List {
  // The list of values
  repeated int32 values = 1;
}

// DateList represents a list of google.type.Date
message DateList {
  // The list of values
  repeated google.type.Date dates = 1;
}
