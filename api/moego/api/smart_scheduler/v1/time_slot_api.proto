// @since 2025-06-06 11:03:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.smart_scheduler.v1;

import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/smart_scheduler/v1/time_slot_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/smart_scheduler/v1;smartschedulerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.smart_scheduler.v1";

// list time_slot params
message ListTimeSlotsParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  // date
  repeated string dates = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // pet param
  repeated moego.models.smart_scheduler.v1.PetParam pet_params = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // filter appointment id
  optional int64 filter_appointment_id = 4 [(validate.rules).int64.gt = 0];
}

// list time_slot result
message ListTimeSlotsResult {
  // the time_slot
  repeated moego.models.smart_scheduler.v1.TimeSlot time_slots = 1;
}

// list time slot params
message ListAllTimeSlotsParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// list time slot result
message ListAllTimeSlotsResult {
  // the time slot
  repeated moego.models.smart_scheduler.v1.TimeSlot time_slots = 1;
}

// the time_slot service
service TimeSlotService {
  // list time_slot
  rpc ListAvailableTimeSlots(ListTimeSlotsParams) returns (ListTimeSlotsResult);

  // list all time slot
  rpc ListAllTimeSlots(ListAllTimeSlotsParams) returns (ListAllTimeSlotsResult);
}
