// @since 2024-06-13 11:16:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.membership.v1;

import "moego/models/membership/v1/data_migration_models.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/sell_link_defs.proto";
import "moego/models/membership/v1/subscription_defs.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/service/subscription/v1/subscription_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.membership.v1";

// the subscription service
service SubscriptionService {
  // create sell link
  rpc CreateSellLink(CreateSellLinkParams) returns (CreateSellLinkResult);
  // create new card on file with public token
  rpc CreateCard(CreateCardParams) returns (CreateCardResult);
  // create subscription
  rpc SellMembership(SellMembershipParams) returns (SellMembershipResult);
  // get subscription details
  rpc GetSubscriptionDetail(GetSubscriptionDetailParams) returns (GetSubscriptionDetailResult);
  // update subscription
  rpc UpdateSubscription(UpdateSubscriptionParams) returns (UpdateSubscriptionResult);
  // list subscriptions
  rpc ListSubscriptions(ListSubscriptionsParams) returns (ListSubscriptionsResult);
  // cancel subscription
  rpc CancelSubscription(CancelSubscriptionParams) returns (CancelSubscriptionResult);
  // renew subscription
  rpc RenewSubscription(RenewSubscriptionParams) returns (RenewSubscriptionResult);
  // pause subscription
  rpc PauseSubscription(PauseSubscriptionParams) returns (PauseSubscriptionResult);
  // resume subscription
  rpc ResumeSubscription(ResumeSubscriptionParams) returns (ResumeSubscriptionResult);
  // list membership's buyers
  rpc ListBuyers(ListBuyersParams) returns (ListBuyersResult);
  // get membership buyer report
  rpc GetBuyerReport(GetBuyerReportParams) returns (GetBuyerReportResult);
  // list payment history
  rpc ListPaymentHistory(ListPaymentHistoryParams) returns (ListPaymentHistoryHistoryResult);

  // import subscriptions
  rpc ImportSubscriptions(ImportSubscriptionsParams) returns (ImportSubscriptionsResult);
  // import quantity entitlements
  rpc ImportQuantityEntitlements(ImportQuantityEntitlementsParams) returns (ImportQuantityEntitlementsResult);
}

// import subscriptions request
message ImportSubscriptionsParams {
  // subscription data
  models.membership.v1.SubscriptionData data = 1;
}

// import subscriptions response
message ImportSubscriptionsResult {
  // successfully imported subscription data
  models.membership.v1.SubscriptionData imported = 1;
}

// import quantity entitlements request
message ImportQuantityEntitlementsParams {
  // quantity entitlement data
  models.membership.v1.QuantityEntitlementData data = 1;
}

// import quantity entitlements response
message ImportQuantityEntitlementsResult {
  // successfully imported quantity entitlement data
  models.membership.v1.QuantityEntitlementData imported = 1;
}

// create subscription params
message CreateSellLinkParams {
  // the subscription def
  moego.models.membership.v1.SellLinkCreateDef sell_link_def = 1 [(validate.rules).message = {required: true}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// create subscription result
message CreateSellLinkResult {
  // the link public token
  string public_token = 1;
}

// create card params
message CreateCardParams {
  // the sell link public token
  string public_token = 1 [(validate.rules).string = {
    min_len: 20
    max_len: 100
  }];
  // use new card with token,
  // will always save card on file for next charge.
  string external_card_token = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// create card result
message CreateCardResult {
  // the card
  moego.models.payment.v1.CreditCardModelPublicView card = 1;
}

// create subscription params
message SellMembershipParams {
  // the subscription def
  moego.models.membership.v1.SubscriptionCreateDef subscription_def = 1 [(validate.rules).message = {required: true}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// create subscription result
message SellMembershipResult {
  // the created subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
  // client secret token
  string external_client_secret_id = 2;
}

// get subscription params
message GetSubscriptionDetailParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get subscription result
// TODO(junbao): optimize subscription detail model
message GetSubscriptionDetailResult {
  // the subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
  // the membership, latest revision
  moego.models.membership.v1.MembershipModel membership = 2;
  // card info
  models.payment.v1.CreditCardModelPublicView card = 3;
}

// update subscription params
message UpdateSubscriptionParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the subscription def
  moego.models.membership.v1.SubscriptionUpdateDef subscription_def = 2 [(validate.rules).message = {required: true}];
}

// update subscription result
message UpdateSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// list subscription params
message ListSubscriptionsParams {
  // filter by customer id
  optional int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter by status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list subscription result
message ListSubscriptionsResult {
  // the subscriptions
  repeated moego.models.membership.v1.MembershipSubscriptionModel membership_subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// cancel subscription params
message CancelSubscriptionParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // 退款策略，默认不退款
  moego.service.subscription.v1.CancelSubscriptionRequest.RefundPolicy refund_policy = 2;
  // 取消策略，默认到期取消
  moego.service.subscription.v1.CancelSubscriptionRequest.CancelPolicy cancel_policy = 3;
}

// cancel subscription result
message CancelSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// renew subscription params
message RenewSubscriptionParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // required for cancelled subscription
  optional string card_on_file_id = 2;
}

// renew subscription result
message RenewSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
  // client secret token
  string external_client_secret_id = 2;
}

// pause subscription params
message PauseSubscriptionParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // auto resume setting
  moego.models.membership.v1.SubscriptionModel.AutoResumeSetting auto_resume_setting = 2;
}

// pause subscription result
message PauseSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// resume subscription params
message ResumeSubscriptionParams {
  // the subscription id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// resume subscription result
message ResumeSubscriptionResult {
  // the updated subscription
  moego.models.membership.v1.SubscriptionModel subscription = 1;
}

// list membership buyers params
message ListBuyersParams {
  // the membership id
  int64 membership_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  optional moego.models.membership.v1.SubscriptionBuyerListFilter filter = 2;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list membership buyers result
message ListBuyersResult {
  // the buyers
  repeated moego.models.membership.v1.SubscriptionBuyerView buyers = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// get membership buyer report params
message GetBuyerReportParams {
  // the membership id
  int64 membership_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get membership buyer report result
message GetBuyerReportResult {
  // the report
  moego.models.subscription.v1.Report content = 1;
}

// list payment history params
message ListPaymentHistoryParams {
  // filter
  moego.models.membership.v1.PaymentHistoryItemFilter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// list payment history result
message ListPaymentHistoryHistoryResult {
  // the payment history
  repeated models.membership.v1.PaymentHistoryItemView payment_history = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
