// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.ai_assistant.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/ai_assistant/v1/business_conversation_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1;aiassistantpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ai_assistant.v1";

// BusinessConversation model
message BusinessConversationModel {
  // conversation id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // creator id (staff id)
  int64 creator_id = 3;
  // scenario
  string scenario = 4;
  // cost
  double cost = 5;
  // total input token
  int32 total_input_token = 13;
  // total output token
  int32 total_output_token = 14;
  // total token
  int32 total_token = 15;
  // question count
  int32 question_count = 16;
  // adopted question id
  int64 adopted_question_id = 6;
  // prompt
  string prompt = 7;
  // trailer prompt
  string trailer_prompt = 8;
  // temperature
  double temperature = 9;
  // status
  BusinessConversationStatus status = 10;
  // create time
  google.protobuf.Timestamp created_at = 11;
  // update time
  google.protobuf.Timestamp updated_at = 12;
}

// BusinessConversationQuestion model
message BusinessConversationQuestionModel {
  // question id
  int64 id = 1;
  // conversation id
  int64 conversation_id = 2;
  // sender id (staff id)
  int64 sender_id = 3;
  // is adopted
  bool adopted = 4;
  // cost
  double cost = 5;
  // input token
  int32 input_token = 13;
  // output token
  int32 output_token = 14;
  // token
  int32 token = 15;
  // question
  string question = 6;
  // answer
  string answer = 7;
  // status
  BusinessConversationQuestionStatus status = 8;
  // send time for question
  google.protobuf.Timestamp sent_at = 9;
  // answer time for answer
  google.protobuf.Timestamp answered_at = 10;
  // create time
  google.protobuf.Timestamp created_at = 11;
  // update time
  google.protobuf.Timestamp updated_at = 12;
}

// business conversation summary model
message BusinessConversationSummaryModel {
  // business id
  int64 business_id = 1;

  // conversation count
  int32 conversation_count = 2;

  // nonempty conversation count
  int32 nonempty_conversation_count = 10;

  // question count
  int32 question_count = 3;

  // adopted conversation count
  int32 adopted_conversation_count = 4;

  // total cost
  double total_cost = 5;

  // total input token
  int32 total_input_token = 6;

  // total output token
  int32 total_output_token = 7;

  // total token
  int32 total_token = 8;

  // last conversation created at
  google.protobuf.Timestamp last_conversation_created_at = 9;
}
