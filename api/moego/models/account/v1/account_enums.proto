syntax = "proto3";

package moego.models.account.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account status
enum AccountStatus {
  // unspecified
  ACCOUNT_STATUS_UNSPECIFIED = 0;
  // active
  ACCOUNT_STATUS_ACTIVE = 1;
  // deleted
  ACCOUNT_STATUS_DELETED = 2;
  // frozen
  ACCOUNT_STATUS_FROZEN = 3;
}

// account namespace type
enum AccountNamespaceType {
  // unspecified
  ACCOUNT_NAMESPACE_TYPE_UNSPECIFIED = 0;
  // moego
  MOEGO = 1;
  // enterprise
  ENTERPRISE = 2;
  // company
  COMPANY = 3;
}
