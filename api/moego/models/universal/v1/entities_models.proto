// @since 2022-05-30 18:04:52
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.universal.v1;

import "moego/models/todo/v1/todo_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/universal/v1;universalpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.universal.v1";

// the universal entities response
message EntityListModel {
  option deprecated = true;
  // size of the page
  int64 size = 1;
  // total of the list
  int64 total = 2;
  // the current page
  int64 page = 3;
  // the next start id
  int64 next_start = 4;
  // the next page unique id
  // only one of page/next_start/next should be set
  string next = 5;

  // the following is the entities list
  // todos
  repeated moego.models.todo.v1.TodoModel todo_list = 8;
}
