// @since 2024-06-03 22:06:04
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The AutoRolloverRule model
message AutoRolloverRuleModel {
  // auto rollover enabled
  bool enabled = 1;
  // auto rollover after x minutes
  int32 after_minute = 2;
  // auto rollover target service id
  int64 target_service_id = 3;
  // service id
  int64 service_id = 4;
}
