syntax = "proto3";

package moego.service.user_profile.v1;

import "moego/models/user_profile/v1/user_profile_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/user_profile/v1;userprofilesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.user_profile.v1";

// UserProfileService 用户画像服务
service UserProfileService {
  // GetUserProfile 获取用户画像
  rpc GetUserProfiles(GetUserProfilesRequest) returns (GetUserProfilesResponse);
}

// GetUserProfileRequest 获取用户画像请求
message GetUserProfilesRequest {
  // 用户
  repeated moego.models.user_profile.v1.User users = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
  }];
}

// GetUserProfileResponse 获取用户画像响应
message GetUserProfilesResponse {
  // 用户画像
  repeated models.user_profile.v1.UserProfile user_profiles = 1;
}
