syntax = "proto3";

package moego.service.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/customer/v1/customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_vaccine_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/customer/v1;customersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.customer.v1";

// pet vaccine input
message PetVaccineInput {
  // vaccine metadata id
  int32 vaccine_metadata_id = 1 [(validate.rules).int32 = {gt: 0}];
  // expiration date
  string expiration_date = 2 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // document url list
  repeated string document_url_list = 3 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      string: {
        uri: true
        max_len: 1000
      }
    }
  }];
}

// pet input
message PetInput {
  // pet name
  string pet_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // pet type
  moego.models.customer.v1.PetType pet_type = 2 [(validate.rules).enum = {defined_only: true}];
  // avatar path
  string avatar_path = 3 [(validate.rules).string = {
    ignore_empty: true
    max_len: 255
    uri: true
  }];
  // breed id
  int32 breed_id = 4 [(validate.rules).int32 = {gt: 0}];
  // breed mix
  bool breed_mix = 5;
  // birthday
  string birthday = 6 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // gender
  moego.models.customer.v1.PetGender gender = 7 [(validate.rules).enum = {defined_only: true}];
  // hair length metadata id
  int32 hair_length_metadata_id = 8 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // behavior metadata id
  int32 behavior_metadata_id = 9 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // weight
  string weight = 10 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // weight unit metadata id
  int32 weight_unit_metadata_id = 12 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // fixed metadata id
  int32 fixed_metadata_id = 13 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
}

// add pet input
message AddPetInput {
  // pet input
  PetInput pet = 1 [(validate.rules).message = {required: true}];
  // vaccine list
  repeated PetVaccineInput vaccine_list = 2;
  // account id
  int64 account_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// update pet input
message UpdatePetInput {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet input
  PetInput pet = 2 [(validate.rules).message = {required: true}];
  // vaccine list
  repeated PetVaccineInput vaccine_list = 3;
  // account id
  int64 account_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get pet list output
message GetPetListOutput {
  // pet list
  repeated GetPetOutput pet_list = 1;
}

// get pet output
message GetPetOutput {
  // pet model
  moego.models.customer.v1.CustomerPetModel pet_model = 1;
  // pet vaccine list
  repeated moego.models.customer.v1.PetVaccineSimpleView vaccine_list = 2;
}

// get pet list input
message GetPetListInput {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete pet input
message DeletePetInput {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete pet input
message GetPetInput {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// init pet input list
message InitPetInputList {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // init pet input
  repeated InitPetInput init_pet_input = 2;
}

// init pet input
message InitPetInput {
  // pet
  InitPetInputPet pet = 1;
  // vaccine
  repeated InitPetInputVaccine vaccine = 2;
}

// init pet input pet
message InitPetInputPet {
  // pet name
  string pet_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // pet type id
  int32 pet_type_id = 2 [(validate.rules).int32 = {gt: 0}];
  // avatar path
  string avatar_path = 3 [(validate.rules).string = {
    ignore_empty: true
    max_len: 255
    uri: true
  }];
  // breed name
  string breed = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // breed mix
  int32 breed_mix = 5 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // birthday
  string birthday = 6 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // gender
  int32 gender = 7 [(validate.rules).int32 = {gte: 0}];
  // hair length
  string hair_length = 8 [(validate.rules).string = {
    ignore_empty: true
    max_len: 50
  }];
  // behavior
  string behavior = 9 [(validate.rules).string = {
    ignore_empty: true
    max_len: 50
  }];
  // weight
  string weight = 10 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // fixed
  string fixed = 13 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // id
  int32 id = 14 [(validate.rules).int32 = {gt: 0}];
  // weight unit
  string weight_unit = 15 [(validate.rules).string = {
    ignore_empty: true
    max_len: 50
  }];
}

// init pet input vaccine
message InitPetInputVaccine {
  // vaccine metadata id
  string vaccine_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // expiration date
  string expiration_date = 2 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // document url list
  repeated string document_url_list = 3 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      string: {
        uri: true
        max_len: 1000
      }
    }
  }];
}

// init pet output list
message InitPetOutputList {
  // init pet list
  repeated InitPetOutput binding_pet_list = 1;
}

// init pet output
message InitPetOutput {
  // old pet id
  int32 old_pet_id = 1;
  // new pet id
  int64 new_pet_id = 2;
}

// link pet request
message LinkPetRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // init pet input
  repeated InitPetInput init_pet_input = 2;
}

// link pet response
message LinkPetResponse {}

// pet service
service PetService {
  // get pet list
  rpc GetPetList(GetPetListInput) returns (GetPetListOutput);
  // add pet
  rpc AddPet(AddPetInput) returns (moego.utils.v1.Id);
  // update pet
  rpc UpdatePet(UpdatePetInput) returns (google.protobuf.Empty);
  // delete pet
  rpc DeletePet(DeletePetInput) returns (google.protobuf.Empty);
  // get pet
  rpc GetPet(GetPetInput) returns (GetPetOutput);
  // init pet
  rpc InitPet(InitPetInputList) returns (InitPetOutputList);

  // link pet, can be called repeatedly
  // Match based on pet_name + breed as unique identifier
  // If the C-end does not exist, add it and link
  // If the C-end exists, link directly
  rpc LinkPet(LinkPetRequest) returns (LinkPetResponse);
}
