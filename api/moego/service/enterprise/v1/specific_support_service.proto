syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/enterprise/v1/specific_support_models.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// The SpecificSupportService service
// 用来放所有 T1 商家的定制化需求接口
service SpecificSupportService {
  // Get company message cycles
  rpc GetCompanyMessageCycles(GetCompanyMessageCyclesRequest) returns (GetCompanyMessageCyclesResponse);
  // Check customers in message list
  rpc CheckCustomersInMessageList(CheckCustomersInMessageListRequest) returns (CheckCustomersInMessageListResponse);
  // Get customers in message list
  rpc GetCustomersInMessageList(GetCustomersInMessageListRequest) returns (GetCustomersInMessageListResponse);
  // Export non standard pet breed
  rpc ExportNonStandardPetBreed(ExportNonStandardPetBreedRequest) returns (ExportNonStandardPetBreedResponse);
  // Import pet code binding
  rpc ImportPetCodeBinding(ImportPetCodeBindingRequest) returns (ImportPetCodeBindingResponse);
  // Check special evaluation
  rpc CheckSpecialEvaluation(CheckSpecialEvaluationRequest) returns (CheckSpecialEvaluationResponse);
  // list applicable line items
  rpc ListApplicableLineItems(ListApplicableLineItemsRequest) returns (ListApplicableLineItemsResponse);
  // start vet verify task job
  // collect customers who have appointments in the next 30 days.
  rpc StartVetVerifyTaskJob(StartVetVerifyTaskJobRequest) returns (StartVetVerifyTaskJobResponse);
}

// GetCompanyMessageCyclesRequest
message GetCompanyMessageCyclesRequest {
  // the company id
  repeated int64 company_ids = 1;
}

// GetCompanyMessageCyclesResponse
message GetCompanyMessageCyclesResponse {
  // the company message cycles
  repeated moego.models.enterprise.v1.MessageCycle message_cycles = 1;
}

// CheckCustomersInMessageListRequest
message CheckCustomersInMessageListRequest {
  // the customer id
  repeated int64 customer_ids = 1;
}

// CheckCustomersInMessageListResponse
message CheckCustomersInMessageListResponse {
  // the customers in message list
  map<int64, bool> in_message_list = 1;
}

// GetCustomersInMessageListRequest
message GetCustomersInMessageListRequest {
  // the company ids
  repeated int64 company_ids = 1;
}

// GetCustomersInMessageListResponse
message GetCustomersInMessageListResponse {
  // binding
  message Binding {
    // the customer
    moego.models.business_customer.v1.BusinessCustomerModel customer = 1;
    // the tags
    repeated string tags = 2;
    // the last appointment
    moego.models.appointment.v1.AppointmentModel last_appointment = 3;
  }
  // the customers in message list
  repeated Binding bindings = 1;
}

// ExportNonStandardPetBreedRequest
message ExportNonStandardPetBreedRequest {
  // the enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the pet type
  moego.models.customer.v1.PetType pet_type = 2;
  // standard file url
  string standard_file_url = 3;
}

// ExportNonStandardPetBreedResponse
message ExportNonStandardPetBreedResponse {
  // the export result
  string file_url = 1;
}

// ImportPetCodeBindingRequest
message ImportPetCodeBindingRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the file url
  string file_url = 2;
}

// ImportPetCodeBindingResponse
message ImportPetCodeBindingResponse {
  // skip pet name, client name, pet breed
  message Skip {
    // the pet name
    string pet_name = 1;
    // the client name
    string client_name = 2;
    // the pet breed
    string pet_breed = 3;
  }
  // the import result
  repeated Skip skips = 1;
}

// CheckSpecialEvaluationRequest
message CheckSpecialEvaluationRequest {
  // service ids
  message ServiceIDs {
    // service ids
    repeated int64 service_ids = 1;
  }
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the pet service ids
  map<int64, ServiceIDs> pet_service_ids = 2;
}

// CheckSpecialEvaluationResponse
message CheckSpecialEvaluationResponse {
  // Result
  message Result {
    // pet id
    int64 pet_id = 1;
    // service id
    int64 service_id = 2;
    // evaluation requirement
    EvaluationRequirement evaluation_requirement = 3;
    // need evaluation
    bool need_evaluation = 4;
  }
  // Evaluation Requirement
  enum EvaluationRequirement {
    // unspecified
    REQUIREMENT_UNSPECIFIED = 0;
    // none
    NONE = 1;
    // basic
    BASIC = 2;
    // special
    SPECIAL = 3;
  }
  // pet service evaluation check results
  repeated Result results = 1;
}

// ListApplicableLineItemsRequest
message ListApplicableLineItemsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // discount id
  int64 discount_id = 2;
  // order id
  int64 order_id = 3;
}

// ListApplicableLineItemsResponse
message ListApplicableLineItemsResponse {
  // Order Line Item Model
  repeated models.order.v1.OrderLineItemModel order_line_items = 1;
  // Business Customer Pet Info Model
  repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 2;
}

// start vet verify task job request
message StartVetVerifyTaskJobRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // vet verify id
  string vet_verify_id = 2 [(validate.rules).string = {min_len: 1}];
}

// start vet verify task job response
message StartVetVerifyTaskJobResponse {
  // task id
  int64 task_id = 1;
  // task name
  string task_name = 2;
}
