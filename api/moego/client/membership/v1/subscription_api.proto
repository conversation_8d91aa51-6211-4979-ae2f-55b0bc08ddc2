// @since 2024-07-23 18:23:57
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.client.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/sell_link_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/organization/v1/tax_models.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "moego/models/payment/v1/us_bank_account_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.membership.v1";

// create subscription params
message GetSellLinkPublicInfoParams {
  // the sell link public token
  string public_token = 1 [(validate.rules).string = {
    min_len: 20
    max_len: 100
  }];
}

// create subscription result
message GetSellLinkPublicInfoResult {
  // the sell link info
  moego.models.membership.v1.SellLinkModel sell_link = 8;
  // company preference
  moego.models.organization.v1.CompanyPreferenceSettingModel company_preference = 1;
  // location info
  moego.models.organization.v1.LocationModelPublicView business = 2;
  // membership
  moego.models.membership.v1.MembershipModelPublicView membership = 3;
  // tax
  moego.models.organization.v1.TaxRuleModelPublicView tax = 7;
  // customer info
  moego.models.business_customer.v1.BusinessCustomerModelPublicView customer = 4;
  // customer card on files
  repeated moego.models.payment.v1.CreditCardModelPublicView customer_cards = 5;
  // customer subscription if created
  optional moego.models.membership.v1.SubscriptionModelPublicView subscription = 6;
  // us bank account/ach
  repeated moego.models.payment.v1.UsBankAccountModel us_bank_accounts = 9;
}

// create subscription params
message BuyMembershipParams {
  // the sell link public token
  string public_token = 1 [(validate.rules).string = {
    min_len: 20
    max_len: 100
  }];
  // the payment method
  oneof payment_method {
    option (validate.required) = true;
    // use existing cof id
    string external_card_id = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
    // use new card with token,
    // will always save card on file for next charge.
    string external_card_token = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
    // use existing ach id
    string external_ach_id = 5 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
    // use ach payment method,
    // will always save ach for next charge.
    string external_ach_token = 6 [(validate.rules).string = {
      min_len: 1
      max_len: 255
    }];
  }
  // restrict the membership revision
  optional int32 membership_revision = 4;
  // 支持创建未来的预约
  google.protobuf.Timestamp start_at = 7;
  // source
  Source source = 8;
  // source
  enum Source {
    // default source
    SOURCE_UNSPECIFIED = 0;
    // branded app
    BRANDED_APP = 1;
  }
}

// create subscription result
message BuyMembershipResult {
  // customer subscription
  moego.models.membership.v1.SubscriptionModelPublicView subscription = 1;
  // client secret token
  string external_client_secret_id = 2;
}

// get buy result params
message GetBuyResultParams {
  // the sell link public token
  string public_token = 1 [(validate.rules).string = {
    min_len: 20
    max_len: 100
  }];
}

// get buy result result
message GetBuyResultResult {
  // the subscription
  optional moego.models.membership.v1.SubscriptionModelPublicView subscription = 1;
}

// list subscription params
message ListSubscriptionsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // filter by status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// list subscription result
message ListSubscriptionsResult {
  // the subscriptions
  repeated moego.models.membership.v1.MembershipSubscriptionModel membership_subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// list subscription params
message ListSubscriptionsForAppParams {
  // online booking name and domain
  int64 company_id = 1;
  // filter by status
  repeated moego.models.membership.v1.SubscriptionModel.Status status_in = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// list subscription result
message ListSubscriptionsForAppResult {
  // the subscriptions
  repeated moego.models.membership.v1.MembershipSubscriptionModel membership_subscriptions = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// ListPaymentHistoryParams
message ListPaymentHistoryParams {
  // PaymentHistoryItemFilter
  message PaymentHistoryItemFilter {
    // the subscription id
    int64 subscription_id = 1;
    // company id
    int64 company_id = 2;
    // business id
    int64 business_id = 3;
    // sort by
    optional models.membership.v1.PaymentHistoryItemFilter.Key sort_by = 4;
    // sort order
    optional bool is_desc = 5;
  }
  // filter
  PaymentHistoryItemFilter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// ListPaymentHistoryParams
message ListPaymentHistoryResult {
  // the payment history
  repeated models.membership.v1.PaymentHistoryItemView payment_history = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// Create OBRequest Param
message CreateOBRequestSettingParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // subscription id
  int64 subscription_id = 3 [(validate.rules).int64 = {gt: 0}];
  // daya of week
  repeated google.type.DayOfWeek days_of_week = 4;
  // pet id
  int64 pet_id = 5 [(validate.rules).int64 = {gte: 0}];
}

// Create OBRequest Result
message CreateOBRequestSettingResult {
  // ob request setting
  moego.models.membership.v1.OBRequestSettingModel ob_request_setting = 1;
}

// create ob request
message GetOBRequestSettingParams {
  // subscription id
  int64 subscription_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id, 兼容 c app 接口无法通过 auth context 获取company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// GetOBRequestResult
message GetOBRequestSettingResult {
  // ob request setting
  optional moego.models.membership.v1.OBRequestSettingModel ob_request_setting = 1;
}

// the subscription service
service SubscriptionService {
  // get sell link information
  rpc GetSellLinkPublicInfo(GetSellLinkPublicInfoParams) returns (GetSellLinkPublicInfoResult);
  // buy membership
  rpc BuyMembership(BuyMembershipParams) returns (BuyMembershipResult);
  // get subscription by sell link
  rpc GetBuyResult(GetBuyResultParams) returns (GetBuyResultResult);
  // list subscriptions
  rpc ListSubscriptions(ListSubscriptionsParams) returns (ListSubscriptionsResult);

  // list subscriptions for app
  rpc ListSubscriptionsForApp(ListSubscriptionsForAppParams) returns (ListSubscriptionsForAppResult);
  // list payment history
  rpc ListPaymentHistory(ListPaymentHistoryParams) returns (ListPaymentHistoryResult);
  // Create OB Request setting
  rpc CreateOBRequestSetting(CreateOBRequestSettingParams) returns (CreateOBRequestSettingResult);
  // Get OB Request setting
  rpc GetOBRequestSetting(GetOBRequestSettingParams) returns (GetOBRequestSettingResult);
}
