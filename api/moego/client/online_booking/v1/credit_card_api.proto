syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/api/subscription/v1/subscription_api.proto";
import "moego/models/payment/v1/credit_card_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// CreditCardService, all for existing client
service CreditCardService {
  // add credit card
  rpc AddCreditCard(AddCreditCardParams) returns (AddCreditCardResult);
  // delete credit card
  rpc DeleteCreditCard(DeleteCreditCardParams) returns (DeleteCreditCardResult);
  // list credit cards
  rpc ListCreditCards(ListCreditCardsParams) returns (ListCreditCardsResult);
  // get credit
  rpc GetCredit(GetCreditParams) returns (GetCreditResult);
  // list history
  rpc ListCreditChangeHistory(ListCreditChangeHistoryParams) returns (ListCreditChangeHistoryResult);
}

// add credit card param
message AddCreditCardParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // charge token
  string charge_token = 3 [(validate.rules).string = {min_len: 1}];
}

// add credit card result
message AddCreditCardResult {
  // credit card
  moego.models.payment.v1.CreditCardModelPublicView card = 1;
}

// delete credit card param
message DeleteCreditCardParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // card id
  string card_id = 3 [(validate.rules).string = {min_len: 1}];
}

// delete credit card result
message DeleteCreditCardResult {}

// list credit cards param
message ListCreditCardsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// list credit cards result
message ListCreditCardsResult {
  // credit card list
  repeated moego.models.payment.v1.CreditCardModelPublicView cards = 1;
}

// GetCreditParams
message GetCreditParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// GetCreditResult
message GetCreditResult {
  // credit, the unit is cents
  int64 credit = 1;
}

// list credit history change history
message ListCreditChangeHistoryParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // page
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list credit change history result
message ListCreditChangeHistoryResult {
  // change history list
  repeated api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory histories = 1;
  // page
  moego.utils.v2.PaginationResponse pagination = 2;
}
