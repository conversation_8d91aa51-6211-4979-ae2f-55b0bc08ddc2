spring:
  application:
    name: moego-svc-appointment
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
    hikari:
      max-lifetime: 600000
  additional-datasources:
    - name: postgres
      driver-class-name: org.postgresql.Driver
      url: ***************************************************************
      username: moego_developer_240310_eff7a0dc
      password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  activemq:
    broker-url: ssl://b-f80a5cc9-31ab-4781-8914-ecb9fe93c94a-1.mq.us-west-2.amazonaws.com:61617
    password: +S+22Y8FlzqazglDiXbwIA
    user: moego-active-mq-dev-v1-master
    enabled: true
  data:
    redis:
      host: redis.t2.moego.dev
      password: iMoReGoTdesstingeCache250310_7fec987d
      port: 40179
      ssl:
        enabled: false
      timeout: 60000
      key:
        delimiter: ':'
        prefix: local

logging:
  level:
    com.moego.svc.appointment.mapper: DEBUG
moego:
  messaging:
    enabled: true
    pulsar:
      service-url: pulsar.t2.moego.dev:40650
      authentication: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      tenant: test2
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka.kafka.svc.cluster.local:9092
    producer:
      # 如果 enabled 为 false (默认值), 则不会初始化 producer, 此时如果代码里依赖了 producer, 则会抛出异常
      enabled: true
      # 发送消息成功时是否打印日志, 默认为 false
      log-success: false
      # 发送消息失败时是否打印日志, 默认为 true
      log-failure: true
    consumer:
      enabled: false
  grpc:
    server:
      port: 9090
      observability:
        metrics:
          port: 0
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
