package com.moego.svc.appointment.dto;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.svc.appointment.domain.AppointmentPetFeeding;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentPetScheduleSetting;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Data
@Accessors(chain = true)
public class PetDetailDTO {

    private Long petId;
    private MoeGroomingPetDetail petDetail;
    private List<MoeGroomingServiceOperation> operations;
    private List<PetFeedingScheduleDTO> feedings;
    private List<PetMedicationScheduleDTO> medications;
    private CustomizedServiceView service;
    private List<BoardingSplitLodging> splitLodgings;

    @Data
    @Accessors(chain = true)
    public static class PetFeedingScheduleDTO {
        private AppointmentPetFeeding feeding;
        private List<AppointmentPetScheduleSetting> scheduleSettings;
    }

    @Data
    @Accessors(chain = true)
    public static class PetMedicationScheduleDTO {
        private AppointmentPetMedication medication;
        private List<AppointmentPetScheduleSetting> scheduleSettings;
    }
}
