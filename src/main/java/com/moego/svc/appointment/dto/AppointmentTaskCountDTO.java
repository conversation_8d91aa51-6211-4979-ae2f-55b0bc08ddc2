package com.moego.svc.appointment.dto;

import com.moego.idl.service.appointment.v1.CountAppointmentTasksRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/12/5
 */
@Data
@Accessors(chain = true)
public class AppointmentTaskCountDTO {

    private long companyId;

    private long businessId;

    private CountAppointmentTasksRequest.Filter filter;

    private String timeZoneName;
}
