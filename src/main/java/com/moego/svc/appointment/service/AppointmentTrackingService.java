package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.id;
import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingDynamicSqlSupport.appointmentTracking;
import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingMapper.updateSelectiveColumns;
import static org.mybatis.dynamic.sql.SqlBuilder.count;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.appointment.v1.StaffLocationStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.appointment.client.CustomerClient;
import com.moego.svc.appointment.converter.AppointmentTrackingConverter;
import com.moego.svc.appointment.domain.AppointmentTracking;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.mapper.pg.AppointmentTrackingMapper;
import com.moego.svc.appointment.service.params.ListAppointmentTrackingFilter;
import com.moego.svc.appointment.service.params.UpdateAppointmentTrackingFilter;
import com.moego.svc.appointment.utils.PageInfo;
import com.moego.svc.appointment.utils.Pair;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentTrackingService {
    private final AppointmentTrackingMapper appointmentTrackingMapper;
    private final CustomerClient customerClient;
    private final AppointmentServiceProxy appointmentService;
    // redis cache key format
    private static final String APPOINTMENT_TRACKING_LATEST_ARRIVAL_FORMAT = "appointment_tracking_latest_arrival_%d";
    private final RedisUtil redisUtil;

    public Pair<List<AppointmentTracking>, PageInfo> list(ListAppointmentTrackingFilter filter, PageInfo pageInfo) {
        var selectStatementBuilder = select(appointmentTracking.allColumns())
                .from(appointmentTracking)
                .where(id, isGreaterThan(0))
                .and(appointmentTracking.appointmentId, isInWhenPresent(filter.getAppointmentIds()))
                .and(appointmentTracking.staffLocationStatus, isInWhenPresent(filter.getStaffLocationStatuses()))
                .and(appointmentTracking.locationSharingStaffId, isInWhenPresent(filter.getLocationSharingStaffIds()));
        if (pageInfo != null) {
            selectStatementBuilder
                    .limit(pageInfo.pageSize())
                    .offset((long) pageInfo.pageSize() * (pageInfo.pageNum() - 1));
        }
        var selectStatement = selectStatementBuilder.build().render(RenderingStrategies.MYBATIS3);

        var countTableStatement = select(count(id))
                .from(appointmentTracking)
                .where(id, isGreaterThan(0))
                .and(appointmentTracking.appointmentId, isInWhenPresent(filter.getAppointmentIds()))
                .and(appointmentTracking.staffLocationStatus, isInWhenPresent(filter.getStaffLocationStatuses()))
                .and(appointmentTracking.locationSharingStaffId, isInWhenPresent(filter.getLocationSharingStaffIds()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        var count = (int) appointmentTrackingMapper.count(countTableStatement);
        PageInfo resultPageInfo;
        if (pageInfo != null) {
            resultPageInfo = new PageInfo(pageInfo.pageNum(), pageInfo.pageSize(), count);
        } else {
            resultPageInfo = new PageInfo(1, count, count);
        }

        return new Pair<>(appointmentTrackingMapper.selectMany(selectStatement), resultPageInfo);
    }

    public AppointmentTracking updateAppointmentTrackingByAppointmentId(AppointmentTracking updateRecord) {
        if (updateRecord.getAppointmentId() == null) {
            throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "appointmentId is required");
        }
        var existRecord = getOrInit(updateRecord.getAppointmentId());
        if (updateRecord.getEstimatedTravelSeconds() != null) {
            updateRecord.setLastEstimateAt(System.currentTimeMillis() / 1000);
        }
        if (updateRecord.getEstimatedTravelSecondsFromLastInTransit() != null) {
            updateRecord.setFromLastInTransitLastEstimateAt(System.currentTimeMillis() / 1000);
        }
        if (Objects.equals(StaffLocationStatus.IN_TRANSIT_VALUE, updateRecord.getStaffLocationStatus())
                && !existRecord.getStaffLocationStatus().equals(StaffLocationStatus.IN_TRANSIT_VALUE)) {
            // 重新打开位置共享，清空 redis 缓存
            redisUtil.delete(
                    String.format(APPOINTMENT_TRACKING_LATEST_ARRIVAL_FORMAT, updateRecord.getAppointmentId()));
        }

        appointmentTrackingMapper.update(c -> updateSelectiveColumns(updateRecord, c)
                .where(appointmentTracking.appointmentId, isEqualTo(updateRecord.getAppointmentId())));
        return appointmentTrackingMapper
                .selectOne(c -> c.where(appointmentTracking.appointmentId, isEqualTo(updateRecord.getAppointmentId())))
                .orElse(null);
    }

    public void create(AppointmentTracking record) {
        if (record.getAppointmentId() == null) {
            throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "appointmentId is required");
        }
        if (record.getCompanyId() == null) {
            throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "companyId is required");
        }
        try {
            appointmentTrackingMapper.insertSelective(record);
        } catch (DuplicateKeyException e) {
            // 并发插入时可能会导致重复 create, 不影响结果, 忽略异常
            log.warn("appointment tracking record already exists, appointmentId: {}", record.getAppointmentId());
        }
    }

    public AppointmentTracking getOrInit(Long appointmentId) {
        var existRecord = appointmentTrackingMapper
                .selectOne(c -> c.where(appointmentTracking.appointmentId, isEqualTo(appointmentId)))
                .orElse(null);
        if (existRecord == null) {
            var appointment = appointmentService.getAppointment(null, appointmentId);
            var address = AppointmentTrackingConverter.INSTANCE.toAddress(
                    customerClient.getPrimaryAddress(appointment.getCustomerId()));
            var newRecord = new AppointmentTracking();
            newRecord.setAppointmentId(appointmentId);
            newRecord.setCustomerAddress(address);
            newRecord.setCompanyId(appointment.getCompanyId());
            create(newRecord);
        }
        return appointmentTrackingMapper
                .selectOne(c -> c.where(appointmentTracking.appointmentId, isEqualTo(appointmentId)))
                .orElse(null);
    }

    public List<AppointmentTracking> batchUpdateOrCreate(
            Long companyId,
            List<Long> appointmentIds,
            UpdateAppointmentTrackingFilter filter,
            AppointmentTracking updateRecord,
            boolean needCreate) {
        if (appointmentIds.isEmpty()) {
            return List.of();
        }
        // 初始化
        if (needCreate) {
            batchGetOrInit(companyId, appointmentIds);
        }
        appointmentTrackingMapper.update(c -> updateSelectiveColumns(updateRecord, c)
                .where(appointmentTracking.appointmentId, isIn(appointmentIds))
                .and(appointmentTracking.staffLocationStatus, isInWhenPresent(filter.getStaffLocationStatuses()))
                .and(appointmentTracking.locationSharingStaffId, isInWhenPresent(filter.getLocationSharingStaffIds()))
                .and(appointmentTracking.companyId, isEqualTo(companyId)));
        return appointmentTrackingMapper.selectMany(select(appointmentTracking.allColumns())
                .from(appointmentTracking)
                .where(appointmentTracking.appointmentId, isIn(appointmentIds))
                .and(appointmentTracking.companyId, isEqualTo(companyId))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }

    public List<AppointmentTracking> batchGetOrInit(Long companyId, List<Long> appointmentIds) {
        if (appointmentIds.isEmpty()) {
            return List.of();
        }
        var existRecords = appointmentTrackingMapper.selectMany(select(appointmentTracking.allColumns())
                .from(appointmentTracking)
                .where(appointmentTracking.appointmentId, isIn(appointmentIds))
                .and(appointmentTracking.companyId, isEqualTo(companyId))
                .build()
                .render(RenderingStrategies.MYBATIS3));
        var existAppointmentIds =
                existRecords.stream().map(AppointmentTracking::getAppointmentId).toList();
        var newAppointmentIds = appointmentIds.stream()
                .filter(id -> !existAppointmentIds.contains(id))
                .toList();
        if (newAppointmentIds.isEmpty()) {
            return existRecords;
        }
        var appointments = appointmentService.getAppointments(companyId, newAppointmentIds);
        var clientIds = appointments.stream()
                .map(MoeGroomingAppointment::getCustomerId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        var clientIdToAddress = customerClient.batchGetPrimaryAddress(clientIds);
        var newRecords = appointments.stream()
                .map(appointment -> {
                    var address = AppointmentTrackingConverter.INSTANCE.toAddress(
                            clientIdToAddress.get(appointment.getCustomerId().longValue()));
                    var newRecord = new AppointmentTracking();
                    newRecord.setAppointmentId(appointment.getId().longValue());
                    newRecord.setCustomerAddress(address);
                    newRecord.setCompanyId(companyId);
                    return newRecord;
                })
                .toList();
        newRecords.forEach(this::create);
        return appointmentTrackingMapper.selectMany(select(appointmentTracking.allColumns())
                .from(appointmentTracking)
                .where(appointmentTracking.appointmentId, isIn(appointmentIds))
                .and(appointmentTracking.companyId, isEqualTo(companyId))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }
}
