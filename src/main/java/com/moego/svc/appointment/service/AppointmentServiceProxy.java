package com.moego.svc.appointment.service;

import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentForPetsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsForCustomersRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.auth.AuthContext;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.utils.PageInfo;
import com.moego.svc.appointment.utils.Pair;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.AndOrCriteriaGroup;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.springframework.stereotype.Component;

/**
 * AppointmentServiceProxy - 代理类用于白名单和非白名单商家的分流调用
 * 白名单商家使用 fulfillment 远程调用，非白名单商家使用原有的 service 调用
 *
 * <AUTHOR>
 * @since 2025/7/11
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AppointmentServiceProxy {

    private final AppointmentService appointmentService;
    // TODO: 注入 fulfillment 远程服务
    // private final FulfillmentAppointmentService fulfillmentAppointmentService;

    /**
     * 判断是否为白名单商家，使用新的 fulfillment flow
     * 直接从 AuthContext 获取 companyId
     * TODO: 实现具体的白名单判断逻辑
     *
     * @return true if company is in fulfillment flow whitelist
     */
    private boolean isFulfillmentFlow() {
        //        Long companyId = AuthContext.get().companyId();
        //        if (companyId == null) {
        //            log.warn("CompanyId is null in AuthContext, using original service");
        //            return false;
        //        }
        // TODO: 实现白名单判断逻辑
        return false;
    }

    // ==================== 基础方法 ====================

    @Nullable
    public MoeGroomingAppointment get(long id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for get, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.get(id);
    }

    @Nonnull
    public MoeGroomingAppointment mustGet(long id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for mustGet, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.mustGet(id);
    }

    public long insertSelective(MoeGroomingAppointment appointment) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insertSelective, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.insertSelective(appointment);
    }

    public int updateByAppointmentId(MoeGroomingAppointment appointment) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateByAppointmentId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.updateByAppointmentId(appointment);
    }

    public MoeGroomingAppointment selectLastAppointment(Integer businessId, Integer customerId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectLastAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.selectLastAppointment(businessId, customerId);
    }

    public MoeGroomingAppointment selectInProgressAppointment(
            Long companyId, Integer businessId, Integer customerId, Integer petId, ServiceItemType serviceItemType) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for selectInProgressAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.selectInProgressAppointment(
                companyId, businessId, customerId, petId, serviceItemType);
    }

    @Nonnull
    public MoeGroomingAppointment getAppointment(@Nullable Long companyId, Long appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointment(companyId, appointmentId);
    }

    public List<MoeGroomingAppointment> getAppointments(@Nullable Long companyId, List<Long> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointments, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointments(companyId, appointmentIds);
    }

    public int update(MoeGroomingAppointment appointment) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for update, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.update(appointment);
    }

    public int update(List<AndOrCriteriaGroup> cond, UpdateDSL<UpdateModel> dsl, Long appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for update with conditions, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.update(cond, dsl, appointmentId);
    }

    public void batchCheckIn(Set<Long> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchCheckIn, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        appointmentService.batchCheckIn(appointmentIds);
    }

    public Map<Long, MoeGroomingAppointment> getCustomerLastAppointment(
            Long companyId,
            List<Long> customerIdList,
            AppointmentStatus appointmentStatus,
            GetCustomerLastAppointmentRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getCustomerLastAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getCustomerLastAppointment(companyId, customerIdList, appointmentStatus, filter);
    }

    public int refreshAppointmentDateTime(MoeGroomingAppointment appointment) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for refreshAppointmentDateTime, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.refreshAppointmentDateTime(appointment);
    }

    public List<MoeGroomingAppointment> listRepeatAppointment(Integer businessId, Integer repeatId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listRepeatAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listRepeatAppointment(businessId, repeatId);
    }

    public List<MoeGroomingAppointment> listAfterRepeatAppointment(Integer businessId, Integer repeatId, String date) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listAfterRepeatAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listAfterRepeatAppointment(businessId, repeatId, date);
    }

    public List<Integer> listUpcomingAppointment(
            Long companyId, @Nullable Integer businessId, String startDate, Integer startMinutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listUpcomingAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listUpcomingAppointment(companyId, businessId, startDate, startMinutes);
    }

    public List<Integer> listNotStartedAppointmentWithPetService(
            Long companyId, Integer businessId, List<Integer> serviceIds, Integer petId, String startDate) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listNotStartedAppointmentWithPetService, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listNotStartedAppointmentWithPetService(
                companyId, businessId, serviceIds, petId, startDate);
    }

    public List<Integer> listNotEndedAppointmentWithPetService(
            Long companyId, Integer businessId, Integer serviceId, Integer petId, String date, Integer minutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listNotEndedAppointmentWithPetService, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listNotEndedAppointmentWithPetService(
                companyId, businessId, serviceId, petId, date, minutes);
    }

    public long getAppointmentCountByService(Long companyId, Long serviceId, Long businessId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentCountByService, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointmentCountByService(companyId, serviceId, businessId);
    }

    public BigDecimal calculateServicePrice(
            long companyId,
            long businessId,
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for calculateServicePrice, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.calculateServicePrice(companyId, businessId, petDetailDefs, petServiceMap);
    }

    public List<Integer> getAppointmentIdsByStartDateRange(
            Long companyId, Integer businessId, String startDateGte, String endDateLt, List<ServiceItemType> types) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentIdsByStartDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointmentIdsByStartDateRange(
                companyId, businessId, startDateGte, endDateLt, types);
    }

    public List<MoeGroomingAppointment> getAppointmentsByDateRange(
            Long companyId,
            Integer businessId,
            LocalDate startDateGte,
            LocalDate endDateLte,
            List<ServiceItemType> types) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointmentsByDateRange, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointmentsByDateRange(companyId, businessId, startDateGte, endDateLte, types);
    }

    public List<Integer> serviceItemTypesToBitValueList(List<ServiceItemType> serviceItemTypesList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for serviceItemTypesToBitValueList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.serviceItemTypesToBitValueList(serviceItemTypesList);
    }

    public Pair<List<MoeGroomingAppointment>, PageInfo> listAppointments(
            Long companyId,
            List<Long> businessIds,
            ListAppointmentsRequest.Filter filter,
            List<OrderBy> orderBys,
            PageInfo pageInfo,
            ListAppointmentsRequest.PriorityOrderType priorityOrderType) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listAppointments, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listAppointments(
                companyId, businessIds, filter, orderBys, pageInfo, priorityOrderType);
    }

    public List<BlockTimeModel> listBlockTimes(
            Long companyId, List<Long> businessIds, ListBlockTimesRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listBlockTimes, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listBlockTimes(companyId, businessIds, filter);
    }

    public Map<Long, List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>>> listAppointmentsForPet(
            Long companyId, Set<Long> petIds, ListAppointmentForPetsRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listAppointmentsForPet, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listAppointmentsForPet(companyId, petIds, filter);
    }

    public List<MoeGroomingAppointment> listAppointmentsForCustomer(
            Long companyId, List<Long> businessIds, ListAppointmentsForCustomersRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for listAppointmentsForCustomer, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.listAppointmentsForCustomer(companyId, businessIds, filter);
    }

    public Map<Long, Integer> batchGetTotalAppointmentCount(Long companyId, List<Long> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchGetTotalAppointmentCount, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.batchGetTotalAppointmentCount(companyId, customerIds);
    }

    public Map<Long, Integer> batchGetUpcomingCountByEvaluationId(Long companyId, List<Long> evaluationIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchGetUpcomingCountByEvaluationId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.batchGetUpcomingCountByEvaluationId(companyId, evaluationIds);
    }

    public Map<Long, Integer> batchGetUpcomingCountByCustomerId(Long companyId, List<Long> customerIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchGetUpcomingCountByCustomerId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.batchGetUpcomingCountByCustomerId(companyId, customerIds);
    }

    public Map<Long, Integer> countAppointmentForPets(Collection<Long> petIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for countAppointmentForPets, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.countAppointmentForPets(petIds);
    }

    public int deleteAppointments(
            @Nullable Long companyId, @Nullable Integer businessId, Collection<Integer> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deleteAppointments, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.deleteAppointments(companyId, businessId, appointmentIds);
    }

    public int restoreAppointments(
            @Nullable Long companyId, @Nullable Integer businessId, Collection<Integer> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for restoreAppointments, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.restoreAppointments(companyId, businessId, appointmentIds);
    }

    public MoeGroomingAppointment getAppointment(Long appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getAppointment(appointmentId);
    }

    public Map<Long, List<AppointmentModel>> getTimeOverlapAppointmentList(
            long companyId,
            List<Long> customerIdList,
            List<Long> petIdsList,
            GetTimeOverlapAppointmentListRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getTimeOverlapAppointmentList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return appointmentService.getTimeOverlapAppointmentList(companyId, customerIdList, petIdsList, filter);
    }
}
