package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingNotificationLogDynamicSqlSupport.appointmentId;
import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingNotificationLogDynamicSqlSupport.deletedAt;
import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingNotificationLogDynamicSqlSupport.notificationId;
import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingNotificationLogDynamicSqlSupport.notificationType;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.appointment.v1.StaffLocationStatus;
import com.moego.idl.models.notification.v1.AppPushByContentDef;
import com.moego.idl.models.notification.v1.AppPushDef;
import com.moego.idl.models.notification.v1.AppointmentTrackingDef;
import com.moego.idl.models.notification.v1.NotificationExtraDef;
import com.moego.idl.models.notification.v1.NotificationMethod;
import com.moego.idl.models.notification.v1.NotificationSource;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.notification.v1.PushTokenSource;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import com.moego.idl.service.notification.v1.AppPushServiceGrpc;
import com.moego.idl.service.notification.v1.CreateInboxNotificationRequest;
import com.moego.idl.service.notification.v1.ListPushTokenRequest;
import com.moego.idl.service.notification.v1.NotificationServiceGrpc;
import com.moego.server.message.client.IMessageSendClient;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.svc.appointment.constant.AppointmentTrackingNotification;
import com.moego.svc.appointment.converter.AppointmentTrackingConverter;
import com.moego.svc.appointment.domain.AppointmentTracking;
import com.moego.svc.appointment.domain.AppointmentTrackingNotificationLog;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.mapper.pg.AppointmentTrackingNotificationLogMapper;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentTrackingNotificationService {
    private final AppointmentTrackingNotificationLogMapper appointmentTrackingNotificationLogMapper;
    private final NotificationServiceGrpc.NotificationServiceBlockingStub notificationService;
    private final AppPushServiceGrpc.AppPushServiceBlockingStub appPushService;
    private final BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub brandedAppConfigServiceBlockingStub;
    private final IMessageSendClient iMessageSendClient;
    public static final long DELAY_SERIOUSLY_THRESHOLD_SECONDS = 60 * 30;
    private static final int CHANNEL_NOTIFICATION = 1;
    private static final int CHANNEL_SMS = 2;

    public AppointmentTrackingNotificationLog getSuccessLog(long apptId, NotificationType notiType) {
        return appointmentTrackingNotificationLogMapper
                .selectOne(c -> c.where(appointmentId, isEqualTo(apptId))
                        .and(notificationType, isEqualTo(notiType.getNumber()))
                        .and(notificationId, isGreaterThan(0L))
                        .and(deletedAt, isNull()))
                .orElse(null);
    }

    public void insertLog(long companyId, long apptId, NotificationType notiType, long notiId, int channel) {
        AppointmentTrackingNotificationLog log = new AppointmentTrackingNotificationLog();
        log.setAppointmentId(apptId);
        log.setCompanyId(companyId);
        log.setNotificationType(notiType.getNumber());
        log.setNotificationId(notiId);
        log.setChannel(channel);
        appointmentTrackingNotificationLogMapper.insertSelective(log);
    }

    public void sendNotification(
            long companyId, MoeGroomingAppointment appointment, CreateInboxNotificationRequest request) {
        if (request == null) {
            log.error("Notification request is null for appointment {}", appointment.getId());
            return;
        }
        var pushTokens = appPushService
                .listPushToken(ListPushTokenRequest.newBuilder()
                        .setFilter(ListPushTokenRequest.Filter.newBuilder()
                                .addAccountIds(request.getReceiverId())
                                .addSources(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                                .build())
                        .build())
                .getPushTokensList();
        var hasAvailablePushToken = pushTokens.stream()
                .anyMatch(p -> p.getUpdatedAt().getSeconds() > Instant.now().getEpochSecond() - 60 * 60 * 24 * 2);
        var notiId = 0L;
        if (hasAvailablePushToken) {
            log.debug("Sending notification to pet parent {} via app push {}", request.getReceiverId(), request);
            var res = notificationService.createInboxNotification(request);
            notiId = res.getNotificationId();
        } else {
            var response =
                    brandedAppConfigServiceBlockingStub.getBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                            .setBrandedEntity(GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                                    .setBrandedType(AccountNamespaceType.COMPANY)
                                    .setBrandedId(companyId)
                                    .build())
                            .build());
            String url = response.hasConfig() ? response.getConfig().getBootDownloadLink() : null;
            if (Strings.isEmpty(url)) {
                log.error("brand app download link for {} is empty", companyId);
                return;
            }
            var content = AppointmentTrackingNotification.SMS_TEMPLATE
                    .replace("{petParentAppDownloadLink}", url)
                    .replace("{appName}", response.getConfig().getAppName());
            log.debug("Sending notification to pet parent {} via SMS {}", appointment.getCustomerId(), content);
            SendMessagesParams sendMessagesParams = new SendMessagesParams();
            sendMessagesParams.setStaffId(0);
            sendMessagesParams.setMessageBody(content);
            sendMessagesParams.setBusinessId(appointment.getBusinessId());
            sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
            sendMessagesParams.setTargetId(0);
            sendMessagesParams.getCustomer().setCustomerId(appointment.getCustomerId());
            sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
            notiId = iMessageSendClient
                    .sendServicesMessageToCustomerWithResult(sendMessagesParams)
                    .getId();
        }
        insertLog(
                companyId,
                appointment.getId(),
                request.getType(),
                notiId,
                hasAvailablePushToken ? CHANNEL_NOTIFICATION : CHANNEL_SMS);
    }

    public CreateInboxNotificationRequest buildNotificationRequest(
            long linkAccountId,
            CompanyPreferenceSettingModel companyPreferenceSetting,
            AppointmentTracking tracking,
            NotificationType type) {
        var builder = CreateInboxNotificationRequest.newBuilder()
                .setSource(NotificationSource.NOTIFICATION_SOURCE_PLATFORM)
                .setMethod(NotificationMethod.NOTIFICATION_METHOD_PET_PARENT_APP)
                .setSenderId(0L)
                .setExtra(NotificationExtraDef.newBuilder()
                        .setAppointmentTracking(AppointmentTrackingDef.newBuilder()
                                .setTracking(AppointmentTrackingConverter.INSTANCE.toModel(tracking))
                                .setCompanyPreference(companyPreferenceSetting)
                                .build()))
                .setType(type)
                .setReceiverId(linkAccountId);

        long estimatedArrivalTime;
        if (tracking.getStaffLocationStatus().equals(StaffLocationStatus.IN_TRANSIT_VALUE)) {
            estimatedArrivalTime = tracking.getEstimatedTravelSeconds() + tracking.getLastEstimateAt();
        } else {
            estimatedArrivalTime = tracking.getEstimatedTravelSecondsFromLastInTransit()
                    + tracking.getFromLastInTransitLastEstimateAt();
        }
        var zoneId = ZoneId.of(companyPreferenceSetting.getTimeZone().getName());
        ZonedDateTime dateTime = Instant.ofEpochSecond(estimatedArrivalTime).atZone(zoneId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("hh:mm a");
        String formattedTime = dateTime.format(formatter).toLowerCase();

        var appPushBuilder = AppPushDef.newBuilder().setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT);
        switch (type) {
            case NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY:
                appPushBuilder.setFromContent(AppPushByContentDef.newBuilder()
                        .setTitle(AppointmentTrackingNotification.APPOINTMENT_TRACKING_COME_ASAP)
                        .setContent(AppointmentTrackingNotification.APPOINTMENT_TRACKING_PREVIOUS_SERVICE_EXTENDS)
                        .build());
                break;
            case NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY:
                appPushBuilder.setFromContent(AppPushByContentDef.newBuilder()
                        .setTitle(AppointmentTrackingNotification.APPOINTMENT_TRACKING_DELAYED)
                        .setContent(AppointmentTrackingNotification.APPOINTMENT_TRACKING_COMPLETING_PREVIOUS_SERVICE)
                        .build());
                break;
            case NOTIFICATION_TYPE_TRACKING_START:
                appPushBuilder.setFromContent(AppPushByContentDef.newBuilder()
                        .setTitle(AppointmentTrackingNotification.APPOINTMENT_TRACKING_ON_THE_WAY)
                        .setContent(
                                AppointmentTrackingNotification.APPOINTMENT_TRACKING_ESTIMATED_ARRIVAL_TIME.formatted(
                                        formattedTime))
                        .build());
                break;
            case NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY_WHEN_LAST_TRACKING_FINISH_ON_TIME:
                appPushBuilder.setFromContent(AppPushByContentDef.newBuilder()
                        .setTitle(AppointmentTrackingNotification.APPOINTMENT_TRACKING_STUCK_IN_TRAFFIC)
                        .setContent(
                                AppointmentTrackingNotification.APPOINTMENT_TRACKING_ESTIMATED_ARRIVAL_TIME.formatted(
                                        formattedTime))
                        .build());
                break;
            case NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY_WHEN_LAST_TRACKING_FINISH_ON_TIME:
                appPushBuilder.setFromContent(AppPushByContentDef.newBuilder()
                        .setTitle(AppointmentTrackingNotification.APPOINTMENT_TRACKING_CAUGHT_IN_NORMAL_TRAFFIC)
                        .setContent(
                                AppointmentTrackingNotification.APPOINTMENT_TRACKING_ESTIMATED_ARRIVAL_TIME.formatted(
                                        formattedTime))
                        .build());
                break;
            default:
                log.error("Unsupported notification type: {}", type);
                return null;
        }
        return builder.setAppPush(appPushBuilder).build();
    }

    public void processNotification(AppointmentTrackingSyncService.UpdateTask updateTask) {
        if (updateTask.getNotificationRequest() == null || updateTask.getAppointment() == null) {
            return;
        }
        // check if the notification is already sent
        var sendLog = getSuccessLog(
                updateTask.getAppointment().getId(),
                updateTask.getNotificationRequest().getType());
        if (sendLog != null) {
            return;
        }
        sendNotification(
                updateTask.getAppointment().getCompanyId(),
                updateTask.getAppointment(),
                updateTask.getNotificationRequest());
    }
}
