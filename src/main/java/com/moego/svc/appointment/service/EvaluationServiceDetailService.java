package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.constant.AppointmentStatusSet.ACTIVE_STATUS_VALUE_SET;
import static com.moego.svc.appointment.mapper.mysql.EvaluationServiceDetailDynamicSqlSupport.evaluationServiceDetail;
import static com.moego.svc.appointment.mapper.mysql.EvaluationServiceDetailDynamicSqlSupport.petId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.deleteFrom;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isFalse;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.or;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.moego.common.constant.CommonConstant;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.mapper.mysql.EvaluationServiceDetailDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.EvaluationServiceDetailMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class EvaluationServiceDetailService {
    private final EvaluationServiceDetailMapper evaluationMapper;

    public List<EvaluationServiceDetail> buildEvaluationList(List<PetDetailDef> petDetailDefs) {
        return petDetailDefs.stream()
                .flatMap(def -> {
                    long petId = def.getPetId();
                    return def.getEvaluationsList().stream().map(k -> {
                        LocalDateTime startAt = LocalDateTime.of(
                                LocalDate.parse(k.getStartDate()), LocalTime.ofSecondOfDay(k.getStartTime() * 60L));
                        LocalDateTime endAt = startAt.plusMinutes(k.getServiceTime());

                        EvaluationServiceDetail evaluationServiceDetail = new EvaluationServiceDetail();
                        evaluationServiceDetail.setPetId(petId);
                        evaluationServiceDetail.setServiceId(k.getServiceId());
                        evaluationServiceDetail.setServicePrice(BigDecimal.valueOf(k.getServicePrice()));
                        evaluationServiceDetail.setServiceTime(k.getServiceTime());
                        evaluationServiceDetail.setStartDate(startAt.toLocalDate());
                        evaluationServiceDetail.setStartTime(DateUtil.getMinutesOfDay(startAt));
                        evaluationServiceDetail.setEndDate(endAt.toLocalDate());
                        evaluationServiceDetail.setEndTime(DateUtil.getMinutesOfDay(endAt));
                        if (k.hasStaffId()) {
                            evaluationServiceDetail.setStaffId(k.getStaffId());
                        }
                        if (k.hasLodgingId()) {
                            evaluationServiceDetail.setLodgingId(k.getLodgingId());
                        }
                        return evaluationServiceDetail;
                    });
                })
                .toList();
    }

    /**
     * 保存 pet details
     *
     * @param appointment appointment detail
     */
    @Transactional
    public void saveEvaluationDetails(MoeGroomingAppointment appointment, List<EvaluationServiceDetail> details) {
        details.forEach(k -> k.setAppointmentId(appointment.getId().longValue()));
        details.forEach(evaluationMapper::insertSelective);
    }

    public int deleteByPetIds(Long appointmentId, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return 0;
        }
        return evaluationMapper.delete(deleteFrom(evaluationServiceDetail)
                .where(EvaluationServiceDetailDynamicSqlSupport.appointmentId, isEqualTo(appointmentId))
                .and(petId, isIn(petIds))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }

    public int deleteById(Long appointmentId, Long id) {
        return evaluationMapper.delete(deleteFrom(evaluationServiceDetail)
                .where(EvaluationServiceDetailDynamicSqlSupport.appointmentId, isEqualTo(appointmentId))
                .and(EvaluationServiceDetailDynamicSqlSupport.id, isEqualTo(id))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }

    public List<EvaluationServiceDetail> getPetEvaluationList(Long appointmentId) {
        return evaluationMapper.selectMany(select(evaluationServiceDetail.allColumns())
                .from(evaluationServiceDetail)
                .where(EvaluationServiceDetailDynamicSqlSupport.appointmentId, isEqualTo(appointmentId))
                .build()
                .render(RenderingStrategies.MYBATIS3));
    }

    @Nullable
    public EvaluationServiceDetail getPetEvaluation(Long evaluationServiceDetailId) {
        return evaluationMapper
                .selectOne(
                        c -> c.where(EvaluationServiceDetailDynamicSqlSupport.id, isEqualTo(evaluationServiceDetailId)))
                .orElse(null);
    }

    public List<EvaluationServiceDetail> getPetEvaluationList(List<Long> appointmentIdList) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return List.of();
        }

        return evaluationMapper.select(
                c -> c.where(EvaluationServiceDetailDynamicSqlSupport.appointmentId, isIn(appointmentIdList)));
    }

    public List<EvaluationServiceDetail> getPetEvaluationList(
            Long companyId, String date, Integer nowMinutes, List<Long> evaluationIds) {
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return List.of();
        }

        var appointmentSubQuery = select(moeGroomingAppointment.id)
                .from(moeGroomingAppointment)
                .where(moeGroomingAppointment.companyId, isEqualTo(companyId))
                .and(moeGroomingAppointment.isWaitingList, isEqualTo(CommonConstant.DISABLE))
                .and(moeGroomingAppointment.isDeprecate, isFalse())
                .and(moeGroomingAppointment.isBlock, isEqualTo(CommonConstant.DELETED))
                .and(moeGroomingAppointment.status, isIn(ACTIVE_STATUS_VALUE_SET))
                .and(
                        moeGroomingAppointment.appointmentDate,
                        isGreaterThan(date),
                        or(
                                moeGroomingAppointment.appointmentDate,
                                isEqualTo(date),
                                and(moeGroomingAppointment.appointmentEndTime, isGreaterThanOrEqualTo(nowMinutes))));

        return evaluationMapper.select(
                c -> c.where(EvaluationServiceDetailDynamicSqlSupport.appointmentId, isIn(appointmentSubQuery))
                        .and(evaluationServiceDetail.serviceId, isIn(evaluationIds)));
    }

    public int updatePetEvaluationById(EvaluationServiceDetail petDetail) {
        return evaluationMapper.updateByPrimaryKeySelective(petDetail);
    }

    @Transactional
    public int updatePetEvaluationById(List<EvaluationServiceDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return 0;
        }
        int affectedRows =
                petDetails.stream().mapToInt(this::updatePetEvaluationById).sum();
        List<Long> resetIds = petDetails.stream()
                .filter(update -> Objects.isNull(update.getStaffId()))
                .map(EvaluationServiceDetail::getId)
                .toList();
        if (CollectionUtils.isEmpty(resetIds)) {
            return affectedRows;
        }
        resetStaff(resetIds);
        return affectedRows;
    }

    @Transactional
    public int updateLodging(List<Long> ids, Long targetLodgingId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        return evaluationMapper.update(c -> c.set(EvaluationServiceDetailDynamicSqlSupport.lodgingId)
                .equalTo(targetLodgingId)
                .where(EvaluationServiceDetailDynamicSqlSupport.id, isIn(ids)));
    }

    public int resetStaff(long id) {
        return evaluationMapper.update(c -> c.set(EvaluationServiceDetailDynamicSqlSupport.staffId)
                .equalToNull()
                .where(EvaluationServiceDetailDynamicSqlSupport.id, isEqualTo(id)));
    }

    @Transactional
    public int resetStaff(List<Long> idList) {
        return idList.stream().mapToInt(this::resetStaff).sum();
    }

    public EvaluationServiceModel toPetEvaluationModel(EvaluationServiceDetail petDetail) {
        EvaluationServiceModel.Builder builder = EvaluationServiceModel.newBuilder()
                .setId(petDetail.getId())
                .setAppointmentId(petDetail.getAppointmentId())
                .setPetId(petDetail.getPetId())
                .setServiceId(petDetail.getServiceId())
                .setServiceTime(petDetail.getServiceTime())
                .setServicePrice(petDetail.getServicePrice().doubleValue())
                .setStartDate(petDetail.getStartDate().toString())
                .setStartTime(petDetail.getStartTime())
                .setEndDate(petDetail.getEndDate().toString())
                .setEndTime(petDetail.getEndTime())
                .setLodgingId(petDetail.getLodgingId());
        if (petDetail.getStaffId() != null && petDetail.getStaffId() != 0) {
            builder.setStaffId(petDetail.getStaffId());
        }
        return builder.build();
    }

    public List<EvaluationServiceModel> toPetEvaluationModel(List<EvaluationServiceDetail> petDetails) {
        return petDetails.stream().map(this::toPetEvaluationModel).toList();
    }

    @Transactional
    public void upsertPetEvaluation(MoeGroomingAppointment appointment, List<PetDetailDef> petDetailDefs) {
        // 1. 删除 pet 的 pet evaluations
        var petIds = petDetailDefs.stream().map(PetDetailDef::getPetId).toList();
        deleteByPetIds(appointment.getId().longValue(), petIds);
        // 2. 保存新的 pet details
        saveEvaluationDetails(appointment, buildEvaluationList(petDetailDefs));
    }

    public boolean isBelongsToAppointment(long appointmentId, List<Long> petEvaluationIds) {
        if (CollectionUtils.isEmpty(petEvaluationIds)) {
            return true;
        }
        var existPetDetailIds = getPetEvaluationList(appointmentId).stream()
                .map(EvaluationServiceDetail::getId)
                .collect(Collectors.toSet());
        return existPetDetailIds.containsAll(petEvaluationIds);
    }
}
