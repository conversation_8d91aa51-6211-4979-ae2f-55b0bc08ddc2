package com.moego.svc.appointment.service;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.CalculateServiceChargeParam;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataName;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.service.appointment.v1.GetAutoApplyServiceChargeRequest;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.svc.appointment.converter.ApplyServiceChargeConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.converter.PetFeedingConverter;
import com.moego.svc.appointment.converter.PetMedicationConverter;
import com.moego.svc.appointment.domain.AppointmentPetFeeding;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.AppointmentPetScheduleSetting;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.service.remote.BusinessPetMetadataRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrganizationRemoteService;
import com.moego.svc.appointment.service.remote.ServiceChargeHelper;
import com.moego.svc.appointment.utils.ServiceChargeUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class ApplyServiceChargeService {
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final AppointmentPetMedicationService appointmentPetMedicationService;
    private final AppointmentPetFeedingService appointmentPetFeedingService;
    private final AppointmentPetScheduleSettingService appointmentPetScheduleSettingService;
    private final BusinessPetMetadataRemoteService businessPetMetadataRemoteService;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestServiceStub;
    private final PetFeedingConverter feedingConverter;
    private final PetMedicationConverter medicationConverter;
    private final OfferingRemoteService offeringRemoteService;
    private final ServiceChargeHelper serviceChargeHelper;
    private final IBusinessBusinessService businessApi;
    private final OrganizationRemoteService organizationRemoteService;

    public List<ServiceCharge> listServiceChargeByFilter(
            Long companyId, Long businessId, GetAutoApplyServiceChargeRequest.ServiceChargeFilter filter) {
        if (!CommonUtil.isNormal(companyId) || !CommonUtil.isNormal(businessId)) {
            return Collections.emptyList();
        }
        List<ServiceCharge> serviceCharges = serviceChargeHelper.listActiveServiceCharges(companyId, businessId);
        if (CollectionUtils.isEmpty(serviceCharges)) {
            return List.of();
        }

        if (Objects.isNull(filter)) {
            return serviceCharges;
        }

        return serviceCharges.stream()
                .filter(serviceCharge -> filterAutoApplyService(filter, serviceCharge))
                .filter(serviceCharge -> filterServiceChargeByIds(filter, serviceCharge))
                .toList();
    }

    private Boolean filterAutoApplyService(
            GetAutoApplyServiceChargeRequest.ServiceChargeFilter filter, ServiceCharge serviceCharge) {
        return CollectionUtils.isEmpty(filter.getAutoApplyStatusList())
                || filter.getAutoApplyStatusList().contains(serviceCharge.getAutoApplyStatus());
    }

    private Boolean filterServiceChargeByIds(
            GetAutoApplyServiceChargeRequest.ServiceChargeFilter filter, ServiceCharge serviceCharge) {
        return CollectionUtils.isEmpty(filter.getServiceChargeIdsList())
                || filter.getServiceChargeIdsList().contains(serviceCharge.getId());
    }

    /**
     * 获取 appointment 需要被 auto apply 的 service charge 及对应数量
     *
     * @param companyId         The ID of the company.
     * @param appointmentId     The ID of the appointment.
     * @param serviceChargeList The list of auto apply service charges.
     * @return A map containing the service charge ID and its quantity.
     */
    public Map<Long, Integer> getAppointmentAutoApplyServiceCharge(
            Long companyId, Long appointmentId, List<ServiceCharge> serviceChargeList) {
        MoeGroomingAppointment appointment = appointmentService.get(appointmentId);
        List<MoeGroomingPetDetail> petDetailList = petDetailService.getPetDetailList(appointmentId);
        if (Objects.isNull(appointment)
                || CollectionUtils.isEmpty(petDetailList)
                || CollectionUtils.isEmpty(serviceChargeList)) {
            return Map.of();
        }

        List<AppointmentPetFeeding> petFeedings =
                appointmentPetFeedingService.listPetFeedings(companyId, List.of(appointmentId));
        List<AppointmentPetMedication> petMedications =
                appointmentPetMedicationService.listPetMedicationsByAppointmentId(appointmentId);
        List<AppointmentPetScheduleSetting> petScheduleSettings =
                appointmentPetScheduleSettingService.listPetSchedules(companyId, List.of(appointmentId));
        Map<String, Long> foodSourceMapping = getFoodSourceMapping(companyId);

        List<AppointmentPetFeedingScheduleDef> petFeedingSchedules =
                feedingConverter.toPetFeedingSchedules(petFeedings, petScheduleSettings);
        List<AppointmentPetMedicationScheduleDef> petMedicationSchedules =
                medicationConverter.toPetMedicationSchedules(petMedications, petScheduleSettings);

        return ServiceChargeUtil.calculateServiceChargeApplyCounts(
                serviceChargeList, petDetailList, petFeedingSchedules, petMedicationSchedules, foodSourceMapping);
    }

    /**
     * 获取 online booking 需要被 auto apply 的 service charge 及对应数量
     *
     * @param companyId         The ID of the company.
     * @param onlineBookingId   The ID of the online booking.
     * @param serviceChargeList The list of auto apply service charges.
     * @return A map containing the service charge ID and its quantity.
     */
    public Map<Long, Integer> getObAutoApplyServiceCharge(
            Long companyId, Long onlineBookingId, List<ServiceCharge> serviceChargeList) {
        BookingRequestModel bookingRequest = bookingRequestServiceStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(onlineBookingId)
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION))
                        .build())
                .getBookingRequest();
        List<ServiceBriefView> serviceList = getServicesByPetService(companyId, bookingRequest.getServicesList());
        var petDetailList = buildPetDetails(bookingRequest.getServicesList(), serviceList);
        var petFeedingSchedules = feedingConverter.buildPetFeedingSchedules(bookingRequest.getServicesList());
        var petMedicationSchedules = medicationConverter.buildPetMedicationSchedules(bookingRequest.getServicesList());
        Map<String, Long> foodSourceMapping = getFoodSourceMapping(companyId);
        return ServiceChargeUtil.calculateServiceChargeApplyCounts(
                serviceChargeList, petDetailList, petFeedingSchedules, petMedicationSchedules, foodSourceMapping);
    }

    /**
     * 获取 food source mapping
     *
     * @param companyId company_id
     * @return food source mapping
     */
    public Map<String, Long> getFoodSourceMapping(Long companyId) {
        return businessPetMetadataRemoteService
                .listPetMetadata(companyId, BusinessPetMetadataName.FEEDING_SOURCE)
                .stream()
                .collect(Collectors.toMap(BusinessPetMetadataModel::getMetadataValue, BusinessPetMetadataModel::getId));
    }

    public Map<Long, Integer> getPetDetailAutoApplyServiceCharge(
            Long companyId,
            CalculateServiceChargeParam calculateServiceChargeParam,
            List<ServiceCharge> serviceChargeList) {

        if (Objects.isNull(calculateServiceChargeParam)
                || CollectionUtils.isEmpty(calculateServiceChargeParam.getPetDetailsList())
                || CollectionUtils.isEmpty(serviceChargeList)) {
            return Map.of();
        }

        var petDetailList = ApplyServiceChargeConverter.INSTANCE.convertMoeGroomingPetDetails(
                calculateServiceChargeParam.getPetDetailsList());
        var petFeedingSchedules = calculateServiceChargeParam.getFeedingScheduleList();
        var petMedicationSchedules = calculateServiceChargeParam.getMedicationScheduleList();
        Map<String, Long> foodSourceMapping = getFoodSourceMapping(companyId);
        return ServiceChargeUtil.calculateServiceChargeApplyCounts(
                serviceChargeList, petDetailList, petFeedingSchedules, petMedicationSchedules, foodSourceMapping);
    }

    private List<ServiceBriefView> getServicesByPetService(
            Long companyId, List<BookingRequestModel.Service> petServices) {
        HashSet<Long> serviceIds = new HashSet<>();
        for (BookingRequestModel.Service petService : petServices) {

            switch (petService.getServiceCase()) {
                case GROOMING -> serviceIds.add(
                        petService.getGrooming().getService().getServiceId());
                case BOARDING -> serviceIds.add(
                        petService.getBoarding().getService().getServiceId());
                case DAYCARE -> serviceIds.add(
                        petService.getDaycare().getService().getServiceId());
                case DOG_WALKING -> serviceIds.add(
                        petService.getDogWalking().getService().getServiceId());
                default -> {
                    // 跳过无效的服务类型, evaluation、group_class 不需要处理
                    continue;
                }
            }
        }

        return offeringRemoteService.getServiceModels(
                companyId, serviceIds.stream().toList());
    }

    private List<MoeGroomingPetDetail> buildPetDetails(
            List<BookingRequestModel.Service> petServices, List<ServiceBriefView> services) {
        if (CollectionUtils.isEmpty(petServices)) {
            return Collections.emptyList();
        }

        var serviceMap = services.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));

        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();

        for (BookingRequestModel.Service petService : petServices) {
            MoeGroomingPetDetail petDetail = null;

            switch (petService.getServiceCase()) {
                case GROOMING:
                    petDetail = PetDetailConverter.INSTANCE.convertOBGroomingToPetDetail(
                            petService.getGrooming(), serviceMap);
                    break;
                case BOARDING:
                    petDetail = PetDetailConverter.INSTANCE.convertOBBoardingToPetDetail(
                            petService.getBoarding(), serviceMap);
                    break;
                case DAYCARE:
                    petDetail = PetDetailConverter.INSTANCE.convertOBDaycareToPetDetail(
                            petService.getDaycare(), serviceMap);
                    break;
                case DOG_WALKING:
                    petDetail = PetDetailConverter.INSTANCE.convertOBDogWalkingToPetDetail(
                            petService.getDogWalking(), serviceMap);
                    break;
                case EVALUATION, GROUP_CLASS, SERVICE_NOT_SET:
                    continue; // evaluation、group_class 不需要处理
                default:
                    // 跳过无效的服务类型
                    continue;
            }

            if (Objects.nonNull(petDetail)) {
                petDetails.add(petDetail);
            }
        }

        return petDetails;
    }

    public BigDecimal calculateServiceChargeAmount(MoeGroomingAppointment appointment) {
        var companyId = appointment.getCompanyId();
        var businessId = appointment.getBusinessId();
        var appointmentId = appointment.getId().longValue();

        var serviceCharges = serviceChargeHelper.listAutoApplyServiceCharges(companyId, businessId);
        if (CollectionUtils.isEmpty(serviceCharges)) {
            return BigDecimal.ZERO;
        }

        var serviceChargeIdToApplyCount =
                getAppointmentAutoApplyServiceCharge(companyId, appointmentId, serviceCharges);

        return ServiceChargeUtil.calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);
    }

    public BigDecimal calculateServiceChargeAmount(long companyId, long businessId, List<PetDetailDef> petDetailDefs) {
        var serviceCharges = serviceChargeHelper.listAutoApplyServiceCharges(companyId, businessId);
        if (CollectionUtils.isEmpty(serviceCharges)) {
            return BigDecimal.ZERO;
        }

        Map<String, Long> foodSourceMapping = getFoodSourceMapping(companyId);
        var selectedServiceDefs = petDetailDefs.stream()
                .map(PetDetailDef::getServicesList)
                .flatMap(List::stream)
                .toList();

        var petFeedingSchedules = selectedServiceDefs.stream()
                .map(SelectedServiceDef::getFeedingsList)
                .flatMap(List::stream)
                .toList();

        var petMedicationSchedules = selectedServiceDefs.stream()
                .map(SelectedServiceDef::getMedicationsList)
                .flatMap(List::stream)
                .toList();

        var serviceIds = selectedServiceDefs.stream()
                .map(SelectedServiceDef::getServiceId)
                .distinct()
                .toList();
        var services = offeringRemoteService.getServiceModels(companyId, serviceIds);
        var idToService = services.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));

        var petDetails = selectedServiceDefs.stream()
                .map(PetDetailConverter.INSTANCE::toDomain)
                .toList();
        petDetails.forEach(p -> {
            var service = idToService.getOrDefault(p.getServiceId().longValue(), ServiceBriefView.getDefaultInstance());
            p.setServiceItemType((byte) service.getServiceItemType().getNumber());
            p.setServiceType(service.getTypeValue());
        });

        var serviceChargeIdToApplyCount = ServiceChargeUtil.calculateServiceChargeApplyCounts(
                serviceCharges, petDetails, petFeedingSchedules, petMedicationSchedules, foodSourceMapping);

        return ServiceChargeUtil.calculateApplyAmount(serviceChargeIdToApplyCount, serviceCharges);
    }

    public List<ServiceCharge> listHitLatePickUpServiceCharges(long companyId, long appointmentId) {
        var appointment = appointmentService.getAppointment(companyId, appointmentId);
        var petDetails = petDetailService.getPetDetailList(appointmentId);
        var businessId = appointment.getBusinessId();

        var conditionServiceCharges = serviceChargeHelper.listConditionServiceCharge(companyId, businessId);

        var businessDateTime = businessApi.getBusinessDateTime(businessId);
        var businessWorkingHour = organizationRemoteService.getBusinessWorkingHourDef(companyId, businessId);

        var latePickUpServiceCharges = conditionServiceCharges.stream()
                .filter(serviceCharge -> ServiceChargeUtil.isMetLatePickUpCondition(
                        serviceCharge, businessDateTime, businessWorkingHour, petDetails))
                .toList();

        var exceed24HourServiceCharges = conditionServiceCharges.stream()
                .filter(serviceCharge -> ServiceChargeUtil.isMetExceed24HourCondition(
                        serviceCharge,
                        appointment.getServiceTypeInclude(),
                        businessDateTime,
                        appointment.getCheckInTime()))
                .toList();

        return Stream.concat(latePickUpServiceCharges.stream(), exceed24HourServiceCharges.stream())
                .toList();
    }
}
