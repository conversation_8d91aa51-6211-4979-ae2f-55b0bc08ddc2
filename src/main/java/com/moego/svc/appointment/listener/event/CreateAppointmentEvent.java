package com.moego.svc.appointment.listener.event;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
@Getter
@Setter
@Accessors(chain = true)
public class CreateAppointmentEvent extends ApplicationEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 3138610090430005242L;

    private Long companyId;
    private Long businessId;
    private Long customerId;
    private Long staffId;
    private Long appointmentId;
    private List<PetDetailDef> petDetailDefs;

    private transient MoeGroomingAppointment appointment;

    private Boolean isBookingRequest;

    public CreateAppointmentEvent(Object source) {
        super(source);
    }
}
