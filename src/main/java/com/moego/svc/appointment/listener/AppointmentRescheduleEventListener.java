package com.moego.svc.appointment.listener;

import static com.moego.svc.appointment.listener.AppointmentEventListener.getTimestamp;

import com.moego.idl.models.event_bus.v1.AppointmentRescheduledEvent;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.TimeZone;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.svc.appointment.client.OrganizationClient;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.listener.event.RescheduleCalendarCardEvent;
import com.moego.svc.appointment.listener.event.RescheduleGroomingServiceEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetDetailsEvent;
import com.moego.svc.appointment.listener.event.ReschedulePetFeedingMedicationEvent;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AppointmentRescheduleEventListener {
    private final AppointmentServiceProxy appointmentService;
    private final OrganizationClient organizationClient;
    private final Producer producer;
    private static final String EVENT_TOPIC = "moego.erp.appointment";

    @EventListener({
        RescheduleCalendarCardEvent.class,
        RescheduleGroomingServiceEvent.class,
        ReschedulePetDetailsEvent.class,
        ReschedulePetFeedingMedicationEvent.class
    })
    public void handleAppointmentRescheduleEvent(ApplicationEvent event) {
        ThreadPool.execute(() -> {
            MoeGroomingAppointment appointment = getAppointment(event);
            if (appointment == null) {
                return;
            }
            TimeZone timeZone = organizationClient.getTimeZone(appointment.getCompanyId());
            producer.send(
                    EVENT_TOPIC,
                    EventRecord.builder()
                            .id(appointment.getId().toString())
                            .detail(EventData.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(appointment.getCompanyId())
                                            .setBusinessId(appointment.getBusinessId())
                                            .build())
                                    .setAppointmentRescheduledEvent(AppointmentRescheduledEvent.newBuilder()
                                            .setId(appointment.getId())
                                            .setCustomerId(appointment.getCustomerId())
                                            .setStartTime(getTimestamp(
                                                    timeZone,
                                                    appointment.getAppointmentDate(),
                                                    appointment.getAppointmentStartTime()))
                                            .setEndTime(getTimestamp(
                                                    timeZone,
                                                    appointment.getAppointmentEndDate(),
                                                    appointment.getAppointmentEndTime()))
                                            .build())
                                    .build())
                            .time(Instant.now())
                            .type(EventType.APPOINTMENT_RESCHEDULED)
                            .build());
        });
    }

    private MoeGroomingAppointment getAppointment(ApplicationEvent event) {
        var appointmentId = 0L;
        if (event instanceof RescheduleCalendarCardEvent) {
            appointmentId = ((RescheduleCalendarCardEvent) event).getAppointmentId();
        } else if (event instanceof RescheduleGroomingServiceEvent) {
            appointmentId = ((RescheduleGroomingServiceEvent) event).getAppointmentId();
        } else if (event instanceof ReschedulePetDetailsEvent) {
            appointmentId = ((ReschedulePetDetailsEvent) event).getAppointmentId();
        } else if (event instanceof ReschedulePetFeedingMedicationEvent) {
            appointmentId = ((ReschedulePetFeedingMedicationEvent) event).getAppointmentId();
        }
        if (appointmentId == 0) {
            return null;
        }
        return appointmentService.getAppointment(appointmentId);
    }
}
