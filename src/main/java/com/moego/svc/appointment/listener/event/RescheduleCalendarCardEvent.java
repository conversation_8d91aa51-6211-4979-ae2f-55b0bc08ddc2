package com.moego.svc.appointment.listener.event;

import jakarta.annotation.Nullable;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@Getter
@Setter
@Accessors(chain = true)
public class RescheduleCalendarCardEvent extends ApplicationEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = -9159590222536476665L;

    private Long companyId;
    private Long businessId;
    private Long customerId;
    private Long appointmentId;
    private LocalDateTime beforeAppointmentDateTime;
    private List<Integer> beforeStaffIds;

    @Nullable
    private Long updatedBy;

    public RescheduleCalendarCardEvent(Object source) {
        super(source);
    }
}
