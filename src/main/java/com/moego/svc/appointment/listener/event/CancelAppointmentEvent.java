package com.moego.svc.appointment.listener.event;

import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
@Getter
@Setter
@Accessors(chain = true)
public class CancelAppointmentEvent extends ApplicationEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = -4114093030359909910L;

    private Long companyId;
    private Long businessId;
    private Long appointmentId;
    private Long customerId;

    private AppointmentUpdatedBy cancelByType;
    /**
     * Cancel by staff or customer
     * staff: staff_id
     * customer: account_id
     */
    private Long cancelBy;

    private String cancelReason;

    private AppointmentNoShowStatus noShow;

    /**
     * Whether the appointment is booking request
     */
    private boolean isBookingRequest;

    private boolean isRepeatEvent;

    private transient MoeGroomingAppointment appointment;

    public CancelAppointmentEvent(Object source) {
        super(source);
    }
}
