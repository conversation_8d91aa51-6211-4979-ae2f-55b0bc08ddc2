package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table appointment_idempotency
 */
public class AppointmentIdempotency {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   feature type, 1-booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_type")
    private Integer featureType;

    /**
     * Database Column Remarks:
     *   feature key
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_key")
    private String featureKey;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.create_time")
    private Date createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_type")
    public Integer getFeatureType() {
        return featureType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_type")
    public void setFeatureType(Integer featureType) {
        this.featureType = featureType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_key")
    public String getFeatureKey() {
        return featureKey;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.feature_key")
    public void setFeatureKey(String featureKey) {
        this.featureKey = featureKey == null ? null : featureKey.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_idempotency.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_idempotency")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", featureType=").append(featureType);
        sb.append(", featureKey=").append(featureKey);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_idempotency")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AppointmentIdempotency other = (AppointmentIdempotency) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFeatureType() == null ? other.getFeatureType() == null : this.getFeatureType().equals(other.getFeatureType()))
            && (this.getFeatureKey() == null ? other.getFeatureKey() == null : this.getFeatureKey().equals(other.getFeatureKey()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_idempotency")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFeatureType() == null) ? 0 : getFeatureType().hashCode());
        result = prime * result + ((getFeatureKey() == null) ? 0 : getFeatureKey().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }
}