package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report
 */
public class MoeGroomingReport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.id")
    private Integer id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.business_id")
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   customer id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.customer_id")
    private Integer customerId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.grooming_id")
    private Integer groomingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_id")
    private Integer petId;

    /**
     * Database Column Remarks:
     *   pet type id: 1-dog, 2-cat
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_type_id")
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   category name, reserved field
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.uuid")
    private String uuid;

    /**
     * Database Column Remarks:
     *   used template publish time, for version check
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_publish_time")
    private Date templatePublishTime;

    /**
     * Database Column Remarks:
     *   grooming report status: Created/Draft/Ready/Sent
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.status")
    private String status;

    /**
     * Database Column Remarks:
     *   grooming report submitted time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.submitted_time")
    private Date submittedTime;

    /**
     * Database Column Remarks:
     *   grooming report link opened count
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.link_opened_count")
    private Integer linkOpenedCount;

    /**
     * Database Column Remarks:
     *   last update staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_by")
    private Integer updateBy;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   theme code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.theme_code")
    private String themeCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.company_id")
    private Long companyId;

    /**
     * Database Column Remarks:
     *   grooming report template without question list
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_json")
    private String templateJson;

    /**
     * Database Column Remarks:
     *   grooming report fill in content, json format
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.content_json")
    private String contentJson;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.business_id")
    public Integer getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.business_id")
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.customer_id")
    public Integer getCustomerId() {
        return customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.customer_id")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.grooming_id")
    public Integer getGroomingId() {
        return groomingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.grooming_id")
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_id")
    public Integer getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_id")
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_type_id")
    public Integer getPetTypeId() {
        return petTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.pet_type_id")
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.uuid")
    public String getUuid() {
        return uuid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.uuid")
    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_publish_time")
    public Date getTemplatePublishTime() {
        return templatePublishTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_publish_time")
    public void setTemplatePublishTime(Date templatePublishTime) {
        this.templatePublishTime = templatePublishTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.status")
    public String getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.status")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.submitted_time")
    public Date getSubmittedTime() {
        return submittedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.submitted_time")
    public void setSubmittedTime(Date submittedTime) {
        this.submittedTime = submittedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.link_opened_count")
    public Integer getLinkOpenedCount() {
        return linkOpenedCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.link_opened_count")
    public void setLinkOpenedCount(Integer linkOpenedCount) {
        this.linkOpenedCount = linkOpenedCount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_by")
    public Integer getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_by")
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.theme_code")
    public String getThemeCode() {
        return themeCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.theme_code")
    public void setThemeCode(String themeCode) {
        this.themeCode = themeCode == null ? null : themeCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_json")
    public String getTemplateJson() {
        return templateJson;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.template_json")
    public void setTemplateJson(String templateJson) {
        this.templateJson = templateJson == null ? null : templateJson.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.content_json")
    public String getContentJson() {
        return contentJson;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_report.content_json")
    public void setContentJson(String contentJson) {
        this.contentJson = contentJson == null ? null : contentJson.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_report")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", customerId=").append(customerId);
        sb.append(", groomingId=").append(groomingId);
        sb.append(", petId=").append(petId);
        sb.append(", petTypeId=").append(petTypeId);
        sb.append(", uuid=").append(uuid);
        sb.append(", templatePublishTime=").append(templatePublishTime);
        sb.append(", status=").append(status);
        sb.append(", submittedTime=").append(submittedTime);
        sb.append(", linkOpenedCount=").append(linkOpenedCount);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", themeCode=").append(themeCode);
        sb.append(", companyId=").append(companyId);
        sb.append(", templateJson=").append(templateJson);
        sb.append(", contentJson=").append(contentJson);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_report")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MoeGroomingReport other = (MoeGroomingReport) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getGroomingId() == null ? other.getGroomingId() == null : this.getGroomingId().equals(other.getGroomingId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getPetTypeId() == null ? other.getPetTypeId() == null : this.getPetTypeId().equals(other.getPetTypeId()))
            && (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getTemplatePublishTime() == null ? other.getTemplatePublishTime() == null : this.getTemplatePublishTime().equals(other.getTemplatePublishTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getSubmittedTime() == null ? other.getSubmittedTime() == null : this.getSubmittedTime().equals(other.getSubmittedTime()))
            && (this.getLinkOpenedCount() == null ? other.getLinkOpenedCount() == null : this.getLinkOpenedCount().equals(other.getLinkOpenedCount()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getThemeCode() == null ? other.getThemeCode() == null : this.getThemeCode().equals(other.getThemeCode()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_report")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getGroomingId() == null) ? 0 : getGroomingId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getPetTypeId() == null) ? 0 : getPetTypeId().hashCode());
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getTemplatePublishTime() == null) ? 0 : getTemplatePublishTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getSubmittedTime() == null) ? 0 : getSubmittedTime().hashCode());
        result = prime * result + ((getLinkOpenedCount() == null) ? 0 : getLinkOpenedCount().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getThemeCode() == null) ? 0 : getThemeCode().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        return result;
    }
}