package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.math.BigDecimal;

/**
 * Database Table Remarks:
 *   宠物服务内容
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_service
 */
public class GroomingService {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_grooming_service.id")
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.business_id")
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   类型id
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.category_id")
    private Integer categoryId;

    /**
     * Database Column Remarks:
     *   服务名称
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.name")
    private String name;

    /**
     * Database Column Remarks:
     *   数据类型：1-主服务(service)；2-额外服务(addons)
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.type")
    private Byte type;

    /**
     * Database Column Remarks:
     *   税费id
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.tax_id")
    private Integer taxId;

    /**
     * Database Column Remarks:
     *   服务价格
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price")
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   服务时间
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.duration")
    private Integer duration;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.inactive")
    private Byte inactive;

    /**
     * Database Column Remarks:
     *   排序值
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.sort")
    private Integer sort;

    /**
     * Database Column Remarks:
     *   color code
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.color_code")
    private String colorCode;

    /**
     * Database Column Remarks:
     *   1 正常 2删除
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.status")
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.create_time")
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.update_time")
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   booking online 是否显示价格
     *   0 do not show price
     *   1 show fixd service price
     *   2 show price with "starting at"
     *   3 show price sa "Varies"
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.show_base_price")
    private Byte showBasePrice;

    /**
     * Database Column Remarks:
     *   0-not  1-yes
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.book_online_available")
    private Byte bookOnlineAvailable;

    /**
     * Database Column Remarks:
     *   是否全选staff
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_staff")
    private Byte isAllStaff;

    /**
     * Database Column Remarks:
     *   filtered by breed, 0-all breed, 1-filter by selected breeds, see table moe_grooming_service_type_breed_binding
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.breed_filter")
    private Byte breedFilter;

    /**
     * Database Column Remarks:
     *   filtered by weight, 0-all weight, 1-filter by weight range
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_filter")
    private Byte weightFilter;

    /**
     * Database Column Remarks:
     *   weight filter down limit
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_down_limit")
    private BigDecimal weightDownLimit;

    /**
     * Database Column Remarks:
     *   weight filter up limit
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_up_limit")
    private BigDecimal weightUpLimit;

    /**
     * Database Column Remarks:
     *   filtered by coat, 0-all coat, 1-filter by selected coat
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.coat_filter")
    private Byte coatFilter;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.company_id")
    private Long companyId;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_location")
    private Byte isAllLocation;

    /**
     * Database Column Remarks:
     *   1 - grooming, 2 - boarding, 3 - daycare
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_item_type")
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   1 - per session, 2 - per night, 3 - per hour
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price_unit")
    private Integer priceUnit;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.add_to_commission")
    private Boolean addToCommission;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.can_tip")
    private Boolean canTip;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_staff")
    private Boolean requireDedicatedStaff;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_lodging")
    private Boolean requireDedicatedLodging;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.lodging_filter")
    private Boolean lodgingFilter;

    /**
     * Database Column Remarks:
     *   only for add-on
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_filter")
    private Boolean serviceFilter;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.pet_size_filter")
    private Boolean petSizeFilter;

    /**
     * Database Column Remarks:
     *   max duration in minutes, only for daycare
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.max_duration")
    private Integer maxDuration;

    /**
     * Database Column Remarks:
     *   描述
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.description")
    private String description;

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.images")
    private String images;

    /**
     * Database Column Remarks:
     *   allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_lodging_list")
    private String allowedLodgingList;

    /**
     * Database Column Remarks:
     *   allowed pet size list, only when service_filter is true
     */
    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_pet_size_list")
    private String allowedPetSizeList;

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_grooming_service.id")
    public Integer getId() {
        return id;
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source field: moe_grooming_service.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.business_id")
    public Integer getBusinessId() {
        return businessId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.business_id")
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.category_id")
    public Integer getCategoryId() {
        return categoryId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.category_id")
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.name")
    public String getName() {
        return name;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.name")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.type")
    public Byte getType() {
        return type;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.type")
    public void setType(Byte type) {
        this.type = type;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.tax_id")
    public Integer getTaxId() {
        return taxId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.tax_id")
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price")
    public BigDecimal getPrice() {
        return price;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price")
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.duration")
    public Integer getDuration() {
        return duration;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.inactive")
    public Byte getInactive() {
        return inactive;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.inactive")
    public void setInactive(Byte inactive) {
        this.inactive = inactive;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.sort")
    public Integer getSort() {
        return sort;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.sort")
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.color_code")
    public String getColorCode() {
        return colorCode;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.color_code")
    public void setColorCode(String colorCode) {
        this.colorCode = colorCode == null ? null : colorCode.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.status")
    public Byte getStatus() {
        return status;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.status")
    public void setStatus(Byte status) {
        this.status = status;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.create_time")
    public Long getCreateTime() {
        return createTime;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.create_time")
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.update_time")
    public Long getUpdateTime() {
        return updateTime;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.update_time")
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.show_base_price")
    public Byte getShowBasePrice() {
        return showBasePrice;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.show_base_price")
    public void setShowBasePrice(Byte showBasePrice) {
        this.showBasePrice = showBasePrice;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.book_online_available")
    public Byte getBookOnlineAvailable() {
        return bookOnlineAvailable;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.book_online_available")
    public void setBookOnlineAvailable(Byte bookOnlineAvailable) {
        this.bookOnlineAvailable = bookOnlineAvailable;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_staff")
    public Byte getIsAllStaff() {
        return isAllStaff;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_staff")
    public void setIsAllStaff(Byte isAllStaff) {
        this.isAllStaff = isAllStaff;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.breed_filter")
    public Byte getBreedFilter() {
        return breedFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.breed_filter")
    public void setBreedFilter(Byte breedFilter) {
        this.breedFilter = breedFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_filter")
    public Byte getWeightFilter() {
        return weightFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_filter")
    public void setWeightFilter(Byte weightFilter) {
        this.weightFilter = weightFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_down_limit")
    public BigDecimal getWeightDownLimit() {
        return weightDownLimit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_down_limit")
    public void setWeightDownLimit(BigDecimal weightDownLimit) {
        this.weightDownLimit = weightDownLimit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_up_limit")
    public BigDecimal getWeightUpLimit() {
        return weightUpLimit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.weight_up_limit")
    public void setWeightUpLimit(BigDecimal weightUpLimit) {
        this.weightUpLimit = weightUpLimit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.coat_filter")
    public Byte getCoatFilter() {
        return coatFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.coat_filter")
    public void setCoatFilter(Byte coatFilter) {
        this.coatFilter = coatFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_location")
    public Byte getIsAllLocation() {
        return isAllLocation;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.is_all_location")
    public void setIsAllLocation(Byte isAllLocation) {
        this.isAllLocation = isAllLocation;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_item_type")
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_item_type")
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price_unit")
    public Integer getPriceUnit() {
        return priceUnit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.price_unit")
    public void setPriceUnit(Integer priceUnit) {
        this.priceUnit = priceUnit;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.add_to_commission")
    public Boolean getAddToCommission() {
        return addToCommission;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.add_to_commission")
    public void setAddToCommission(Boolean addToCommission) {
        this.addToCommission = addToCommission;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.can_tip")
    public Boolean getCanTip() {
        return canTip;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.can_tip")
    public void setCanTip(Boolean canTip) {
        this.canTip = canTip;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_staff")
    public Boolean getRequireDedicatedStaff() {
        return requireDedicatedStaff;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_staff")
    public void setRequireDedicatedStaff(Boolean requireDedicatedStaff) {
        this.requireDedicatedStaff = requireDedicatedStaff;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_lodging")
    public Boolean getRequireDedicatedLodging() {
        return requireDedicatedLodging;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.require_dedicated_lodging")
    public void setRequireDedicatedLodging(Boolean requireDedicatedLodging) {
        this.requireDedicatedLodging = requireDedicatedLodging;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.lodging_filter")
    public Boolean getLodgingFilter() {
        return lodgingFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.lodging_filter")
    public void setLodgingFilter(Boolean lodgingFilter) {
        this.lodgingFilter = lodgingFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_filter")
    public Boolean getServiceFilter() {
        return serviceFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.service_filter")
    public void setServiceFilter(Boolean serviceFilter) {
        this.serviceFilter = serviceFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.pet_size_filter")
    public Boolean getPetSizeFilter() {
        return petSizeFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.pet_size_filter")
    public void setPetSizeFilter(Boolean petSizeFilter) {
        this.petSizeFilter = petSizeFilter;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.max_duration")
    public Integer getMaxDuration() {
        return maxDuration;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.max_duration")
    public void setMaxDuration(Integer maxDuration) {
        this.maxDuration = maxDuration;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.description")
    public String getDescription() {
        return description;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.description")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.images")
    public String getImages() {
        return images;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.images")
    public void setImages(String images) {
        this.images = images == null ? null : images.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_lodging_list")
    public String getAllowedLodgingList() {
        return allowedLodgingList;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_lodging_list")
    public void setAllowedLodgingList(String allowedLodgingList) {
        this.allowedLodgingList = allowedLodgingList == null ? null : allowedLodgingList.trim();
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_pet_size_list")
    public String getAllowedPetSizeList() {
        return allowedPetSizeList;
    }

    @Generated(
            value = "org.mybatis.generator.api.MyBatisGenerator",
            comments = "Source field: moe_grooming_service.allowed_pet_size_list")
    public void setAllowedPetSizeList(String allowedPetSizeList) {
        this.allowedPetSizeList = allowedPetSizeList == null ? null : allowedPetSizeList.trim();
    }
}
