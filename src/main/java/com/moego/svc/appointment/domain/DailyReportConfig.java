package com.moego.svc.appointment.domain;

import com.moego.svc.appointment.dto.DailyReportContentDTO;
import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table daily_report_config
 */
public class DailyReportConfig {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.company_id")
    private Long companyId;

    /**
     * Database Column Remarks:
     *   customer id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.customer_id")
    private Long customerId;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   pet id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   uuid
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.uuid")
    private String uuid;

    /**
     * Database Column Remarks:
     *   report template
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.template_json")
    private DailyReportContentDTO templateJson;

    /**
     * Database Column Remarks:
     *   last update staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_by")
    private Long updateBy;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   service date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.service_date")
    private Date serviceDate;

    /**
     * Database Column Remarks:
     *   report status: created/draft/ready/sent
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.status")
    private String status;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.customer_id")
    public Long getCustomerId() {
        return customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.customer_id")
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.uuid")
    public String getUuid() {
        return uuid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.uuid")
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.template_json")
    public DailyReportContentDTO getTemplateJson() {
        return templateJson;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.template_json")
    public void setTemplateJson(DailyReportContentDTO templateJson) {
        this.templateJson = templateJson;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_by")
    public Long getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_by")
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.service_date")
    public Date getServiceDate() {
        return serviceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.service_date")
    public void setServiceDate(Date serviceDate) {
        this.serviceDate = serviceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.status")
    public String getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.status")
    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_config")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", companyId=").append(companyId);
        sb.append(", customerId=").append(customerId);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", petId=").append(petId);
        sb.append(", uuid=").append(uuid);
        sb.append(", templateJson=").append(templateJson);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serviceDate=").append(serviceDate);
        sb.append(", status=").append(status);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_config")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DailyReportConfig other = (DailyReportConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getTemplateJson() == null ? other.getTemplateJson() == null : this.getTemplateJson().equals(other.getTemplateJson()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getServiceDate() == null ? other.getServiceDate() == null : this.getServiceDate().equals(other.getServiceDate()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_config")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getTemplateJson() == null) ? 0 : getTemplateJson().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getServiceDate() == null) ? 0 : getServiceDate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }
}