package com.moego.svc.appointment.converter;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCalendarView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface CustomerAddressConverter {

    CustomerAddressConverter INSTANCE = Mappers.getMapper(CustomerAddressConverter.class);

    BusinessCustomerAddressCalendarView toView(BusinessCustomerAddressModel model);
}
