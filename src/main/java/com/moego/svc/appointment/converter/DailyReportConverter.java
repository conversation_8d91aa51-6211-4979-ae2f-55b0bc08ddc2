package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v1.ContentDef;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.appointment.v1.QuestionCategoryType;
import com.moego.idl.models.appointment.v1.QuestionDef;
import com.moego.idl.models.appointment.v1.QuestionType;
import com.moego.idl.models.appointment.v1.ReportCardStatus;
import com.moego.idl.models.appointment.v1.ReportDef;
import com.moego.idl.models.appointment.v1.SentHistoryRecordDef;
import com.moego.svc.appointment.domain.DailyReportConfig;
import com.moego.svc.appointment.domain.DailyReportSendLog;
import com.moego.svc.appointment.dto.DailyReportContentDTO;
import com.moego.svc.appointment.dto.DailyReportQuestionDTO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {DateConverter.class, TimeConverter.class})
public interface DailyReportConverter {

    DailyReportConverter INSTANCE = Mappers.getMapper(DailyReportConverter.class);

    ContentDef toContentDef(DailyReportContentDTO content);

    QuestionDef toQuestionDef(DailyReportQuestionDTO question);

    @InheritInverseConfiguration
    DailyReportContentDTO toContent(ContentDef content);

    @InheritInverseConfiguration
    DailyReportQuestionDTO toQuestion(QuestionDef question);

    @Mapping(target = "report", source = "contentJson", qualifiedByName = "dailyReportContentDTOToReportDef")
    SentHistoryRecordDef toSentHistoryRecordDef(DailyReportSendLog log);

    List<SentHistoryRecordDef> toSentHistoryRecordDef(List<DailyReportSendLog> log);

    default QuestionCategoryType mapQuestionCategoryType(Integer value) {
        return QuestionCategoryType.forNumber(value);
    }

    default QuestionType mapQuestionType(Integer value) {
        return QuestionType.forNumber(value);
    }

    default Integer mapQuestionCategoryType(QuestionCategoryType value) {
        return value.getNumber();
    }

    default Integer mapQuestionType(QuestionType value) {
        return value.getNumber();
    }

    @Named("dailyReportContentDTOToReportDef")
    default ReportDef dailyReportContentDTOToReportDef(DailyReportContentDTO dailyReportContentDTO) {
        if (dailyReportContentDTO == null) {
            return null;
        }

        return ReportDef.newBuilder()
                .setContent(ContentDef.newBuilder()
                        .addAllPhotos(dailyReportContentDTO.getPhotos())
                        .addAllVideos(dailyReportContentDTO.getVideos())
                        .addAllFeedbacks(dailyReportContentDTO.getFeedbacks().stream()
                                .map(this::toQuestionDef)
                                .toList())
                        .setThemeColor(dailyReportContentDTO.getThemeColor())
                        .build())
                .build();
    }

    default List<DailyReportConfigDef> converterToDailyReportConfigDefList(
            List<DailyReportConfig> dailyReportConfigList, List<DailyReportSendLog> dailyReportSendLogs) {
        Map<Long, DailyReportSendLog> sendLogMap = dailyReportSendLogs.stream()
                .collect(Collectors.toMap(DailyReportSendLog::getDailyReportId, Function.identity()));
        return dailyReportConfigList.stream()
                .map(dailyReportConfig -> {
                    DailyReportSendLog dailyReportSendLog = sendLogMap.get(dailyReportConfig.getId());
                    // 存在 SendLog，将 sendLog 的 content 覆盖到当前的 content
                    if (dailyReportSendLog != null) {
                        return toDailyReportConfigDef(dailyReportConfig, dailyReportSendLog);
                    } else {
                        return toDailyReportConfigDef(dailyReportConfig);
                    }
                })
                .toList();
    }

    default DailyReportConfigDef toDailyReportConfigDef(
            DailyReportConfig dailyReportConfig, DailyReportSendLog dailyReportSendLog) {
        DailyReportConfigDef dailyReportConfigDef = toDailyReportConfigDef(dailyReportConfig);
        if (!dailyReportConfigDef.getStatus().equals(ReportCardStatus.REPORT_CARD_SENT)
                || Objects.isNull(dailyReportSendLog)) {
            return dailyReportConfigDef;
        }

        // If the report is sent, we need to recover the send method and send time from the log
        return dailyReportConfigDef.toBuilder()
                .setSendMethod(dailyReportSendLog.getSendMethod())
                .setSendTime(TimeConverter.INSTANCE.map(dailyReportSendLog.getSendTime()))
                .setReport(ReportDef.newBuilder()
                        .setContent(DailyReportConverter.INSTANCE.toContentDef(dailyReportSendLog.getContentJson()))
                        .build())
                .build();
    }

    default DailyReportConfigDef toDailyReportConfigDef(DailyReportConfig dailyReportConfig) {
        return DailyReportConfigDef.newBuilder()
                .setId(dailyReportConfig.getId())
                .setCustomerId(dailyReportConfig.getCustomerId())
                .setAppointmentId(dailyReportConfig.getAppointmentId())
                .setPetId(dailyReportConfig.getPetId())
                .setUuid(dailyReportConfig.getUuid())
                .setReport(ReportDef.newBuilder()
                        .setContent(DailyReportConverter.INSTANCE.toContentDef(dailyReportConfig.getTemplateJson()))
                        .build())
                .setStatus(DailyReportConverter.INSTANCE.toStatus(dailyReportConfig.getStatus()))
                .setUpdateTime(TimeConverter.INSTANCE.map(dailyReportConfig.getUpdateTime()))
                .setServiceDate(DateConverter.INSTANCE.convertToGoogleDate(dailyReportConfig.getServiceDate()))
                .build();
    }

    default ReportCardStatus toStatus(String status) {
        return switch (status.toLowerCase()) {
            case "created" -> ReportCardStatus.REPORT_CARD_CREATED;
            case "draft" -> ReportCardStatus.REPORT_CARD_DRAFT;
            case "ready" -> ReportCardStatus.REPORT_CARD_READY;
            case "sent" -> ReportCardStatus.REPORT_CARD_SENT;
            default -> ReportCardStatus.REPORT_CARD_STATUS_UNSPECIFIED;
        };
    }

    default String toStatusEntityString(ReportCardStatus status) {
        return switch (status) {
            case REPORT_CARD_CREATED -> "created";
            case REPORT_CARD_DRAFT -> "draft";
            case REPORT_CARD_READY -> "ready";
            case REPORT_CARD_SENT -> "sent";
            default -> "";
        };
    }
}
