package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v1.InvoiceDepositModel;
import com.moego.idl.models.appointment.v1.InvoiceDepositStatus;
import com.moego.svc.appointment.domain.MoeInvoiceDeposit;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {StructConverter.class, TimeConverter.class})
public interface InvoiceDepositConverter {

    InvoiceDepositConverter INSTANCE = Mappers.getMapper(InvoiceDepositConverter.class);

    List<InvoiceDepositModel> toModel(List<MoeInvoiceDeposit> depositList);

    InvoiceDepositModel toModel(MoeInvoiceDeposit deposit);

    default InvoiceDepositStatus map(Byte value) {
        return InvoiceDepositStatus.forNumber(value.intValue());
    }
}
