/*
 * @since 2023-05-28 22:06:38
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.appointment.converter;

import static java.time.ZoneOffset.UTC;

import com.google.protobuf.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface TimeConverter {

    TimeConverter INSTANCE = Mappers.getMapper(TimeConverter.class);

    default LocalDateTime toLocalDateTime(Timestamp timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), UTC);
    }

    default Timestamp toTimestamp(LocalDateTime localDateTime) {
        return Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(UTC))
                .setNanos(localDateTime.getNano())
                .build();
    }

    default Timestamp toTimestamp(Long timestamp) {
        return Timestamp.newBuilder().setSeconds(timestamp).setNanos(0).build();
    }

    default Timestamp map(java.util.Date value) {
        return Timestamp.newBuilder()
                .setSeconds(value.getTime() / 1000)
                .setNanos(0)
                .build();
    }

    default com.google.type.Date toGoogleDate(LocalDate localDate) {
        return com.google.type.Date.newBuilder()
                .setYear(localDate.getYear())
                .setMonth(localDate.getMonthValue())
                .setDay(localDate.getDayOfMonth())
                .build();
    }

    default LocalDate toLocalDate(com.google.type.Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }
}
