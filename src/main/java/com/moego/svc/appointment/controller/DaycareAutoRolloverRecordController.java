package com.moego.svc.appointment.controller;

import static com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel.Status.FAILED;
import static com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel.Status.PENDING_VALUE;
import static com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel.Status.PROCESSING_VALUE;
import static com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel.Status.SUCCESS;
import static com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel.Status.SUCCESS_VALUE;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.enums.AppointmentAction.AUTO_ROLLOVER;
import static com.moego.svc.activitylog.event.enums.ResourceType.APPOINTMENT;
import static com.moego.svc.appointment.mapper.pg.DaycareAutoRolloverRecordDynamicSqlSupport.daycareAutoRolloverRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.DaycareAutoRolloverRecordModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.AutoRolloverRuleModel;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.BatchCreateDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.appointment.v1.BatchCreateDaycareAutoRolloverRecordByServiceIdResponse;
import com.moego.idl.service.appointment.v1.BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.appointment.v1.BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse;
import com.moego.idl.service.appointment.v1.CreateDaycareAutoRolloverRecordRequest;
import com.moego.idl.service.appointment.v1.CreateDaycareAutoRolloverRecordResponse;
import com.moego.idl.service.appointment.v1.DaycareAutoRolloverRecordServiceGrpc;
import com.moego.idl.service.appointment.v1.RefreshDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.appointment.v1.RefreshDaycareAutoRolloverRecordByServiceIdResponse;
import com.moego.idl.service.appointment.v1.RolloverDaycareAutoRolloverRecordRequest;
import com.moego.idl.service.appointment.v1.RolloverDaycareAutoRolloverRecordResponse;
import com.moego.idl.service.offering.v1.AutoRolloverRuleServiceGrpc.AutoRolloverRuleServiceBlockingStub;
import com.moego.idl.service.offering.v1.BatchGetAutoRolloverRuleRequest;
import com.moego.idl.service.offering.v1.GetApplicableServiceListRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.converter.DaycareAutoRolloverRecordConverter;
import com.moego.svc.appointment.domain.DaycareAutoRolloverRecord;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.mapper.pg.DaycareAutoRolloverRecordMapper;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.DaycareAutoRolloverRecordService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class DaycareAutoRolloverRecordController
        extends DaycareAutoRolloverRecordServiceGrpc.DaycareAutoRolloverRecordServiceImplBase {

    private final PetDetailServiceProxy petDetailService;
    private final AppointmentServiceProxy appointmentService;
    private final DaycareAutoRolloverRecordService daycareAutoRolloverRecordService;
    private final DaycareAutoRolloverRecordMapper daycareAutoRolloverRecordMapper;
    private final PricingRuleRecordApplyService pricingRuleRecordApplyService;
    private final OrderRemoteService orderRemoteService;

    private final AutoRolloverRuleServiceBlockingStub autoRolloverRuleStub;
    private final ServiceManagementServiceBlockingStub serviceService;

    private final TransactionOperations transactionOperations;

    @Override
    public void createDaycareAutoRolloverRecord(
            CreateDaycareAutoRolloverRecordRequest request,
            StreamObserver<CreateDaycareAutoRolloverRecordResponse> responseObserver) {

        long id = daycareAutoRolloverRecordService.insert(
                DaycareAutoRolloverRecordConverter.INSTANCE.createRequestToEntity(request));

        responseObserver.onNext(CreateDaycareAutoRolloverRecordResponse.newBuilder()
                .setRecord(
                        DaycareAutoRolloverRecordConverter.INSTANCE.entityToModel(mustGetDaycareAutoRolloverRecord(id)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchCreateDaycareAutoRolloverRecordByServiceId(
            BatchCreateDaycareAutoRolloverRecordByServiceIdRequest request,
            StreamObserver<BatchCreateDaycareAutoRolloverRecordByServiceIdResponse> responseObserver) {

        int affectedRows = daycareAutoRolloverRecordService.batchInsert(request.getServiceId());

        responseObserver.onNext(BatchCreateDaycareAutoRolloverRecordByServiceIdResponse.newBuilder()
                .setAffectedRows(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteDaycareAutoRolloverRecordByServiceId(
            BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest request,
            StreamObserver<BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse> responseObserver) {

        int affectedRows = daycareAutoRolloverRecordService.deleteByServiceId(request.getServiceId());

        responseObserver.onNext(BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse.newBuilder()
                .setAffectedRows(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void refreshDaycareAutoRolloverRecordByServiceId(
            RefreshDaycareAutoRolloverRecordByServiceIdRequest request,
            StreamObserver<RefreshDaycareAutoRolloverRecordByServiceIdResponse> responseObserver) {

        transactionOperations.executeWithoutResult(status -> {
            daycareAutoRolloverRecordService.deleteByServiceId(request.getServiceId());

            daycareAutoRolloverRecordService.batchInsert(request.getServiceId());
        });

        responseObserver.onNext(RefreshDaycareAutoRolloverRecordByServiceIdResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void rolloverDaycareAutoRolloverRecord(
            RolloverDaycareAutoRolloverRecordRequest request,
            StreamObserver<RolloverDaycareAutoRolloverRecordResponse> responseObserver) {

        // 这里使用 fetch and lock on execution + 乐观锁的方式避免并发问题
        // 理论上使用 lock and fetch (select for update) 可以得到更优的性能，但是我们使用了更简单的方式。
        // see https://github.com/kagkarlsson/db-scheduler?tab=readme-ov-file#performance

        var records = listPendingAutoRolloverRecord(request);

        var serviceIdToAutoRolloverRule = listAutoRolloverRule(records);

        for (var autoRolloverRecord : records) {
            rolloverAndUpdateStatus(
                    autoRolloverRecord, serviceIdToAutoRolloverRule.get(autoRolloverRecord.getServiceId()));
        }

        responseObserver.onNext(RolloverDaycareAutoRolloverRecordResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void applyPricingRule(MoeGroomingAppointment appointment) {
        pricingRuleRecordApplyService.applyPricingRule(
                appointment.getId().longValue(),
                appointment.getCompanyId(),
                appointment.getBusinessId().longValue());

        // Refresh order after applying pricing rule
        orderRemoteService.updateOrder(appointmentService.mustGet(appointment.getId()));
    }

    private void rolloverAndUpdateStatus(
            DaycareAutoRolloverRecord autoRolloverRecord, @Nullable AutoRolloverRuleModel autoRolloverRule) {
        if (autoRolloverRule == null
                || !autoRolloverRule.getEnabled()
                || !tryUpdateStatusToProcessing(autoRolloverRecord)) {
            return;
        }

        try {
            rollover(autoRolloverRecord, autoRolloverRule);
        } catch (Exception e) {
            log.error(
                    "Failed to rollover daycare, record: {}, rule: {}",
                    JsonUtil.toJson(autoRolloverRecord),
                    JsonUtil.toJson(autoRolloverRule),
                    e);

            updateStatus(autoRolloverRecord.getId(), FAILED);

            return;
        }

        updateStatus(autoRolloverRecord.getId(), SUCCESS);
    }

    private Map<Long, AutoRolloverRuleModel> listAutoRolloverRule(List<DaycareAutoRolloverRecord> records) {
        var serviceIds =
                records.stream().map(DaycareAutoRolloverRecord::getServiceId).collect(Collectors.toSet());
        return autoRolloverRuleStub
                .batchGetAutoRolloverRule(BatchGetAutoRolloverRuleRequest.newBuilder()
                        .addAllServiceIds(serviceIds)
                        .build())
                .getServiceIdToAutoRolloverRuleMap();
    }

    private void rollover(DaycareAutoRolloverRecord autoRolloverRecord, AutoRolloverRuleModel autoRolloverRule) {
        var petDetail = petDetailService.get(autoRolloverRecord.getDaycareServiceDetailId());
        if (petDetail == null) {
            return;
        }

        var appointment = appointmentService.get(petDetail.getGroomingId());
        if (appointment == null || !isInStoreStatus(appointment.getStatus())) {
            return;
        }
        boolean hasBoarding = ServiceItemEnum.BOARDING.isIncludedIn(appointment.getServiceTypeInclude());
        if (hasBoarding) {
            return;
        }

        var service = getService(
                appointment.getCompanyId(),
                appointment.getBusinessId(),
                autoRolloverRule.getTargetServiceId(),
                petDetail.getPetId());
        if (service == null) {
            return;
        }

        var associatedAddons = petDetailService.getByAssociatedId(petDetail.getGroomingId(), petDetail.getServiceId());

        petDetailService.autoRolloverServiceUpdate(service, petDetail, associatedAddons);

        applyPricingRule(appointment);

        ActivityLogRecorder.record(
                appointment.getBusinessId(),
                AUTO_ROLLOVER,
                APPOINTMENT,
                appointment.getId(),
                Map.of(
                        "sourceServiceId", autoRolloverRecord.getServiceId(),
                        "targetServiceId", autoRolloverRule.getTargetServiceId()));
    }

    @Nullable
    CustomizedServiceView getService(Number companyId, Number businessId, Number serviceId, Number petId) {
        return serviceService
                .getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                        .setCompanyId(companyId.longValue())
                        .setBusinessId(businessId.longValue())
                        .setServiceType(ServiceType.SERVICE)
                        .setServiceItemType(ServiceItemType.DAYCARE)
                        .setPetId(petId.longValue())
                        .setOnlyAvailable(false)
                        .setInactive(false)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getCategoryListList()
                .stream()
                .flatMap(e -> e.getServicesList().stream())
                .filter(e -> Objects.equals(e.getId(), serviceId.longValue()))
                .findFirst()
                .orElse(null);
    }

    private List<DaycareAutoRolloverRecord> listPendingAutoRolloverRecord(
            RolloverDaycareAutoRolloverRecordRequest request) {
        return daycareAutoRolloverRecordMapper.select(
                c -> c.where(daycareAutoRolloverRecord.status, isEqualTo(PENDING_VALUE))
                        .and(
                                daycareAutoRolloverRecord.rolloverTime,
                                isGreaterThanOrEqualTo(timestampToDate(request.getStart())))
                        .and(daycareAutoRolloverRecord.rolloverTime, isLessThan(timestampToDate(request.getEnd()))));
    }

    private void updateStatus(long daycareAutoRolloverRecordId, DaycareAutoRolloverRecordModel.Status status) {
        // spotless:off
        daycareAutoRolloverRecordMapper.update(c -> c
            .set(daycareAutoRolloverRecord.status).equalTo(status.getNumber())
            .where(daycareAutoRolloverRecord.id, isEqualTo(daycareAutoRolloverRecordId)));
        // spotless:on
    }

    private boolean tryUpdateStatusToProcessing(DaycareAutoRolloverRecord entity) {
        // spotless:off
        return daycareAutoRolloverRecordMapper.update(c -> c
            .set(daycareAutoRolloverRecord.status).equalTo(PROCESSING_VALUE)
            .where(daycareAutoRolloverRecord.id, isEqualTo(entity.getId()))
            .and(daycareAutoRolloverRecord.status, isEqualTo(PENDING_VALUE))) > 0;
        // spotless:on
    }

    private static Date timestampToDate(Timestamp timestamp) {
        return new Date(Timestamps.toMillis(timestamp));
    }

    private DaycareAutoRolloverRecord mustGetDaycareAutoRolloverRecord(long id) {
        return Optional.ofNullable(daycareAutoRolloverRecordService.get(id))
                .orElseThrow(() -> bizException(
                        Code.CODE_PARAMS_ERROR, "Failed to get daycare auto rollover record by id: " + id));
    }

    private static boolean isInStoreStatus(Number status) {
        return Objects.equals(status.intValue(), AppointmentStatus.CHECKED_IN_VALUE)
                || Objects.equals(status.intValue(), AppointmentStatus.READY_VALUE);
    }

    private List<DaycareAutoRolloverRecord> listSuccessAutoRolloverRecord(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return daycareAutoRolloverRecordMapper.select(
                c -> c.where(daycareAutoRolloverRecord.status, isEqualTo(SUCCESS_VALUE))
                        .and(daycareAutoRolloverRecord.id, isIn(ids)));
    }
}
