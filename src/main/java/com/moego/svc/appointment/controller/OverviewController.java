package com.moego.svc.appointment.controller;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.OverviewDateType;
import com.moego.idl.models.appointment.v1.OverviewStatus;
import com.moego.idl.service.appointment.v1.GetOverviewListRequest;
import com.moego.idl.service.appointment.v1.GetOverviewListResponse;
import com.moego.idl.service.appointment.v1.ListOverviewAppointmentRequest;
import com.moego.idl.service.appointment.v1.ListOverviewAppointmentResponse;
import com.moego.idl.service.appointment.v1.OverviewServiceGrpc;
import com.moego.idl.service.appointment.v1.OverviewStatusEntry;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.svc.appointment.converter.AppointmentConverter;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.service.OverviewService;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class OverviewController extends OverviewServiceGrpc.OverviewServiceImplBase {

    private final OverviewService overviewService;
    private final ICustomerCustomerClient customerClient;

    @Override
    public void getOverviewList(
            GetOverviewListRequest request, StreamObserver<GetOverviewListResponse> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = request.getCompanyId();
        String date = request.getDate();
        List<Integer> serviceItemTypesValueList = request.getServiceItemTypesValueList();
        OverviewDateType dateType = request.getDateType();

        Set<Integer> clientIdList = Set.of();
        if (request.hasKeyword() && StringUtils.hasText(request.getKeyword())) {
            SearchCustomerIdsParam param = new SearchCustomerIdsParam()
                    .setBusinessId(Math.toIntExact(businessId))
                    .setKeyword(request.getKeyword());
            clientIdList = customerClient.searchCustomerIds(param);
        }

        Map<OverviewStatus, List<MoeGroomingAppointment>> appointmentMap = overviewService.getAppointmentListOverview(
                companyId, businessId, date, serviceItemTypesValueList, clientIdList, dateType);

        List<OverviewStatusEntry> entries = appointmentMap.entrySet().stream()
                .map(entry -> OverviewStatusEntry.newBuilder()
                        .setStatus(entry.getKey())
                        .setCount(entry.getValue().size())
                        .addAllAppointmentOverviews(AppointmentConverter.INSTANCE.toOverviewList(entry.getValue()))
                        .build())
                .toList();

        responseObserver.onNext(
                GetOverviewListResponse.newBuilder().addAllEntries(entries).build());
        responseObserver.onCompleted();
    }

    @Override
    public void listOverviewAppointment(
            ListOverviewAppointmentRequest request, StreamObserver<ListOverviewAppointmentResponse> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = request.getCompanyId();
        LocalDate date = DateConverter.INSTANCE.fromGoogleDate(request.getDate());
        OverviewDateType dateType = request.getDateType();
        OverviewStatus overviewStatus = request.getOverviewStatus();

        List<Integer> serviceItemTypesValueList = request.getServiceItemTypesValueList();
        List<AppointmentStatus> appointmentStatusesList = request.getAppointmentStatusesList();
        List<Integer> customerIdList =
                request.getCustomerIdsList().stream().map(Long::intValue).toList();

        OverviewService.QueryParams queryParams = new OverviewService.QueryParams(
                companyId,
                businessId,
                date,
                dateType,
                overviewStatus,
                serviceItemTypesValueList,
                appointmentStatusesList,
                customerIdList);
        var appointments = overviewService.getAppointmentListOverview(queryParams);

        responseObserver.onNext(ListOverviewAppointmentResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toOverviewList(appointments))
                .build());
        responseObserver.onCompleted();
    }
}
