package com.moego.svc.appointment.config;

import com.moego.common.constant.ActiveMQConstant;
import jakarta.jms.Queue;
import jakarta.jms.Topic;
import org.apache.activemq.command.ActiveMQQueue;
import org.apache.activemq.command.ActiveMQTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class ActiveMQConfig {

    @Bean
    public Topic appointTopic() {
        return new ActiveMQTopic(ActiveMQConstant.GROOMING_APPOINTMENT_TOPIC);
    }

    @Bean
    public Queue bookingRequestQueue() {
        return new ActiveMQQueue(ActiveMQConstant.ONLINE_BOOKING_REQUEST_QUEUE);
    }
}
