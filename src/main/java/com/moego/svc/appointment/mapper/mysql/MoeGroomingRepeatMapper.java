package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingRepeatDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.MoeGroomingRepeat;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MoeGroomingRepeatMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MoeGroomingRepeatMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    BasicColumn[] selectList = BasicColumn.columnList(id, customerId, businessId, staffId, repeatType, repeatEvery, repeatBy, startsOn, times, createTime, updateTime, status, endOn, isNotice, setEndOn, repeatEveryType, monthDay, monthWeekTimes, monthWeekDay, type, ssFlag, ssBeforeDays, ssAfterDays, companyId, repeatByDays);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Integer.class)
    int insert(InsertStatementProvider<MoeGroomingRepeat> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MoeGroomingRepeatResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.INTEGER),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.INTEGER),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.INTEGER),
        @Result(column="repeat_type", property="repeatType", jdbcType=JdbcType.TINYINT),
        @Result(column="repeat_every", property="repeatEvery", jdbcType=JdbcType.INTEGER),
        @Result(column="repeat_by", property="repeatBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="starts_on", property="startsOn", jdbcType=JdbcType.DATE),
        @Result(column="times", property="times", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.BIGINT),
        @Result(column="status", property="status", jdbcType=JdbcType.TINYINT),
        @Result(column="end_on", property="endOn", jdbcType=JdbcType.CHAR),
        @Result(column="is_notice", property="isNotice", jdbcType=JdbcType.TINYINT),
        @Result(column="set_end_on", property="setEndOn", jdbcType=JdbcType.DATE),
        @Result(column="repeat_every_type", property="repeatEveryType", jdbcType=JdbcType.TINYINT),
        @Result(column="month_day", property="monthDay", jdbcType=JdbcType.TINYINT),
        @Result(column="month_week_times", property="monthWeekTimes", jdbcType=JdbcType.TINYINT),
        @Result(column="month_week_day", property="monthWeekDay", jdbcType=JdbcType.TINYINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="ss_flag", property="ssFlag", jdbcType=JdbcType.TINYINT),
        @Result(column="ss_before_days", property="ssBeforeDays", jdbcType=JdbcType.INTEGER),
        @Result(column="ss_after_days", property="ssAfterDays", jdbcType=JdbcType.INTEGER),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="repeat_by_days", property="repeatByDays", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MoeGroomingRepeat> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MoeGroomingRepeatResult")
    Optional<MoeGroomingRepeat> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default int insertSelective(MoeGroomingRepeat row) {
        return MyBatis3Utils.insert(this::insert, row, moeGroomingRepeat, c ->
            c.map(customerId).toPropertyWhenPresent("customerId", row::getCustomerId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(repeatType).toPropertyWhenPresent("repeatType", row::getRepeatType)
            .map(repeatEvery).toPropertyWhenPresent("repeatEvery", row::getRepeatEvery)
            .map(repeatBy).toPropertyWhenPresent("repeatBy", row::getRepeatBy)
            .map(startsOn).toPropertyWhenPresent("startsOn", row::getStartsOn)
            .map(times).toPropertyWhenPresent("times", row::getTimes)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(endOn).toPropertyWhenPresent("endOn", row::getEndOn)
            .map(isNotice).toPropertyWhenPresent("isNotice", row::getIsNotice)
            .map(setEndOn).toPropertyWhenPresent("setEndOn", row::getSetEndOn)
            .map(repeatEveryType).toPropertyWhenPresent("repeatEveryType", row::getRepeatEveryType)
            .map(monthDay).toPropertyWhenPresent("monthDay", row::getMonthDay)
            .map(monthWeekTimes).toPropertyWhenPresent("monthWeekTimes", row::getMonthWeekTimes)
            .map(monthWeekDay).toPropertyWhenPresent("monthWeekDay", row::getMonthWeekDay)
            .map(type).toPropertyWhenPresent("type", row::getType)
            .map(ssFlag).toPropertyWhenPresent("ssFlag", row::getSsFlag)
            .map(ssBeforeDays).toPropertyWhenPresent("ssBeforeDays", row::getSsBeforeDays)
            .map(ssAfterDays).toPropertyWhenPresent("ssAfterDays", row::getSsAfterDays)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(repeatByDays).toPropertyWhenPresent("repeatByDays", row::getRepeatByDays)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default Optional<MoeGroomingRepeat> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default List<MoeGroomingRepeat> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default List<MoeGroomingRepeat> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default Optional<MoeGroomingRepeat> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, moeGroomingRepeat, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    static UpdateDSL<UpdateModel> updateAllColumns(MoeGroomingRepeat row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(customerId).equalTo(row::getCustomerId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(staffId).equalTo(row::getStaffId)
                .set(repeatType).equalTo(row::getRepeatType)
                .set(repeatEvery).equalTo(row::getRepeatEvery)
                .set(repeatBy).equalTo(row::getRepeatBy)
                .set(startsOn).equalTo(row::getStartsOn)
                .set(times).equalTo(row::getTimes)
                .set(createTime).equalTo(row::getCreateTime)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(status).equalTo(row::getStatus)
                .set(endOn).equalTo(row::getEndOn)
                .set(isNotice).equalTo(row::getIsNotice)
                .set(setEndOn).equalTo(row::getSetEndOn)
                .set(repeatEveryType).equalTo(row::getRepeatEveryType)
                .set(monthDay).equalTo(row::getMonthDay)
                .set(monthWeekTimes).equalTo(row::getMonthWeekTimes)
                .set(monthWeekDay).equalTo(row::getMonthWeekDay)
                .set(type).equalTo(row::getType)
                .set(ssFlag).equalTo(row::getSsFlag)
                .set(ssBeforeDays).equalTo(row::getSsBeforeDays)
                .set(ssAfterDays).equalTo(row::getSsAfterDays)
                .set(companyId).equalTo(row::getCompanyId)
                .set(repeatByDays).equalTo(row::getRepeatByDays);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MoeGroomingRepeat row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(customerId).equalToWhenPresent(row::getCustomerId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(repeatType).equalToWhenPresent(row::getRepeatType)
                .set(repeatEvery).equalToWhenPresent(row::getRepeatEvery)
                .set(repeatBy).equalToWhenPresent(row::getRepeatBy)
                .set(startsOn).equalToWhenPresent(row::getStartsOn)
                .set(times).equalToWhenPresent(row::getTimes)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(endOn).equalToWhenPresent(row::getEndOn)
                .set(isNotice).equalToWhenPresent(row::getIsNotice)
                .set(setEndOn).equalToWhenPresent(row::getSetEndOn)
                .set(repeatEveryType).equalToWhenPresent(row::getRepeatEveryType)
                .set(monthDay).equalToWhenPresent(row::getMonthDay)
                .set(monthWeekTimes).equalToWhenPresent(row::getMonthWeekTimes)
                .set(monthWeekDay).equalToWhenPresent(row::getMonthWeekDay)
                .set(type).equalToWhenPresent(row::getType)
                .set(ssFlag).equalToWhenPresent(row::getSsFlag)
                .set(ssBeforeDays).equalToWhenPresent(row::getSsBeforeDays)
                .set(ssAfterDays).equalToWhenPresent(row::getSsAfterDays)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(repeatByDays).equalToWhenPresent(row::getRepeatByDays);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    default int updateByPrimaryKeySelective(MoeGroomingRepeat row) {
        return update(c ->
            c.set(customerId).equalToWhenPresent(row::getCustomerId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(repeatType).equalToWhenPresent(row::getRepeatType)
            .set(repeatEvery).equalToWhenPresent(row::getRepeatEvery)
            .set(repeatBy).equalToWhenPresent(row::getRepeatBy)
            .set(startsOn).equalToWhenPresent(row::getStartsOn)
            .set(times).equalToWhenPresent(row::getTimes)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(endOn).equalToWhenPresent(row::getEndOn)
            .set(isNotice).equalToWhenPresent(row::getIsNotice)
            .set(setEndOn).equalToWhenPresent(row::getSetEndOn)
            .set(repeatEveryType).equalToWhenPresent(row::getRepeatEveryType)
            .set(monthDay).equalToWhenPresent(row::getMonthDay)
            .set(monthWeekTimes).equalToWhenPresent(row::getMonthWeekTimes)
            .set(monthWeekDay).equalToWhenPresent(row::getMonthWeekDay)
            .set(type).equalToWhenPresent(row::getType)
            .set(ssFlag).equalToWhenPresent(row::getSsFlag)
            .set(ssBeforeDays).equalToWhenPresent(row::getSsBeforeDays)
            .set(ssAfterDays).equalToWhenPresent(row::getSsAfterDays)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(repeatByDays).equalToWhenPresent(row::getRepeatByDays)
            .where(id, isEqualTo(row::getId))
        );
    }
}