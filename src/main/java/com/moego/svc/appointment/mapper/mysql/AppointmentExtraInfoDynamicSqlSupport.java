package com.moego.svc.appointment.mapper.mysql;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentExtraInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    public static final AppointmentExtraInfo appointmentExtraInfo = new AppointmentExtraInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.id")
    public static final SqlColumn<Long> id = appointmentExtraInfo.id;

    /**
     * Database Column Remarks:
     *   The appointment ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentExtraInfo.appointmentId;

    /**
     * Database Column Remarks:
     *   Whether the appointment was created by a new order process
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_extra_info.is_new_order")
    public static final SqlColumn<Boolean> isNewOrder = appointmentExtraInfo.isNewOrder;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    public static final class AppointmentExtraInfo extends AliasableSqlTable<AppointmentExtraInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> isNewOrder = column("is_new_order", JDBCType.BIT);

        public AppointmentExtraInfo() {
            super("appointment_extra_info", AppointmentExtraInfo::new);
        }
    }
}