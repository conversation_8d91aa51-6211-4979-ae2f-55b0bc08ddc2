package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.ServiceChargeDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.ServiceChargeDetail;
import com.moego.svc.appointment.mapper.typehandler.ServiceOverrideTypeTypeHandler;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ServiceChargeDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<ServiceChargeDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, appointmentId, serviceChargeId, price, currency, priceOverrideType, taxId, createdAt, updatedAt, deletedAt, orderLineItemId, totalPrice, quantity);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ServiceChargeDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ServiceChargeDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_charge_id", property="serviceChargeId", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.DECIMAL),
        @Result(column="currency", property="currency", jdbcType=JdbcType.VARCHAR),
        @Result(column="price_override_type", property="priceOverrideType", typeHandler=ServiceOverrideTypeTypeHandler.class, jdbcType=JdbcType.TINYINT),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="order_line_item_id", property="orderLineItemId", jdbcType=JdbcType.BIGINT),
        @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="quantity", property="quantity", jdbcType=JdbcType.INTEGER)
    })
    List<ServiceChargeDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ServiceChargeDetailResult")
    Optional<ServiceChargeDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default int insertSelective(ServiceChargeDetail row) {
        return MyBatis3Utils.insert(this::insert, row, serviceChargeDetail, c ->
            c.map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(serviceChargeId).toPropertyWhenPresent("serviceChargeId", row::getServiceChargeId)
            .map(price).toPropertyWhenPresent("price", row::getPrice)
            .map(currency).toPropertyWhenPresent("currency", row::getCurrency)
            .map(priceOverrideType).toPropertyWhenPresent("priceOverrideType", row::getPriceOverrideType)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(orderLineItemId).toPropertyWhenPresent("orderLineItemId", row::getOrderLineItemId)
            .map(totalPrice).toPropertyWhenPresent("totalPrice", row::getTotalPrice)
            .map(quantity).toPropertyWhenPresent("quantity", row::getQuantity)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default Optional<ServiceChargeDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default List<ServiceChargeDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default List<ServiceChargeDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default Optional<ServiceChargeDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, serviceChargeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(ServiceChargeDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(appointmentId).equalTo(row::getAppointmentId)
                .set(serviceChargeId).equalTo(row::getServiceChargeId)
                .set(price).equalTo(row::getPrice)
                .set(currency).equalTo(row::getCurrency)
                .set(priceOverrideType).equalTo(row::getPriceOverrideType)
                .set(taxId).equalTo(row::getTaxId)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(orderLineItemId).equalTo(row::getOrderLineItemId)
                .set(totalPrice).equalTo(row::getTotalPrice)
                .set(quantity).equalTo(row::getQuantity);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ServiceChargeDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(serviceChargeId).equalToWhenPresent(row::getServiceChargeId)
                .set(price).equalToWhenPresent(row::getPrice)
                .set(currency).equalToWhenPresent(row::getCurrency)
                .set(priceOverrideType).equalToWhenPresent(row::getPriceOverrideType)
                .set(taxId).equalToWhenPresent(row::getTaxId)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(orderLineItemId).equalToWhenPresent(row::getOrderLineItemId)
                .set(totalPrice).equalToWhenPresent(row::getTotalPrice)
                .set(quantity).equalToWhenPresent(row::getQuantity);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    default int updateByPrimaryKeySelective(ServiceChargeDetail row) {
        return update(c ->
            c.set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(serviceChargeId).equalToWhenPresent(row::getServiceChargeId)
            .set(price).equalToWhenPresent(row::getPrice)
            .set(currency).equalToWhenPresent(row::getCurrency)
            .set(priceOverrideType).equalToWhenPresent(row::getPriceOverrideType)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(orderLineItemId).equalToWhenPresent(row::getOrderLineItemId)
            .set(totalPrice).equalToWhenPresent(row::getTotalPrice)
            .set(quantity).equalToWhenPresent(row::getQuantity)
            .where(id, isEqualTo(row::getId))
        );
    }
}