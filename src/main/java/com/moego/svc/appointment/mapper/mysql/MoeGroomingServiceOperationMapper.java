package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.MoeGroomingServiceOperationDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MoeGroomingServiceOperationMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MoeGroomingServiceOperationMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, groomingId, groomingServiceId, petId, staffId, operationName, startTime, duration, comment, price, priceRatio, status, createTime, updateTime, companyId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MoeGroomingServiceOperation> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MoeGroomingServiceOperationResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.INTEGER),
        @Result(column="grooming_id", property="groomingId", jdbcType=JdbcType.INTEGER),
        @Result(column="grooming_service_id", property="groomingServiceId", jdbcType=JdbcType.INTEGER),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.INTEGER),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.INTEGER),
        @Result(column="operation_name", property="operationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="comment", property="comment", jdbcType=JdbcType.VARCHAR),
        @Result(column="price", property="price", jdbcType=JdbcType.DECIMAL),
        @Result(column="price_ratio", property="priceRatio", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.BIT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT)
    })
    List<MoeGroomingServiceOperation> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MoeGroomingServiceOperationResult")
    Optional<MoeGroomingServiceOperation> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default int insertSelective(MoeGroomingServiceOperation row) {
        return MyBatis3Utils.insert(this::insert, row, moeGroomingServiceOperation, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(groomingId).toPropertyWhenPresent("groomingId", row::getGroomingId)
            .map(groomingServiceId).toPropertyWhenPresent("groomingServiceId", row::getGroomingServiceId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(operationName).toPropertyWhenPresent("operationName", row::getOperationName)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(comment).toPropertyWhenPresent("comment", row::getComment)
            .map(price).toPropertyWhenPresent("price", row::getPrice)
            .map(priceRatio).toPropertyWhenPresent("priceRatio", row::getPriceRatio)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default Optional<MoeGroomingServiceOperation> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default List<MoeGroomingServiceOperation> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default List<MoeGroomingServiceOperation> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default Optional<MoeGroomingServiceOperation> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, moeGroomingServiceOperation, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    static UpdateDSL<UpdateModel> updateAllColumns(MoeGroomingServiceOperation row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(groomingId).equalTo(row::getGroomingId)
                .set(groomingServiceId).equalTo(row::getGroomingServiceId)
                .set(petId).equalTo(row::getPetId)
                .set(staffId).equalTo(row::getStaffId)
                .set(operationName).equalTo(row::getOperationName)
                .set(startTime).equalTo(row::getStartTime)
                .set(duration).equalTo(row::getDuration)
                .set(comment).equalTo(row::getComment)
                .set(price).equalTo(row::getPrice)
                .set(priceRatio).equalTo(row::getPriceRatio)
                .set(status).equalTo(row::getStatus)
                .set(createTime).equalTo(row::getCreateTime)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(companyId).equalTo(row::getCompanyId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MoeGroomingServiceOperation row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(groomingId).equalToWhenPresent(row::getGroomingId)
                .set(groomingServiceId).equalToWhenPresent(row::getGroomingServiceId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(operationName).equalToWhenPresent(row::getOperationName)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(comment).equalToWhenPresent(row::getComment)
                .set(price).equalToWhenPresent(row::getPrice)
                .set(priceRatio).equalToWhenPresent(row::getPriceRatio)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(companyId).equalToWhenPresent(row::getCompanyId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_service_operation")
    default int updateByPrimaryKeySelective(MoeGroomingServiceOperation row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(groomingId).equalToWhenPresent(row::getGroomingId)
            .set(groomingServiceId).equalToWhenPresent(row::getGroomingServiceId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(operationName).equalToWhenPresent(row::getOperationName)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(comment).equalToWhenPresent(row::getComment)
            .set(price).equalToWhenPresent(row::getPrice)
            .set(priceRatio).equalToWhenPresent(row::getPriceRatio)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .where(id, isEqualTo(row::getId))
        );
    }
}