package com.moego.svc.appointment.mapper.pg;

import com.moego.idl.models.appointment.v1.CheckInAlertSettings;
import com.moego.idl.models.appointment.v1.CheckOutAlertSettings;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class CheckInOutAlertSettingsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    public static final CheckInOutAlertSettings checkInOutAlertSettings = new CheckInOutAlertSettings();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.id")
    public static final SqlColumn<Long> id = checkInOutAlertSettings.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.company_id")
    public static final SqlColumn<Long> companyId = checkInOutAlertSettings.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.check_in")
    public static final SqlColumn<CheckInAlertSettings> checkIn = checkInOutAlertSettings.checkIn;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.check_out")
    public static final SqlColumn<CheckOutAlertSettings> checkOut = checkInOutAlertSettings.checkOut;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = checkInOutAlertSettings.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: check_in_out_alert_settings.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = checkInOutAlertSettings.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: check_in_out_alert_settings")
    public static final class CheckInOutAlertSettings extends AliasableSqlTable<CheckInOutAlertSettings> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<CheckInAlertSettings> checkIn = column("check_in", JDBCType.OTHER, "com.moego.svc.appointment.mapper.typehandler.CheckInAlertSettingsHandler");

        public final SqlColumn<CheckOutAlertSettings> checkOut = column("check_out", JDBCType.OTHER, "com.moego.svc.appointment.mapper.typehandler.CheckOutAlertSettingsHandler");

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public CheckInOutAlertSettings() {
            super("check_in_out_alert_settings", CheckInOutAlertSettings::new);
        }
    }
}