package com.moego.svc.appointment.mapper.pg;

import com.moego.idl.models.offering.v1.PricingRuleModel;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class PricingRuleApplyLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    public static final PricingRuleApplyLog pricingRuleApplyLog = new PricingRuleApplyLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.id")
    public static final SqlColumn<Long> id = pricingRuleApplyLog.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.business_id")
    public static final SqlColumn<Long> businessId = pricingRuleApplyLog.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.company_id")
    public static final SqlColumn<Long> companyId = pricingRuleApplyLog.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.appointment_id")
    public static final SqlColumn<Long> appointmentId = pricingRuleApplyLog.appointmentId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.pet_id")
    public static final SqlColumn<Long> petId = pricingRuleApplyLog.petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.service_id")
    public static final SqlColumn<Long> serviceId = pricingRuleApplyLog.serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.original_price")
    public static final SqlColumn<BigDecimal> originalPrice = pricingRuleApplyLog.originalPrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = pricingRuleApplyLog.servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.pricing_rule")
    public static final SqlColumn<PricingRuleModel> pricingRule = pricingRuleApplyLog.pricingRule;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.created_at")
    public static final SqlColumn<Date> createdAt = pricingRuleApplyLog.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.deleted_at")
    public static final SqlColumn<Date> deletedAt = pricingRuleApplyLog.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.service_date")
    public static final SqlColumn<String> serviceDate = pricingRuleApplyLog.serviceDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.rule_group_type")
    public static final SqlColumn<Integer> ruleGroupType = pricingRuleApplyLog.ruleGroupType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.is_using_rule")
    public static final SqlColumn<Boolean> isUsingRule = pricingRuleApplyLog.isUsingRule;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.source_id")
    public static final SqlColumn<Long> sourceId = pricingRuleApplyLog.sourceId;

    /**
     * Database Column Remarks:
     *   source type, 1-appointment, 2-booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_apply_log.source_type")
    public static final SqlColumn<Integer> sourceType = pricingRuleApplyLog.sourceType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    public static final class PricingRuleApplyLog extends AliasableSqlTable<PricingRuleApplyLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> originalPrice = column("original_price", JDBCType.NUMERIC);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<PricingRuleModel> pricingRule = column("pricing_rule", JDBCType.OTHER, "com.moego.svc.appointment.mapper.typehandler.PricingRuleHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> serviceDate = column("service_date", JDBCType.VARCHAR);

        public final SqlColumn<Integer> ruleGroupType = column("rule_group_type", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isUsingRule = column("is_using_rule", JDBCType.BIT);

        public final SqlColumn<Long> sourceId = column("source_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> sourceType = column("source_type", JDBCType.INTEGER);

        public PricingRuleApplyLog() {
            super("pricing_rule_apply_log", PricingRuleApplyLog::new);
        }
    }
}