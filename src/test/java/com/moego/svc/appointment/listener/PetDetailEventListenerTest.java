package com.moego.svc.appointment.listener;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.offering.v1.AutoRolloverRuleModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.offering.v1.AutoRolloverRuleServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetAutoRolloverRuleRequest;
import com.moego.idl.service.offering.v1.BatchGetAutoRolloverRuleResponse;
import com.moego.svc.appointment.domain.DaycareAutoRolloverRecord;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.listener.event.PetDetailEvent;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.DaycareAutoRolloverRecordService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link PetDetailEventListener}
 */
@ExtendWith(MockitoExtension.class)
class PetDetailEventListenerTest {

    @Mock
    AppointmentServiceProxy appointmentService;

    @Mock
    DaycareAutoRolloverRecordService daycareAutoRolloverRecordService;

    @Mock
    AutoRolloverRuleServiceGrpc.AutoRolloverRuleServiceBlockingStub autoRolloverRuleStub;

    @Mock
    OrderRemoteService orderRemoteService;

    @Mock
    ServiceOperationService serviceOperationService;

    @InjectMocks
    PetDetailEventListener listener;

    /**
     * {@link PetDetailEventListener#handleCreatedEvent(PetDetailEvent.Created)}
     */
    @Test
    void handleCreatedEvent_whenMetAllConditions() {
        // Mock
        var petDetail = mock(MoeGroomingPetDetail.class);
        when(petDetail.getServiceItemType()).thenReturn((byte) ServiceItemType.DAYCARE_VALUE);
        when(petDetail.getServiceId()).thenReturn(123);
        when(petDetail.getGroomingId()).thenReturn(1);

        var autoRolloverRule = mock(AutoRolloverRuleModel.class);
        when(autoRolloverRule.getEnabled()).thenReturn(true);

        var response = BatchGetAutoRolloverRuleResponse.newBuilder()
                .putAllServiceIdToAutoRolloverRule(Map.of(123L, autoRolloverRule))
                .build();
        when(autoRolloverRuleStub.batchGetAutoRolloverRule(any(BatchGetAutoRolloverRuleRequest.class)))
                .thenReturn(response);

        var appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getStatus()).thenReturn((byte) AppointmentStatus.CHECKED_IN_VALUE);
        when(appointmentService.mustGet(anyLong())).thenReturn(appointment);

        // Act
        listener.handleCreatedEvent(new PetDetailEvent.Created(petDetail));

        // Assert
        await().atMost(2, TimeUnit.SECONDS).untilAsserted(() -> verify(daycareAutoRolloverRecordService, times(1))
                .insert(any(DaycareAutoRolloverRecord.class)));
    }

    /**
     * {@link PetDetailEventListener#handleUpdatedEvent(PetDetailEvent.Updated)}
     */
    @Test
    void handleUpdatedEvent_whenMetAllConditions() {
        // Mock
        var before = mock(MoeGroomingPetDetail.class);
        var after = mock(MoeGroomingPetDetail.class);

        var petDetailId = 100;
        when(before.getId()).thenReturn(petDetailId);
        when(after.getId()).thenReturn(petDetailId);
        when(before.getServiceId()).thenReturn(1);
        when(after.getServiceId()).thenReturn(2);
        when(after.getGroomingId()).thenReturn(1);
        when(before.getServiceItemType()).thenReturn((byte) ServiceItemType.DAYCARE_VALUE);
        when(after.getServiceItemType()).thenReturn((byte) ServiceItemType.DAYCARE_VALUE);

        var appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getStatus()).thenReturn((byte) AppointmentStatus.CHECKED_IN_VALUE);
        when(appointmentService.mustGet(1)).thenReturn(appointment);

        var autoRolloverRule = mock(AutoRolloverRuleModel.class);
        when(autoRolloverRule.getEnabled()).thenReturn(true);
        var response = BatchGetAutoRolloverRuleResponse.newBuilder()
                .putServiceIdToAutoRolloverRule(1L, autoRolloverRule)
                .putServiceIdToAutoRolloverRule(2L, autoRolloverRule)
                .build();
        when(autoRolloverRuleStub.batchGetAutoRolloverRule(any(BatchGetAutoRolloverRuleRequest.class)))
                .thenReturn(response);

        var order = mock(OrderModel.class);
        when(order.hasId()).thenReturn(Boolean.TRUE);
        when(order.getId()).thenReturn(1L);
        when(order.getOrderType()).thenReturn(OrderModel.OrderType.ORIGIN);
        when(orderRemoteService.getLatestOrderByAppointmentId(1)).thenReturn(order);

        when(daycareAutoRolloverRecordService.deleteByPetDetailId(petDetailId)).thenReturn(1);
        when(daycareAutoRolloverRecordService.insert(any(DaycareAutoRolloverRecord.class)))
                .thenReturn(1L);

        when(serviceOperationService.listByPetDetailIds(List.of(petDetailId))).thenReturn(Map.of());

        // Act
        listener.handleUpdatedEvent(new PetDetailEvent.Updated(before, after, true));

        // Assert
        await().atMost(2, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(daycareAutoRolloverRecordService, times(1)).deleteByPetDetailId(before.getId());
            verify(daycareAutoRolloverRecordService, times(1)).insert(any(DaycareAutoRolloverRecord.class));
        });
        verify(orderRemoteService, times(1)).updateOrder(any());
        verify(serviceOperationService, never()).reassignOperationTime(any(), any(), any());
        verify(serviceOperationService, never()).reassignOperationPrice(any(), any(), any());
    }

    /**
     * {@link PetDetailEventListener#handleUpdatedEvent(PetDetailEvent.Updated)} )}
     */
    @Test
    void handleUpdatedEvent_whenPriceNotChange_thenNotTriggerUpdate() {
        // Mock
        var before = mock(MoeGroomingPetDetail.class);
        var after = mock(MoeGroomingPetDetail.class);
        when(before.getServiceId()).thenReturn(1);
        when(after.getServiceId()).thenReturn(1);
        when(before.getServicePrice()).thenReturn(new BigDecimal("100"));
        when(after.getServicePrice()).thenReturn(new BigDecimal("100"));

        // Act
        listener.handleUpdatedEvent(new PetDetailEvent.Updated(before, after, true));

        // Assert
        verify(orderRemoteService, never()).updateOrder(any());
    }

    /**
     * {@link PetDetailEventListener#handleDeletedEvent(PetDetailEvent.Deleted)}
     */
    @Test
    void handleDeletedEvent_whenMetAllConditions() {
        // Mock
        var petDetail = mock(MoeGroomingPetDetail.class);
        when(petDetail.getServiceItemType()).thenReturn((byte) ServiceItemType.DAYCARE_VALUE);
        when(petDetail.getId()).thenReturn(123);
        when(petDetail.getGroomingId()).thenReturn(1);

        var appointment = mock(MoeGroomingAppointment.class);
        when(appointmentService.mustGet(1)).thenReturn(appointment);

        var order = mock(OrderModel.class);
        when(order.getId()).thenReturn(1L);
        when(order.getOrderType()).thenReturn(OrderModel.OrderType.EXTRA);
        when(orderRemoteService.getLatestOrderByAppointmentId(1)).thenReturn(order);

        // Act
        listener.handleDeletedEvent(new PetDetailEvent.Deleted(petDetail, true));

        // Assert
        verify(orderRemoteService, times(1)).updateOrder(appointment);
        verify(orderRemoteService, times(1)).updateExtraOrder(any());

        await().atMost(2, TimeUnit.SECONDS).untilAsserted(() -> verify(daycareAutoRolloverRecordService, times(1))
                .deleteByPetDetailId(petDetail.getId()));
    }

    /**
     * {@link PetDetailEventListener#handleDeletedEvent(PetDetailEvent.Deleted)}
     */
    @Test
    void handleDeletedEvent_whenSetShouldUpdateOrderToFalse_thenNotUpdateOrder() {
        // Mock
        var petDetail = mock(MoeGroomingPetDetail.class);
        when(petDetail.getGroomingId()).thenReturn(1);

        var appointment = mock(MoeGroomingAppointment.class);
        when(appointmentService.mustGet(1)).thenReturn(appointment);

        var order = mock(OrderModel.class);
        when(order.getId()).thenReturn(1L);
        when(order.getOrderType()).thenReturn(OrderModel.OrderType.EXTRA);
        when(orderRemoteService.getLatestOrderByAppointmentId(1)).thenReturn(order);

        // Act
        listener.handleDeletedEvent(new PetDetailEvent.Deleted(petDetail, false));

        // Assert
        verify(orderRemoteService, never()).updateOrder(any());
        verify(orderRemoteService, times(1)).updateExtraOrder(any()); // still update extra order
    }

    /**
     * {@link PetDetailEventListener#handleDeletedEvent(PetDetailEvent.Deleted)}
     */
    @Test
    void handleDeletedEvent_whenSetShouldUpdateOrderToTrue_thenUpdateOrder() {
        // Mock
        var petDetail = mock(MoeGroomingPetDetail.class);
        when(petDetail.getGroomingId()).thenReturn(1);

        var appointment = mock(MoeGroomingAppointment.class);
        when(appointmentService.mustGet(1)).thenReturn(appointment);

        var order = mock(OrderModel.class);
        when(order.getId()).thenReturn(1L);
        when(order.getOrderType()).thenReturn(OrderModel.OrderType.EXTRA);
        when(orderRemoteService.getLatestOrderByAppointmentId(1)).thenReturn(order);

        // Act
        listener.handleDeletedEvent(new PetDetailEvent.Deleted(petDetail, true));

        // Assert
        verify(orderRemoteService, times(1)).updateOrder(any());
        verify(orderRemoteService, times(1)).updateExtraOrder(any());
    }
}
