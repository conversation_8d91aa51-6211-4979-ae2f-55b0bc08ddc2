package com.moego.svc.appointment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.helper.FeatureFlagHelper;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailMapper;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.utils.Pair;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link PetDetailService}
 */
@ExtendWith(MockitoExtension.class)
class PetDetailServiceTest {

    @Mock
    ServiceOperationService serviceOperationService;

    @Mock
    AppointmentPetFeedingService petFeedingService;

    @Mock
    AppointmentPetMedicationService petMedicationService;

    @Mock
    MoeGroomingPetDetailMapper petDetailMapper;

    @Mock
    FeatureFlagHelper featureFlagHelper;

    @Mock
    OfferingRemoteService offeringRemoteService;

    @Mock
    ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    @InjectMocks
    PetDetailService petDetailService;

    @Mock
    private CompanyRemoteService companyRemoteService;

    @Mock
    private BoardingSplitLodgingService boardingSplitLodgingService;

    private static final long COMPANY_ID = 1L;

    /**
     * {@link PetDetailService#deletePetDetails(Long, Long, List)}
     * {@link PetDetailService#deletePetDetails(Long, Long, List, boolean)}
     */
    @Test
    void deletePetDetails_whenAnyCondition_thenServiceOperationAndPetFeedingAndPetMedicationMustBeDeleted() {

        // Mock
        when(petDetailMapper.selectMany(any())).thenReturn(List.of());
        doNothing().when(boardingSplitLodgingService).deleteByPetIds(any(), any());

        // Act
        petDetailService.deletePetDetails(1L, 1L, List.of(1L, 2L, 3L));

        // Assert
        verify(serviceOperationService, times(1)).deleteByPetIds(any(), anyList());
        verify(petFeedingService, times(1)).deleteByPetIds(any(), any(), anyList());
        verify(petMedicationService, times(1)).deleteByPetIds(any(), any(), anyList());
    }

    /**
     * {@link PetDetailService#upsertAllInOnePetDetail(MoeGroomingAppointment, List, List)}
     */
    @Test
    void
            upsertAllInOnePetDetail_whenParamsAreEmpty_thenStillTryToDeleteServiceOperationAndPetFeedingAndPetMedication() {

        // Mock
        var appointment = mock(MoeGroomingAppointment.class);
        when(appointment.getId()).thenReturn(1);
        when(appointment.getCompanyId()).thenReturn(1L);
        when(appointment.getBusinessId()).thenReturn(1);

        when(offeringRemoteService.listService(any(), any(), anyList())).thenReturn(Map.of());
        when(serviceStub.batchGetCustomizedService(any()))
                .thenReturn(BatchGetCustomizedServiceResponse.getDefaultInstance());
        when(petDetailMapper.selectMany(any())).thenReturn(List.of());
        doNothing().when(boardingSplitLodgingService).deleteByPetIds(any(), any());
        when(featureFlagHelper.isNewOrderFlow(anyLong())).thenReturn(false);

        // Act
        petDetailService.upsertAllInOnePetDetail(
                appointment, List.of(), List.of(PetDetailDef.newBuilder().build()));

        // Assert
        verify(serviceOperationService, times(1)).deleteByPetIds(any(), anyList());
        verify(petFeedingService, times(1)).deleteByPetIds(any(), any(), anyList());
        verify(petMedicationService, times(1)).deleteByPetIds(any(), any(), anyList());
    }

    @Test
    void buildAddOnPetDetailDTO_DatePointAddOn() {
        // Arrange
        var petId = 100L;
        var addOnId = 1;
        var staffId = 10;
        var price = 100d;
        var selectedAddOnDef = SelectedAddOnDef.newBuilder()
                .setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .setAddOnId(addOnId)
                .setStartDate("2024-10-28")
                .setStartTime(540)
                .setStaffId(staffId)
                .setServiceTime(20)
                .setServicePrice(price)
                .build();

        var addOn = CustomizedServiceView.newBuilder()
                .setId(1)
                .setDuration(20)
                .setType(ServiceType.ADDON)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .setRequireDedicatedStaff(true)
                .build();

        try (var utilities = Mockito.mockStatic(DateUtil.class)) {
            var timestamp = DateUtil.get10Timestamp();
            utilities.when(DateUtil::get10Timestamp).thenReturn(timestamp);

            // Act
            var result = petDetailService.buildAddOnPetDetailDTO(null, null, petId, selectedAddOnDef, addOn);

            // Assert
            var petDetail = new MoeGroomingPetDetail();
            petDetail.setPetId((int) petId);
            petDetail.setServiceId(addOnId);
            petDetail.setStaffId(staffId);
            petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
            petDetail.setServiceType(ServiceType.ADDON_VALUE);
            petDetail.setStartDate("2024-10-28");
            petDetail.setStartTime(540L);
            petDetail.setServiceTime(20);
            petDetail.setEndDate("2024-10-28");
            petDetail.setEndTime(560L);
            petDetail.setServicePrice(BigDecimal.valueOf(price));
            petDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
            petDetail.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setScopeTypeTime(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setUpdateTime(timestamp);
            petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);
            petDetail.setQuantityPerDay(1);
            var expected = new PetDetailDTO()
                    .setPetId(petId)
                    .setPetDetail(petDetail)
                    .setOperations(List.of())
                    .setService(addOn);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void buildAddOnPetDetailDTO_SpecificDateAddOn() {
        // Arrange
        var petId = 100L;
        var addOnId = 1;
        var price = 100d;
        var selectedAddOnDef = SelectedAddOnDef.newBuilder()
                .setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .setAddOnId(addOnId)
                .addSpecificDates("2024-10-28")
                .addSpecificDates("2024-10-29")
                .setServiceTime(20)
                .setServicePrice(price)
                .setQuantityPerDay(2)
                .build();

        var addOn = CustomizedServiceView.newBuilder()
                .setId(1)
                .setDuration(20)
                .setType(ServiceType.ADDON)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .build();

        try (var utilities = Mockito.mockStatic(DateUtil.class)) {
            var timestamp = DateUtil.get10Timestamp();
            utilities.when(DateUtil::get10Timestamp).thenReturn(timestamp);

            // Act
            var result = petDetailService.buildAddOnPetDetailDTO(null, null, petId, selectedAddOnDef, addOn);

            // Assert
            var petDetail = new MoeGroomingPetDetail();
            petDetail.setPetId((int) petId);
            petDetail.setServiceId(addOnId);
            petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
            petDetail.setServiceType(ServiceType.ADDON_VALUE);
            petDetail.setSpecificDates("[\"2024-10-28\",\"2024-10-29\"]");
            petDetail.setQuantityPerDay(2);
            petDetail.setServiceTime(20);
            petDetail.setServicePrice(BigDecimal.valueOf(price));
            petDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
            petDetail.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setScopeTypeTime(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setUpdateTime(timestamp);
            petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
            var expected = new PetDetailDTO()
                    .setPetId(petId)
                    .setPetDetail(petDetail)
                    .setOperations(List.of())
                    .setService(addOn);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void buildAddOnPetDetailDTO_EverydayAddOn() {
        // Arrange
        var petId = 100L;
        var addOnId = 1;
        var price = 100d;
        var selectedAddOnDef = SelectedAddOnDef.newBuilder()
                .setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                .setAddOnId(addOnId)
                .setServiceTime(20)
                .setServicePrice(price)
                .setQuantityPerDay(2)
                .build();

        var addOn = CustomizedServiceView.newBuilder()
                .setId(1)
                .setDuration(20)
                .setType(ServiceType.ADDON)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .build();

        try (var utilities = Mockito.mockStatic(DateUtil.class)) {
            var timestamp = DateUtil.get10Timestamp();
            utilities.when(DateUtil::get10Timestamp).thenReturn(timestamp);

            // Act
            var result = petDetailService.buildAddOnPetDetailDTO(null, null, petId, selectedAddOnDef, addOn);

            // Assert
            var petDetail = new MoeGroomingPetDetail();
            petDetail.setPetId((int) petId);
            petDetail.setServiceId(addOnId);
            petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
            petDetail.setServiceType(ServiceType.ADDON_VALUE);
            petDetail.setQuantityPerDay(2);
            petDetail.setServiceTime(20);
            petDetail.setServicePrice(BigDecimal.valueOf(price));
            petDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
            petDetail.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setScopeTypeTime(ServiceScopeType.DO_NOT_SAVE_VALUE);
            petDetail.setUpdateTime(timestamp);
            petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);
            var expected = new PetDetailDTO()
                    .setPetId(petId)
                    .setPetDetail(petDetail)
                    .setOperations(List.of())
                    .setService(addOn);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void buildAddOnPetDetailDTO_InvalidDateType() {
        // Arrange
        var petId = 100L;
        var addOnId = 1;
        var price = 100d;
        var selectedAddOnDef = SelectedAddOnDef.newBuilder()
                .setAddOnId(addOnId)
                .setServiceTime(20)
                .setServicePrice(price)
                .build();

        var addOn = CustomizedServiceView.newBuilder()
                .setId(1)
                .setDuration(20)
                .setType(ServiceType.ADDON)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .build();

        // Act & Assert
        assertThatThrownBy(() -> petDetailService.buildAddOnPetDetailDTO(null, null, petId, selectedAddOnDef, addOn))
                .isInstanceOf(BizException.class)
                .hasMessageContaining("Date type not found");
    }

    @Test
    void calculatePeriod_withBoardingWithNoRequireStaffAddON_returnsCorrectPeriod() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setStartDate("2023-01-01");
        petDetail1.setEndDate("2023-01-02");
        petDetail1.setPetId(1);
        petDetail1.setServiceId(11);
        petDetail1.setStartTime(480L);
        petDetail1.setEndTime(1020L);
        petDetail1.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail1.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail1.setPetId(1);
        petDetail2.setStartTime(0L);
        petDetail2.setEndTime(0L);
        petDetail2.setServiceType(ServiceType.ADDON_VALUE);
        petDetail2.setAssociatedServiceId(11L);
        petDetail2.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);

        // Arrange
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail1, petDetail2);

        // Act
        Pair<LocalDateTime, LocalDateTime> result = petDetailService.calculatePeriod(1L, petDetails, null);

        var expect = Pair.of(LocalDateTime.of(2023, 1, 1, 8, 0), LocalDateTime.of(2023, 1, 2, 17, 0));
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void calculatePeriod_withBoardingWithRequireStaffAddON_returnsCorrectPeriod() {
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setStartDate("2023-01-01");
        petDetail1.setEndDate("2023-01-02");
        petDetail1.setPetId(1);
        petDetail1.setServiceId(11);
        petDetail1.setStartTime(480L);
        petDetail1.setEndTime(1020L);
        petDetail1.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetail1.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setStartDate("2023-01-01");
        petDetail2.setEndDate("2023-01-01");
        petDetail2.setPetId(1);
        petDetail2.setStartTime(420L);
        petDetail2.setEndTime(1020L);
        petDetail2.setServiceType(ServiceType.ADDON_VALUE);
        petDetail2.setStaffId(1);
        petDetail2.setAssociatedServiceId(11L);
        petDetail2.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        var petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setStartDate("2023-01-02");
        petDetail3.setEndDate("2023-01-02");
        petDetail3.setPetId(1);
        petDetail3.setStartTime(480L);
        petDetail3.setEndTime(1080L);
        petDetail3.setServiceType(ServiceType.ADDON_VALUE);
        petDetail3.setStaffId(1);
        petDetail3.setAssociatedServiceId(11L);
        petDetail3.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);

        // Arrange
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail1, petDetail2, petDetail3);

        // Act
        Pair<LocalDateTime, LocalDateTime> result = petDetailService.calculatePeriod(1L, petDetails, null);

        var expect = Pair.of(LocalDateTime.of(2023, 1, 1, 7, 0), LocalDateTime.of(2023, 1, 2, 18, 0));
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void getPetLastPetDetail_EmptyInputs_ReturnsEmptyList() {
        // Test with empty customer IDs
        List<MoeGroomingPetDetail> result1 = petDetailService.getPetLastPetDetail(
                COMPANY_ID, Collections.emptyList(), List.of(1L), GetLastPetDetailRequest.Filter.getDefaultInstance());
        assertTrue(result1.isEmpty());

        // Test with empty pet IDs
        List<MoeGroomingPetDetail> result2 = petDetailService.getPetLastPetDetail(
                COMPANY_ID, List.of(1L), Collections.emptyList(), GetLastPetDetailRequest.Filter.getDefaultInstance());
        assertTrue(result2.isEmpty());
    }

    @Test
    void getPetLastPetDetail_WithServiceIds_ReturnsPetDetails() {
        // Arrange
        List<Long> customerIds = List.of(1L);
        List<Long> petIds = List.of(2L);
        GetLastPetDetailRequest.Filter filter = GetLastPetDetailRequest.Filter.newBuilder()
                .addServiceIds(1L)
                .addServiceIds(2L)
                .build();

        when(companyRemoteService.getTimezoneName(any())).thenReturn("Asia/Shanghai");
        MoeGroomingPetDetail expectedDetail = new MoeGroomingPetDetail();
        when(petDetailMapper.selectMany(any())).thenReturn(List.of(expectedDetail));

        // Act
        List<MoeGroomingPetDetail> result =
                petDetailService.getPetLastPetDetail(COMPANY_ID, customerIds, petIds, filter);

        // Assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        verify(petDetailMapper).selectMany(any());
    }

    @Test
    void getPetLastPetDetail_WithTimeRangeFilter_ReturnsPetDetails() {
        // Arrange
        List<Long> customerIds = List.of(1L);
        List<Long> petIds = List.of(2L);
        LocalDateTime now = LocalDateTime.now();

        Timestamp pastTimestamp = Timestamp.newBuilder()
                .setSeconds(now.minusDays(7).toEpochSecond(ZoneOffset.UTC))
                .build();

        Timestamp timestamp = Timestamp.newBuilder()
                .setSeconds(now.toEpochSecond(ZoneOffset.UTC))
                .build();

        Interval timeRange = com.google.type.Interval.newBuilder()
                .setStartTime(pastTimestamp)
                .setEndTime(timestamp)
                .build();

        GetLastPetDetailRequest.Filter filter = GetLastPetDetailRequest.Filter.newBuilder()
                .setStartTimeRange(timeRange)
                .setEndTimeRange(timeRange)
                .build();

        when(companyRemoteService.getTimezoneName(any())).thenReturn("Asia/Shanghai");
        MoeGroomingPetDetail expectedDetail = new MoeGroomingPetDetail();
        when(petDetailMapper.selectMany(any())).thenReturn(List.of(expectedDetail));

        // Act
        List<MoeGroomingPetDetail> result =
                petDetailService.getPetLastPetDetail(COMPANY_ID, customerIds, petIds, filter);

        // Assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        verify(petDetailMapper).selectMany(any());
    }

    @Test
    void getPetLastPetDetail_WithServiceItemTypes_ReturnsPetDetails() {
        // Arrange
        List<Long> customerIds = List.of(1L);
        List<Long> petIds = List.of(2L);
        GetLastPetDetailRequest.Filter filter = GetLastPetDetailRequest.Filter.newBuilder()
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .addServiceItemTypes(ServiceItemType.BOARDING)
                .build();

        when(companyRemoteService.getTimezoneName(any())).thenReturn("Asia/Shanghai");
        MoeGroomingPetDetail expectedDetail = new MoeGroomingPetDetail();
        when(petDetailMapper.selectMany(any())).thenReturn(List.of(expectedDetail));

        // Act
        List<MoeGroomingPetDetail> result =
                petDetailService.getPetLastPetDetail(COMPANY_ID, customerIds, petIds, filter);

        // Assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        verify(petDetailMapper).selectMany(any());
    }

    @Test
    void getPetLastPetDetail_WithBookingRequestFilter_ReturnsPetDetails() {
        // Arrange
        List<Long> customerIds = List.of(1L);
        List<Long> petIds = List.of(2L);
        GetLastPetDetailRequest.Filter filter = GetLastPetDetailRequest.Filter.newBuilder()
                .setBusinessId(1L)
                .addStatus(AppointmentStatus.FINISHED)
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .setFilterNoStartTime(true)
                .setFilterBookingRequest(true)
                .addServiceIds(1L)
                .build();

        when(companyRemoteService.getTimezoneName(any())).thenReturn("Asia/Shanghai");

        MoeGroomingPetDetail expectedDetail = new MoeGroomingPetDetail();
        when(petDetailMapper.selectMany(any())).thenReturn(List.of(expectedDetail));

        // Act
        List<MoeGroomingPetDetail> result =
                petDetailService.getPetLastPetDetail(COMPANY_ID, customerIds, petIds, filter);

        // Assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        verify(petDetailMapper).selectMany(any());
    }

    @Test
    void serviceItemTypesToBitValueList_ReturnsCorrectBitValues() {
        // Arrange
        List<ServiceItemType> serviceItemTypes = Arrays.asList(ServiceItemType.GROOMING, ServiceItemType.BOARDING);

        // Act
        List<Integer> result = petDetailService.serviceItemTypesToBitValueList(serviceItemTypes);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void isBelongsToAppointment_withEmptyPetEvaluationIds_returnsTrue() {
        boolean result = petDetailService.isBelongsToAppointment(1L, List.of());
        assertThat(result).isTrue();
    }

    @Test
    void isBelongsToAppointment_withNonExistingPetEvaluationIds_returnsFalse() {
        var spyService = spy(petDetailService);
        doReturn(List.of(
                        new MoeGroomingPetDetail() {
                            {
                                setId(1);
                            }
                        },
                        new MoeGroomingPetDetail() {
                            {
                                setId(2);
                            }
                        }))
                .when(spyService)
                .getPetDetailList(anyLong());
        boolean result = spyService.isBelongsToAppointment(1L, List.of(3));
        assertThat(result).isFalse();
    }

    @Test
    void isBelongsToAppointment_withExistingPetEvaluationIds_returnsTrue() {
        var spyService = spy(petDetailService);
        doReturn(List.of(
                        new MoeGroomingPetDetail() {
                            {
                                setId(1);
                            }
                        },
                        new MoeGroomingPetDetail() {
                            {
                                setId(2);
                            }
                        }))
                .when(spyService)
                .getPetDetailList(anyLong());
        boolean result = spyService.isBelongsToAppointment(1L, List.of(1, 2));
        assertThat(result).isTrue();
    }

    @Test
    void isBelongsToAppointment_withPartialExistingPetEvaluationIds_returnsFalse() {
        var spyService = spy(petDetailService);
        doReturn(List.of(
                        new MoeGroomingPetDetail() {
                            {
                                setId(1);
                            }
                        },
                        new MoeGroomingPetDetail() {
                            {
                                setId(2);
                            }
                        }))
                .when(spyService)
                .getPetDetailList(anyLong());
        boolean result = spyService.isBelongsToAppointment(1L, List.of(1, 3));
        assertThat(result).isFalse();
    }

    @Test
    void getWithActualDatesInfo_WhenDateTypeNotEveryday_WithMultipleRecords_ShouldReturnUnmodified() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();

        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(1);
        petDetail1.setServiceId(100);
        petDetail1.setPetId(200);
        petDetail1.setServiceType(ServiceType.SERVICE_VALUE);
        petDetail1.setGroomingId(100);
        petDetail1.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);
        petDetail1.setStartDate("2024-10-28");
        petDetail1.setEndDate("2024-10-30");
        petDetail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);

        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(2);
        petDetail2.setServiceId(300);
        petDetail2.setPetId(200);
        petDetail2.setAssociatedServiceId(100L);
        petDetail2.setServiceType(ServiceType.ADDON_VALUE);
        petDetail2.setGroomingId(100);
        petDetail2.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        petDetail2.setSpecificDates("[\"2024-10-28\", \"2024-10-29\"]");
        petDetail2.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);

        petDetails.add(petDetail1);
        petDetails.add(petDetail2);

        // Act
        List<MoeGroomingPetDetail> result = petDetailService.getWithActualDatesInfo(petDetails);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify first pet detail
        MoeGroomingPetDetail resultPetDetail1 = result.get(0);
        assertEquals(petDetail1.getId(), resultPetDetail1.getId());
        assertEquals(petDetail1.getStartDate(), resultPetDetail1.getStartDate());
        assertEquals(petDetail1.getEndDate(), resultPetDetail1.getEndDate());
        assertEquals(petDetail1.getDateType(), resultPetDetail1.getDateType());

        // Verify second pet detail
        MoeGroomingPetDetail resultPetDetail2 = result.get(1);
        assertEquals(petDetail2.getId(), resultPetDetail2.getId());
        assertEquals(petDetail2.getDateType(), resultPetDetail2.getDateType());
    }

    /**
     * {@link PetDetailService#processPetDetailForBoarding(MoeGroomingPetDetail, List, boolean)}
     */
    @Test
    void processPetDetailForBoarding_whenAfterDatesNonEmpty_shouldUpdatePetDetail() {
        var spyService = spy(petDetailService);

        // Arrange
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE); // Changed to match implementation
        petDetail.setSpecificDates("[\"2023-10-01\", \"2023-10-02\", \"2023-10-03\"]");

        var newDates = List.of(LocalDate.of(2023, 10, 1));

        doReturn(1).when(spyService).update(any(), anyBoolean());

        // Act
        spyService.processPetDetailForBoarding(petDetail, newDates, false);

        // Assert
        verify(spyService, times(1)).update(any(), eq(false));
    }

    /**
     * {@link PetDetailService#processPetDetailForBoarding(MoeGroomingPetDetail, List, boolean)}
     */
    @Test
    void processPetDetailForBoarding_whenAfterDatesEmpty_shouldDeletePetDetail() {
        var spyService = spy(petDetailService);

        // Arrange
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        petDetail.setSpecificDates("[\"2023-10-01\", \"2023-10-02\", \"2023-10-03\"]");

        var newDates = List.<LocalDate>of();

        doReturn(1).when(spyService).delete(anyLong(), anyBoolean());

        // Act
        spyService.processPetDetailForBoarding(petDetail, newDates, false);

        // Assert
        verify(spyService, times(1)).delete(anyLong(), eq(false));
    }

    /**
     * {@link PetDetailService#processPetDetailForDaycare(MoeGroomingPetDetail, List, boolean)}
     */
    @Test
    void processPetDetailForDaycare_whenAfterDatesNonEmpty_shouldUpdatePetDetail() {
        var spyService = spy(petDetailService);

        // Arrange
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        petDetail.setSpecificDates("[\"2023-10-01\", \"2023-10-02\", \"2023-10-03\"]");

        var newDates = List.of(LocalDate.of(2023, 10, 1));

        doReturn(1).when(spyService).update(any(), anyBoolean());

        // Act
        spyService.processPetDetailForDaycare(petDetail, newDates, false);

        // Assert
        verify(spyService, times(1)).update(any(), eq(false));
    }

    /**
     * {@link PetDetailService#processPetDetailForDaycare(MoeGroomingPetDetail, List, boolean)}
     */
    @Test
    void processPetDetailForDaycare_whenAfterDatesEmpty_shouldDeletePetDetail() {
        var spyService = spy(petDetailService);

        // Arrange
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        petDetail.setSpecificDates("[\"2023-10-01\", \"2023-10-02\", \"2023-10-03\"]");

        var newDates = List.<LocalDate>of();

        doReturn(1).when(spyService).delete(anyLong(), anyBoolean());

        // Act
        spyService.processPetDetailForDaycare(petDetail, newDates, false);

        // Assert
        verify(spyService, times(1)).delete(anyLong(), eq(false));
    }

    private static MoeGroomingPetDetail buildPetDetail(int petId, long updateTime) {
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(petId);
        petDetail.setUpdateTime(updateTime);
        return petDetail;
    }

    @Test
    void testGetPetIdToUpdateTime_withValidData() {
        // Arrange
        var details = Arrays.asList(
                buildPetDetail(1, 1700000000L),
                buildPetDetail(1, 1690000000L), // 最小值
                buildPetDetail(2, 1750000000L),
                buildPetDetail(2, 1745000000L) // 最小值
                );
        List<Long> petIds = Arrays.asList(1L, 2L);

        // Act
        var result = PetDetailService.getPetIdToUpdateTime(details, petIds);

        // Assert
        var expected = Map.of(1L, 1690000000L, 2L, 1745000000L);
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testGetPetIdToUpdateTime_withEmptyDetails() {
        // Arrange
        var details = new ArrayList<MoeGroomingPetDetail>();
        List<Long> petIds = Arrays.asList(1L, 2L);

        // Act
        var result = PetDetailService.getPetIdToUpdateTime(details, petIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetPetIdToUpdateTime_withNoMatchingPetIds() {
        // Arrange
        var details = Arrays.asList(buildPetDetail(3, 1700000000L), buildPetDetail(4, 1695000000L));
        List<Long> petIds = Arrays.asList(1L, 2L);

        // Act
        var result = PetDetailService.getPetIdToUpdateTime(details, petIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void sortByServiceItemType_shouldSortByServiceItemTypeOrder() {
        // Arrange
        List<MoeGroomingPetDetail> petDetailsToBeSorted = new ArrayList<>();

        // Create pet details with different service item types
        MoeGroomingPetDetail boarding = new MoeGroomingPetDetail();
        boarding.setId(1);
        boarding.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);

        MoeGroomingPetDetail daycare = new MoeGroomingPetDetail();
        daycare.setId(2);
        daycare.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);

        MoeGroomingPetDetail dogWalking = new MoeGroomingPetDetail();
        dogWalking.setId(3);
        dogWalking.setServiceItemType((byte) ServiceItemType.DOG_WALKING_VALUE);

        MoeGroomingPetDetail evaluation = new MoeGroomingPetDetail();
        evaluation.setId(4);
        evaluation.setServiceItemType((byte) ServiceItemType.EVALUATION_VALUE);

        MoeGroomingPetDetail grooming = new MoeGroomingPetDetail();
        grooming.setId(5);
        grooming.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);

        MoeGroomingPetDetail groupClass = new MoeGroomingPetDetail();
        groupClass.setId(6);
        groupClass.setServiceItemType((byte) ServiceItemType.GROUP_CLASS_VALUE);

        // Add pet details in random order
        petDetailsToBeSorted.add(boarding);
        petDetailsToBeSorted.add(evaluation);
        petDetailsToBeSorted.add(grooming);
        petDetailsToBeSorted.add(daycare);
        petDetailsToBeSorted.add(dogWalking);
        petDetailsToBeSorted.add(groupClass);

        // Mock mapper to return the same list
        when(petDetailMapper.select(any())).thenReturn(petDetailsToBeSorted);

        // Act
        List<MoeGroomingPetDetail> result = petDetailService.sortByServiceItemType(petDetailsToBeSorted);

        // Assert
        assertThat(result).hasSize(6);
        assertThat(result.get(0).getServiceItemType()).isEqualTo((byte) ServiceItemType.GROOMING_VALUE);
        assertThat(result.get(1).getServiceItemType()).isEqualTo((byte) ServiceItemType.GROUP_CLASS_VALUE);
        assertThat(result.get(2).getServiceItemType()).isEqualTo((byte) ServiceItemType.DOG_WALKING_VALUE);
        assertThat(result.get(3).getServiceItemType()).isEqualTo((byte) ServiceItemType.EVALUATION_VALUE);
        assertThat(result.get(4).getServiceItemType()).isEqualTo((byte) ServiceItemType.DAYCARE_VALUE);
        assertThat(result.get(5).getServiceItemType()).isEqualTo((byte) ServiceItemType.BOARDING_VALUE);
    }

    @Test
    void sortByServiceItemType_withEmptyList_shouldReturnEmptyList() {
        // Arrange
        List<MoeGroomingPetDetail> emptyList = new ArrayList<>();
        when(petDetailMapper.select(any())).thenReturn(emptyList);

        // Act
        List<MoeGroomingPetDetail> result = petDetailService.sortByServiceItemType(emptyList);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void sortByServiceItemType_withSingleItem_shouldReturnSameList() {
        // Arrange
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        List<MoeGroomingPetDetail> singleItemList = new ArrayList<>();
        singleItemList.add(petDetail);
        when(petDetailMapper.select(any())).thenReturn(singleItemList);

        // Act
        List<MoeGroomingPetDetail> result = petDetailService.sortByServiceItemType(singleItemList);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(petDetail);
    }

    @Test
    void sortByServiceItemType_withDuplicatePetDetails_shouldReturnSortedList() {
        // Arrange
        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(1);
        petDetail1.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        petDetails.add(petDetail1);
        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(2);
        petDetail2.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetails.add(petDetail2);
        MoeGroomingPetDetail petDetail3 = new MoeGroomingPetDetail();
        petDetail3.setId(3);
        petDetail3.setServiceItemType((byte) ServiceItemType.DOG_WALKING_VALUE);
        petDetails.add(petDetail3);
        MoeGroomingPetDetail petDetail4 = new MoeGroomingPetDetail();
        petDetail4.setId(4);
        petDetail4.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetails.add(petDetail4);

        // duplicate pet detail to be sorted
        List<MoeGroomingPetDetail> petDetailsToBeSorted = new ArrayList<>(petDetails);
        petDetailsToBeSorted.add(petDetail1);

        when(petDetailMapper.select(any())).thenReturn(petDetails);

        // Act
        List<MoeGroomingPetDetail> result = petDetailService.sortByServiceItemType(petDetailsToBeSorted);

        // Assert
        assertThat(result).hasSize(5);
        assertThat(result.get(0).getServiceItemType()).isEqualTo((byte) ServiceItemType.GROOMING_VALUE);
        assertThat(result.get(1).getServiceItemType()).isEqualTo((byte) ServiceItemType.GROOMING_VALUE);
        assertThat(result.get(2).getServiceItemType()).isEqualTo((byte) ServiceItemType.DOG_WALKING_VALUE);
        assertThat(result.get(3).getServiceItemType()).isEqualTo((byte) ServiceItemType.BOARDING_VALUE);
        assertThat(result.get(4).getServiceItemType()).isEqualTo((byte) ServiceItemType.BOARDING_VALUE);
    }
}
