package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.service.AppointmentRescheduleService.buildToBeResizedPetDetails;
import static com.moego.svc.appointment.service.AppointmentRescheduleService.calculateUpdatedQuantities;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;

import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.WorkMode;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.RescheduleCalendarCardDTO;
import com.moego.svc.appointment.dto.RescheduleGroomingDTO;
import com.moego.svc.appointment.service.remote.ActiveMQService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith({SpringExtension.class})
class AppointmentRescheduleServiceTest {

    @InjectMocks
    private AppointmentRescheduleService appointmentRescheduleService;

    @Mock
    private AppointmentCompositeService appointmentCompositeService;

    @Mock
    private ActiveMQService mqService;

    private final Date mockDate = new Date(1730718399000L);

    @Test
    void testRescheduleGroomingAppointment_WithBatchReschedule() {
        // 准备测试数据
        RescheduleGroomingDTO dto = RescheduleGroomingDTO.builder()
                .startDate("2023-05-02")
                .startTime(600) // 10:00 AM
                .endTime(660) // 11:00 AM
                .batchRescheduleAppointment(RescheduleGroomingDTO.BatchRescheduleAppointmentDTO.builder()
                        .sourceStaffId(1)
                        .targetStaffId(2)
                        .build())
                .cardType(CalendarCardType.APPOINTMENT)
                .build();

        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-05-01");
        appointment.setAppointmentStartTime(600); // 9:00 AM

        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(1);
        petDetail1.setStartDate("2023-05-01");
        petDetail1.setStartTime(600L);
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId(1);

        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(2);
        petDetail2.setStartDate("2023-05-01");
        petDetail2.setStartTime(600L);
        petDetail2.setEndTime(660L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId(3);

        MoeGroomingServiceOperation serviceOperation1 = new MoeGroomingServiceOperation();
        serviceOperation1.setId(1L);
        serviceOperation1.setGroomingServiceId(2);
        serviceOperation1.setStartTime(600);
        serviceOperation1.setStaffId(1);

        MoeGroomingServiceOperation serviceOperation2 = new MoeGroomingServiceOperation();
        serviceOperation2.setId(2L);
        serviceOperation2.setGroomingServiceId(2);
        serviceOperation2.setStartTime(600);
        serviceOperation2.setStaffId(3);

        List<MoeGroomingPetDetail> petDetails = Arrays.asList(petDetail1, petDetail2);
        List<MoeGroomingServiceOperation> serviceOperations = List.of(serviceOperation1, serviceOperation2);

        doNothing().when(mqService).publishAppointmentEvent(anyLong(), any(AppointmentEventEnum.class));

        // 执行测试方法
        appointmentRescheduleService.rescheduleCalendarCard(dto, appointment, petDetails, serviceOperations);

        // 验证结果
        verify(appointmentCompositeService)
                .updateServiceDetails(
                        argThat(updatePetDetails -> {
                            assertEquals(2, updatePetDetails.size());
                            MoeGroomingPetDetail updatedPetDetail1 = updatePetDetails.get(0);
                            assertEquals("2023-05-02", updatedPetDetail1.getStartDate());
                            assertEquals(600L, updatedPetDetail1.getStartTime());
                            assertEquals(660L, updatedPetDetail1.getEndTime());
                            assertEquals(2, updatedPetDetail1.getStaffId());

                            MoeGroomingPetDetail updatedPetDetail2 = updatePetDetails.get(1);
                            assertEquals("2023-05-02", updatedPetDetail2.getStartDate());
                            assertEquals(600L, updatedPetDetail1.getStartTime());
                            assertEquals(660L, updatedPetDetail1.getEndTime());
                            assertNull(updatedPetDetail2.getStaffId()); // 未变更，因为不匹配 sourceStaffId

                            return true;
                        }),
                        argThat(updateServiceOperations -> {
                            assertEquals(2, updateServiceOperations.size());
                            MoeGroomingServiceOperation updatedOperation1 = updateServiceOperations.get(0);
                            assertEquals(600, updatedOperation1.getStartTime());
                            assertEquals(2, updatedOperation1.getStaffId());

                            MoeGroomingServiceOperation updatedOperation2 = updateServiceOperations.get(1);
                            assertEquals(600, updatedOperation2.getStartTime());
                            assertNull(updatedOperation2.getStaffId()); // 未变更，因为不匹配 sourceStaffId
                            return true;
                        }));
        // 验证 MQ 消息发送
        verify(mqService).publishAppointmentEvent(appointment.getId(), AppointmentEventEnum.MODIFY_SINGLE);
    }

    @Test
    void testRescheduleGroomingAppointment_WithoutBatchReschedule() {
        // 准备测试数据
        RescheduleGroomingDTO dto = RescheduleGroomingDTO.builder()
                .startDate("2023-05-02")
                .startTime(600) // 10:00 AM
                .endTime(660) // 11:00 AM
                .cardType(CalendarCardType.APPOINTMENT)
                .staffId(2L)
                .build();

        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setAppointmentDate("2023-05-01");
        appointment.setAppointmentStartTime(600); // 9:00 AM

        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(1);
        petDetail1.setStartDate("2023-05-01");
        petDetail1.setStartTime(600L);
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId(1);

        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(2);
        petDetail2.setStartDate("2023-05-01");
        petDetail2.setStartTime(600L);
        petDetail2.setEndTime(660L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId(3);

        MoeGroomingServiceOperation serviceOperation = new MoeGroomingServiceOperation();
        serviceOperation.setId(1L);
        serviceOperation.setGroomingServiceId(2);
        serviceOperation.setStartTime(600);
        serviceOperation.setStaffId(1);

        List<MoeGroomingPetDetail> petDetails = Arrays.asList(petDetail1, petDetail2);
        List<MoeGroomingServiceOperation> serviceOperations = List.of(serviceOperation);

        doNothing().when(mqService).publishAppointmentEvent(anyLong(), any(AppointmentEventEnum.class));

        // 执行测试方法
        appointmentRescheduleService.rescheduleCalendarCard(dto, appointment, petDetails, serviceOperations);

        // 验证结果
        verify(appointmentCompositeService)
                .updateServiceDetails(
                        argThat(updatePetDetails -> {
                            assertEquals(2, updatePetDetails.size());
                            MoeGroomingPetDetail updatedPetDetail1 = updatePetDetails.get(0);
                            assertEquals("2023-05-02", updatedPetDetail1.getStartDate());
                            assertEquals(600L, updatedPetDetail1.getStartTime());
                            assertEquals(660L, updatedPetDetail1.getEndTime());
                            assertEquals(
                                    2L,
                                    updatedPetDetail1
                                            .getStaffId()
                                            .longValue()); // 未传递BatchReschedule的情况下，直接使用dto.staffId

                            MoeGroomingPetDetail updatedPetDetail2 = updatePetDetails.get(1);
                            assertEquals("2023-05-02", updatedPetDetail2.getStartDate());
                            assertEquals(600L, updatedPetDetail1.getStartTime());
                            assertEquals(660L, updatedPetDetail1.getEndTime());
                            assertEquals(
                                    2L,
                                    updatedPetDetail2
                                            .getStaffId()
                                            .longValue()); // 未传递BatchReschedule的情况下，直接使用dto.staffId

                            return true;
                        }),
                        argThat(updateServiceOperations -> {
                            assertEquals(1, updateServiceOperations.size());
                            MoeGroomingServiceOperation updatedOperation = updateServiceOperations.get(0);
                            assertEquals(600, updatedOperation.getStartTime());
                            assertNull(updatedOperation.getStaffId());
                            return true;
                        }));
        // 验证 MQ 消息发送
        verify(mqService).publishAppointmentEvent(appointment.getId(), AppointmentEventEnum.MODIFY_SINGLE);
    }

    @Test
    void getToBeRescheduledOperations_AppointmentCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.APPOINTMENT)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);
        petDetail1.setWorkMode(WorkMode.SEQUENCE_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        var operation1 = new MoeGroomingServiceOperation();
        operation1.setId(100L);
        operation1.setGroomingServiceId(petDetail1Id);
        operation1.setStaffId((int) originalStaff1Id);
        operation1.setStartTime(600);
        operation1.setDuration(30);

        var operation2 = new MoeGroomingServiceOperation();
        operation2.setId(200L);
        operation2.setGroomingServiceId(petDetail1Id);
        operation2.setStaffId((int) originalStaff2Id);
        operation2.setStartTime(630);
        operation2.setDuration(30);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result = AppointmentRescheduleService.getToBeRescheduledOperations(
                    dto, List.of(petDetail1, petDetail2), List.of(operation1, operation2));

            // Assert
            var expectedOperation1 = new MoeGroomingServiceOperation();
            expectedOperation1.setId(100L);
            expectedOperation1.setStaffId((int) targetStaffId);
            expectedOperation1.setStartTime(petDetail1.getStartTime().intValue());
            expectedOperation1.setUpdateTime(mockDate);

            var expectedOperation2 = new MoeGroomingServiceOperation();
            expectedOperation2.setId(200L);
            expectedOperation2.setStartTime(petDetail1.getStartTime().intValue() + operation1.getDuration());
            expectedOperation2.setUpdateTime(mockDate);

            var expected = List.of(expectedOperation1, expectedOperation2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledOperations_ServiceCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.SERVICE)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result =
                    AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail1, petDetail2));

            // Assert
            var expectedPetDetail1 = new MoeGroomingPetDetail();
            expectedPetDetail1.setId(petDetail1Id);
            expectedPetDetail1.setStartDate("2024-11-04");
            expectedPetDetail1.setStartTime(petDetail1.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setEndDate("2024-11-04");
            expectedPetDetail1.setEndTime(petDetail1.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setStaffId((int) targetStaffId);
            expectedPetDetail1.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail1);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledOperations_ServiceAndOperationCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.SERVICE_AND_OPERATION)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id, petDetail2Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);
        petDetail1.setWorkMode(WorkMode.SEQUENCE_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        var operation1 = new MoeGroomingServiceOperation();
        operation1.setId(100L);
        operation1.setGroomingServiceId(petDetail1Id);
        operation1.setStaffId((int) originalStaff1Id);
        operation1.setStartTime(600);
        operation1.setDuration(30);

        var operation2 = new MoeGroomingServiceOperation();
        operation2.setId(200L);
        operation2.setGroomingServiceId(petDetail1Id);
        operation2.setStaffId((int) originalStaff2Id);
        operation2.setStartTime(630);
        operation2.setDuration(30);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result = AppointmentRescheduleService.getToBeRescheduledOperations(
                    dto, List.of(petDetail1, petDetail2), List.of(operation1, operation2));

            // Assert
            var expectedOperation1 = new MoeGroomingServiceOperation();
            expectedOperation1.setId(100L);
            expectedOperation1.setStartTime(petDetail1.getStartTime().intValue());
            expectedOperation1.setStaffId((int) targetStaffId);
            expectedOperation1.setUpdateTime(mockDate);

            var expectedOperation2 = new MoeGroomingServiceOperation();
            expectedOperation2.setId(200L);
            expectedOperation2.setStartTime(petDetail1.getStartTime().intValue() + operation1.getDuration());
            expectedOperation2.setUpdateTime(mockDate);

            var expected = List.of(expectedOperation1, expectedOperation2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledOperations_OperationCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetailId = 100;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.OPERATION)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetailId))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(60)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(petDetailId);
        petDetail.setStartDate("2024-11-04");
        petDetail.setStartTime(660L);
        petDetail.setEndDate("2024-11-04");
        petDetail.setEndTime(720L);
        petDetail.setServiceTime(60);
        petDetail.setStaffId((int) originalStaff1Id);
        petDetail.setWorkMode(WorkMode.SEQUENCE_VALUE);

        var operation1 = new MoeGroomingServiceOperation();
        operation1.setId(100L);
        operation1.setGroomingServiceId(petDetailId);
        operation1.setStaffId((int) originalStaff1Id);
        operation1.setStartTime(600);
        operation1.setDuration(30);

        var operation2 = new MoeGroomingServiceOperation();
        operation2.setId(200L);
        operation2.setGroomingServiceId(petDetailId);
        operation2.setStaffId((int) originalStaff2Id);
        operation2.setStartTime(630);
        operation2.setDuration(30);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result = AppointmentRescheduleService.getToBeRescheduledOperations(
                    dto, List.of(petDetail), List.of(operation1, operation2));

            // Assert
            var expectedOperation1 = new MoeGroomingServiceOperation();
            expectedOperation1.setId(100L);
            expectedOperation1.setStaffId((int) targetStaffId);
            expectedOperation1.setStartTime(operation1.getStartTime() + dto.getOffsetMinutes());
            expectedOperation1.setUpdateTime(mockDate);

            var expectedOperation2 = new MoeGroomingServiceOperation();
            expectedOperation2.setId(200L);
            expectedOperation2.setStartTime(operation2.getStartTime() + dto.getOffsetMinutes());
            expectedOperation2.setUpdateTime(mockDate);

            var expected = List.of(expectedOperation1, expectedOperation2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledPetDetails_AppointmentCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.APPOINTMENT)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result =
                    AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail1, petDetail2));

            // Assert
            var expectedPetDetail1 = new MoeGroomingPetDetail();
            expectedPetDetail1.setId(petDetail1Id);
            expectedPetDetail1.setStartDate("2024-11-04");
            expectedPetDetail1.setStartTime(petDetail1.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setEndDate("2024-11-04");
            expectedPetDetail1.setEndTime(petDetail1.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setStaffId((int) targetStaffId);
            expectedPetDetail1.setUpdatedAt(mockDate);

            var expectedPetDetail2 = new MoeGroomingPetDetail();
            expectedPetDetail2.setId(petDetail2Id);
            expectedPetDetail2.setStartDate("2024-11-04");
            expectedPetDetail2.setStartTime(petDetail2.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setEndDate("2024-11-04");
            expectedPetDetail2.setEndTime(petDetail2.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setStaffId((int) targetStaffId);
            expectedPetDetail2.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail1, expectedPetDetail2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledPetDetails_ServiceAndOperationCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.SERVICE_AND_OPERATION)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id, petDetail2Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);
        petDetail1.setWorkMode(WorkMode.SEQUENCE_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result =
                    AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail1, petDetail2));

            // Assert
            var expectedPetDetail1 = new MoeGroomingPetDetail();
            expectedPetDetail1.setId(petDetail1Id);
            expectedPetDetail1.setWorkMode(WorkMode.SEQUENCE_VALUE);
            expectedPetDetail1.setStartDate("2024-11-04");
            expectedPetDetail1.setStartTime(petDetail1.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setEndDate("2024-11-04");
            expectedPetDetail1.setEndTime(petDetail1.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setStaffId((int) targetStaffId);
            expectedPetDetail1.setUpdatedAt(mockDate);

            var expectedPetDetail2 = new MoeGroomingPetDetail();
            expectedPetDetail2.setId(petDetail2Id);
            expectedPetDetail2.setStartDate("2024-11-04");
            expectedPetDetail2.setStartTime(petDetail2.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setEndDate("2024-11-04");
            expectedPetDetail2.setEndTime(petDetail2.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setStaffId((int) targetStaffId);
            expectedPetDetail2.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail1, expectedPetDetail2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledPetDetails_OperationCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.OPERATION)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);
        petDetail1.setWorkMode(WorkMode.SEQUENCE_VALUE);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(660L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(720L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        try (MockedStatic<DateUtil> mocked = mockStatic(DateUtil.class)) {
            mocked.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result =
                    AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail1, petDetail2));

            // Assert
            var expectedPetDetail = new MoeGroomingPetDetail();
            expectedPetDetail.setId(petDetail1Id);
            expectedPetDetail.setWorkMode(WorkMode.SEQUENCE_VALUE);
            expectedPetDetail.setStartDate("2024-11-04");
            expectedPetDetail.setStartTime(petDetail1.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail.setEndDate("2024-11-04");
            expectedPetDetail.setEndTime(petDetail1.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail.setStaffId((int) targetStaffId);
            expectedPetDetail.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledPetDetails_BlockCard() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.BLOCK)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(false)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setId(petDetail1Id);
        petDetail.setStartDate("2024-11-04");
        petDetail.setStartTime(600L);
        petDetail.setEndDate("2024-11-04");
        petDetail.setEndTime(660L);
        petDetail.setServiceTime(60);
        petDetail.setStaffId((int) originalStaff1Id);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result = AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail));

            // Assert
            var expectedPetDetail = new MoeGroomingPetDetail();
            expectedPetDetail.setId(petDetail1Id);
            expectedPetDetail.setStaffId((int) targetStaffId);
            expectedPetDetail.setStartDate("2024-11-04");
            expectedPetDetail.setStartTime(petDetail.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail.setEndDate("2024-11-04");
            expectedPetDetail.setEndTime(petDetail.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    void getToBeRescheduledOperations_MoveAllCards() {
        // Arrange
        var appointmentId = 1L;
        var originalStaff1Id = 10L;
        var originalStaff2Id = 20L;
        var targetStaffId = 30L;
        var petDetail1Id = 100;
        var petDetail2Id = 200;
        var dto = new RescheduleCalendarCardDTO()
                .setCardType(CalendarCardType.SERVICE)
                .setOriginalStaffId(originalStaff1Id)
                .setAffectedPetDetailIds(Set.of(petDetail1Id))
                .setAppointmentId(appointmentId)
                .setMoveAllCards(true)
                .setOffsetMinutes(30)
                .setTargetStaffId(targetStaffId)
                .setTargetStartTime(660);
        var petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setId(petDetail1Id);
        petDetail1.setStartDate("2024-11-04");
        petDetail1.setStartTime(600L);
        petDetail1.setEndDate("2024-11-04");
        petDetail1.setEndTime(660L);
        petDetail1.setServiceTime(60);
        petDetail1.setStaffId((int) originalStaff1Id);

        var petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setId(petDetail2Id);
        petDetail2.setStartDate("2024-11-04");
        petDetail2.setStartTime(1080L);
        petDetail2.setEndDate("2024-11-04");
        petDetail2.setEndTime(1140L);
        petDetail2.setServiceTime(60);
        petDetail2.setStaffId((int) originalStaff2Id);

        try (MockedStatic<DateUtil> mockedDate = mockStatic(DateUtil.class)) {
            mockedDate.when(DateUtil::getCurrentDateTime).thenReturn(mockDate);

            // Act
            var result =
                    AppointmentRescheduleService.getToBeRescheduledPetDetails(dto, List.of(petDetail1, petDetail2));

            // Assert
            var expectedPetDetail1 = new MoeGroomingPetDetail();
            expectedPetDetail1.setId(petDetail1Id);
            expectedPetDetail1.setStartDate("2024-11-04");
            expectedPetDetail1.setStartTime(petDetail1.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setEndDate("2024-11-04");
            expectedPetDetail1.setEndTime(petDetail1.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail1.setStaffId((int) targetStaffId);
            expectedPetDetail1.setUpdatedAt(mockDate);

            var expectedPetDetail2 = new MoeGroomingPetDetail();
            expectedPetDetail2.setId(petDetail2Id);
            expectedPetDetail2.setStartDate("2024-11-04");
            expectedPetDetail2.setStartTime(petDetail2.getStartTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setEndDate("2024-11-04");
            expectedPetDetail2.setEndTime(petDetail2.getEndTime() + dto.getOffsetMinutes());
            expectedPetDetail2.setUpdatedAt(mockDate);

            var expected = List.of(expectedPetDetail1, expectedPetDetail2);
            assertThat(result).usingRecursiveComparison().isEqualTo(expected);
        }
    }

    @Test
    @DisplayName("Should throw exception when target times are null")
    public void testBuildToBeResizedPetDetails_NullTargetTimes() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        // dto.setTargetStartTime(null); // 默认为null
        // dto.setTargetEndTime(null); // 默认为null

        List<MoeGroomingPetDetail> affectPetDetails = Collections.singletonList(createPetDetail(1, 30));

        // Act & Assert
        assertThatThrownBy(() -> buildToBeResizedPetDetails(dto, affectPetDetails))
                .isInstanceOf(BizException.class)
                .hasMessageContaining("Resize multi service need target start time and end time");
    }

    @Test
    @DisplayName("Should resize non-zero service and keep zero services unchanged when no position overlap")
    public void testBuildToBeResizedPetDetails_ResizeNonZeroService_NoZeroServiceMovement() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540); // 9:00 AM
        dto.setTargetEndTime(600); // 10:00 AM

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 12:00 PM (不与非零时长服务的起止时间重合)
        MoeGroomingPetDetail zeroPetDetail = createPetDetail(2, 0);
        zeroPetDetail.setStartTime(720L); // 12:00 PM
        zeroPetDetail.setEndTime(720L); // 12:00 PM

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, zeroPetDetail);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(540L);
        assertThat(result.get(0).getEndTime()).isEqualTo(600L);
        assertThat(result.get(0).getServiceTime()).isEqualTo(60);

        // 验证零时长服务保持不变
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(720L);
        assertThat(result.get(1).getEndTime()).isEqualTo(720L);
    }

    @Test
    @DisplayName("Should move zero service upward when connected to start and resizing upward")
    public void testBuildToBeResizedPetDetails_MoveZeroServiceUpward() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540); // 9:00 AM - 向上拉伸
        dto.setTargetEndTime(660); // 11:00 AM - 保持结束时间不变

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 10:00 AM (与非零时长服务的开始时间重合)
        MoeGroomingPetDetail zeroPetDetail = createPetDetail(2, 0);
        zeroPetDetail.setStartTime(600L); // 10:00 AM
        zeroPetDetail.setEndTime(600L); // 10:00 AM

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, zeroPetDetail);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(540L); // 新开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(660L); // 保持原结束时间
        assertThat(result.get(0).getServiceTime()).isEqualTo(120); // 新服务时长

        // 验证零时长服务也向上偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(540L); // 新位置，与非零服务新开始时间一致
        assertThat(result.get(1).getEndTime()).isEqualTo(540L);
    }

    @Test
    @DisplayName("Should move zero service downward when connected to end and resizing downward")
    public void testBuildToBeResizedPetDetails_MoveZeroServiceDownward() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(600); // 10:00 AM - 保持开始时间不变
        dto.setTargetEndTime(720); // 12:00 PM - 向下拉伸

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 11:00 AM (与非零时长服务的结束时间重合)
        MoeGroomingPetDetail zeroPetDetail = createPetDetail(2, 0);
        zeroPetDetail.setStartTime(660L); // 11:00 AM
        zeroPetDetail.setEndTime(660L); // 11:00 AM

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, zeroPetDetail);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(600L); // 保持原开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(720L); // 新结束时间
        assertThat(result.get(0).getServiceTime()).isEqualTo(120); // 新服务时长

        // 验证零时长服务也向下偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(720L); // 新位置，与非零服务新结束时间一致
        assertThat(result.get(1).getEndTime()).isEqualTo(720L);
    }

    @Test
    @DisplayName("Should handle multiple zero services with different conditions")
    public void testBuildToBeResizedPetDetails_MultipleZeroServices() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540); // 9:00 AM - 向上拉伸
        dto.setTargetEndTime(720); // 12:00 PM - 向下拉伸

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务1: 10:00 AM (与非零时长服务的开始时间重合)
        MoeGroomingPetDetail zeroPetDetail1 = createPetDetail(2, 0);
        zeroPetDetail1.setStartTime(600L); // 10:00 AM
        zeroPetDetail1.setEndTime(600L); // 10:00 AM

        // 零时长服务2: 11:00 AM (与非零时长服务的结束时间重合)
        MoeGroomingPetDetail zeroPetDetail2 = createPetDetail(3, 0);
        zeroPetDetail2.setStartTime(660L); // 11:00 AM
        zeroPetDetail2.setEndTime(660L); // 11:00 AM

        // 零时长服务3: 1:00 PM (不与非零时长服务的时间重合)
        MoeGroomingPetDetail zeroPetDetail3 = createPetDetail(4, 0);
        zeroPetDetail3.setStartTime(780L); // 1:00 PM
        zeroPetDetail3.setEndTime(780L); // 1:00 PM

        List<MoeGroomingPetDetail> affectPetDetails =
                Arrays.asList(nonZeroPetDetail, zeroPetDetail1, zeroPetDetail2, zeroPetDetail3);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(4);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(540L); // 新开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(720L); // 新结束时间
        assertThat(result.get(0).getServiceTime()).isEqualTo(180); // 新服务时长
        // 验证与开始时间重合的零时长服务向上偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(540L); // 新位置，与非零服务新开始时间一致

        // 验证与结束时间重合的零时长服务向下偏移了
        assertThat(result.get(2).getId()).isEqualTo(3L);
        assertThat(result.get(2).getStartTime()).isEqualTo(720L); // 新位置，与非零服务新结束时间一致

        // 验证不与非零服务时间重合的零时长服务保持不变
        assertThat(result.get(3).getId()).isEqualTo(4L);
        assertThat(result.get(3).getStartTime()).isEqualTo(780L); // 保持原位置不变
    }

    @Test
    @DisplayName("Should handle empty affected pet details list")
    public void testBuildToBeResizedPetDetails_EmptyList() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540);
        dto.setTargetEndTime(600);

        List<MoeGroomingPetDetail> affectPetDetails = Collections.emptyList();

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should handle no non-zero service in affected pet details")
    public void testBuildToBeResizedPetDetails_NoNonZeroService() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540);
        dto.setTargetEndTime(600);

        // 只有零时长服务
        MoeGroomingPetDetail zeroPetDetail1 = createPetDetail(1, 0);
        zeroPetDetail1.setStartTime(660L);
        zeroPetDetail1.setEndTime(660L);

        MoeGroomingPetDetail zeroPetDetail2 = createPetDetail(2, 0);
        zeroPetDetail2.setStartTime(720L);
        zeroPetDetail2.setEndTime(720L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(zeroPetDetail1, zeroPetDetail2);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should handle multiple non-zero services in affected pet details")
    public void testBuildToBeResizedPetDetails_MultipleNonZeroServices() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540);
        dto.setTargetEndTime(600);

        // 多个非零时长服务
        MoeGroomingPetDetail nonZeroPetDetail1 = createPetDetail(1, 60);
        nonZeroPetDetail1.setStartTime(600L);
        nonZeroPetDetail1.setEndTime(660L);

        MoeGroomingPetDetail nonZeroPetDetail2 = createPetDetail(2, 30);
        nonZeroPetDetail2.setStartTime(660L);
        nonZeroPetDetail2.setEndTime(690L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail1, nonZeroPetDetail2);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should move zero service upward when connected to start and stretching upward")
    public void testBuildToBeResizedPetDetails_StretchUpwardWithConnectedZero() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(540); // 9:00 AM - 向上拉伸
        dto.setTargetEndTime(660); // 11:00 AM - 保持结束时间不变

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 10:00 AM (与非零时长服务的开始时间重合)
        MoeGroomingPetDetail startConnectedZero = createPetDetail(2, 0);
        startConnectedZero.setStartTime(600L);
        startConnectedZero.setEndTime(600L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, startConnectedZero);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(540L); // 新开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(660L); // 保持原结束时间

        // 验证与开始时间重合的零时长服务向上偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(540L); // 新位置，与非零服务新开始时间一致
    }

    @Test
    @DisplayName("Should move zero service downward when connected to end and stretching downward")
    public void testBuildToBeResizedPetDetails_StretchDownwardWithConnectedZero() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(600); // 10:00 AM - 保持开始时间不变
        dto.setTargetEndTime(720); // 12:00 PM - 向下拉伸

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 11:00 AM (与非零时长服务的结束时间重合)
        MoeGroomingPetDetail endConnectedZero = createPetDetail(3, 0);
        endConnectedZero.setStartTime(660L);
        endConnectedZero.setEndTime(660L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, endConnectedZero);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确调整
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(600L); // 保持原开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(720L); // 新结束时间

        // 验证与结束时间重合的零时长服务向下偏移了
        assertThat(result.get(1).getId()).isEqualTo(3L);
        assertThat(result.get(1).getStartTime()).isEqualTo(720L); // 新位置，与非零服务新结束时间一致
    }

    @Test
    @DisplayName("Should move zero service downward when connected to start and shrinking downward")
    public void testBuildToBeResizedPetDetails_ShrinkDownwardWithConnectedZero() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(630); // 10:30 AM - 向下缩小开始时间
        dto.setTargetEndTime(660); // 11:00 AM - 保持结束时间不变

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 10:00 AM (与非零时长服务的开始时间重合)
        MoeGroomingPetDetail startConnectedZero = createPetDetail(2, 0);
        startConnectedZero.setStartTime(600L);
        startConnectedZero.setEndTime(600L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, startConnectedZero);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确缩小
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(630L); // 新开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(660L); // 保持原结束时间

        // 验证与开始时间重合的零时长服务向下偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(630L); // 新位置，与非零服务新开始时间一致
    }

    @Test
    @DisplayName("Should move zero service upward when connected to end and shrinking upward")
    public void testBuildToBeResizedPetDetails_ShrinkUpwardWithConnectedZero() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(600); // 10:00 AM - 保持开始时间不变
        dto.setTargetEndTime(630); // 10:30 AM - 向上缩小结束时间

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务: 11:00 AM (与非零时长服务的结束时间重合)
        MoeGroomingPetDetail endConnectedZero = createPetDetail(2, 0);
        endConnectedZero.setStartTime(660L);
        endConnectedZero.setEndTime(660L);

        List<MoeGroomingPetDetail> affectPetDetails = Arrays.asList(nonZeroPetDetail, endConnectedZero);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(2);

        // 验证非零时长服务已被正确缩小
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(600L); // 保持原开始时间
        assertThat(result.get(0).getEndTime()).isEqualTo(630L); // 新结束时间

        // 验证与结束时间重合的零时长服务向上偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(630L); // 新位置，与非零服务新结束时间一致
    }

    @Test
    @DisplayName("Should handle complex scenario with multiple zero services")
    public void testBuildToBeResizedPetDetails_ComplexScenario() {
        // Arrange
        RescheduleCalendarCardDTO dto = new RescheduleCalendarCardDTO();
        dto.setTargetStartTime(630); // 10:30 AM - 向下缩小开始时间
        dto.setTargetEndTime(630); // 10:30 AM - 向上缩小结束时间(变为零时长)

        // 非零时长服务: 10:00 AM - 11:00 AM
        MoeGroomingPetDetail nonZeroPetDetail = createPetDetail(1, 60);
        nonZeroPetDetail.setStartTime(600L); // 10:00 AM
        nonZeroPetDetail.setEndTime(660L); // 11:00 AM

        // 零时长服务1: 10:00 AM (与非零时长服务的开始时间重合)
        MoeGroomingPetDetail startConnectedZero = createPetDetail(2, 0);
        startConnectedZero.setStartTime(600L);
        startConnectedZero.setEndTime(600L);

        // 零时长服务2: 11:00 AM (与非零时长服务的结束时间重合)
        MoeGroomingPetDetail endConnectedZero = createPetDetail(3, 0);
        endConnectedZero.setStartTime(660L);
        endConnectedZero.setEndTime(660L);

        // 零时长服务3: 10:30 AM (在非零时长服务中间)
        MoeGroomingPetDetail middleZero = createPetDetail(4, 0);
        middleZero.setStartTime(630L);
        middleZero.setEndTime(630L);

        List<MoeGroomingPetDetail> affectPetDetails =
                Arrays.asList(nonZeroPetDetail, startConnectedZero, endConnectedZero, middleZero);

        // Act
        List<MoeGroomingPetDetail> result = buildToBeResizedPetDetails(dto, affectPetDetails);

        // Assert
        assertThat(result).hasSize(4);

        // 验证非零时长服务已变为零时长服务
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getStartTime()).isEqualTo(630L);
        assertThat(result.get(0).getEndTime()).isEqualTo(630L);
        assertThat(result.get(0).getServiceTime()).isEqualTo(0);

        // 验证与原开始时间重合的零时长服务向下偏移了
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(1).getStartTime()).isEqualTo(630L);

        // 验证与原结束时间重合的零时长服务向上偏移了
        assertThat(result.get(2).getId()).isEqualTo(3L);
        assertThat(result.get(2).getStartTime()).isEqualTo(630L);

        // 验证中间的零时长服务保持不变
        assertThat(result.get(3).getId()).isEqualTo(4L);
        assertThat(result.get(3).getStartTime()).isEqualTo(630L);
    }

    // 辅助方法：创建宠物详情对象
    private MoeGroomingPetDetail createPetDetail(int id, int serviceTime) {
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setId(id);
        petDetail.setServiceTime(serviceTime);
        petDetail.setStartDate("2023-05-01");
        petDetail.setEndDate("2023-05-01");
        return petDetail;
    }

    // ===== BOARDING 场景测试 =====
    @Test
    void should_calculate_quantity_when_boarding_per_night_updated() {
        // Arrange
        MoeGroomingPetDetail beforeDetail = createBoardingDetail(1, 100, "2024-02-01", 600L, "2024-02-03", 600L); // 2晚
        beforeDetail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        beforeDetail.setQuantity(2);

        MoeGroomingPetDetail updatedDetail = new MoeGroomingPetDetail();
        updatedDetail.setId(1);
        updatedDetail.setStartDate("2024-02-01");
        updatedDetail.setStartTime(600L);
        updatedDetail.setEndDate("2024-02-05"); // 延长到4晚
        updatedDetail.setEndTime(600L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(beforeDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(4); // 4晚
    }

    @Test
    void should_calculate_quantity_when_boarding_per_day_updated() {
        // Arrange
        MoeGroomingPetDetail beforeDetail = createBoardingDetail(2, 101, "2024-02-01", 600L, "2024-02-03", 600L);
        beforeDetail.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE); // 改为按天计费
        beforeDetail.setQuantity(2);

        MoeGroomingPetDetail updatedDetail = new MoeGroomingPetDetail();
        updatedDetail.setId(2);
        updatedDetail.setStartDate("2024-02-01");
        updatedDetail.setStartTime(600L);
        updatedDetail.setEndDate("2024-02-05"); // 延长到5天
        updatedDetail.setEndTime(600L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(beforeDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(5); // 5 天
    }

    // ===== DAYCARE 场景测试 =====
    @Test
    void should_calculate_quantity_when_standalone_daycare_updated() {
        // Arrange - 独立的 daycare 服务，按次计费
        MoeGroomingPetDetail beforeDetail =
                createDaycareDetail(3, 200, "2024-02-01", 480L, "2024-02-01", 960L, ServicePriceUnit.PER_SESSION_VALUE);
        beforeDetail.setQuantity(1);

        MoeGroomingPetDetail updatedDetail = new MoeGroomingPetDetail();
        updatedDetail.setId(3);
        updatedDetail.setStartDate("2024-02-02"); // 改日期
        updatedDetail.setStartTime(480L);
        updatedDetail.setEndDate("2024-02-02");
        updatedDetail.setEndTime(960L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(beforeDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(1); // 独立daycare按次计费
    }

    @Test
    void should_calculate_quantity_when_daycare_under_boarding_everyday_updated() {
        // Arrange - daycare 关联在 boarding 下，每天都有
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(4, 300, "2024-02-01", 600L, "2024-02-05", 600L); // 4晚boarding

        MoeGroomingPetDetail beforeDaycareDetail =
                createDaycareDetail(5, 400, "2024-02-01", 480L, "2024-02-05", 960L, ServicePriceUnit.PER_SESSION_VALUE);
        beforeDaycareDetail.setAssociatedServiceId(300L);
        beforeDaycareDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE); // 每天不含最后一天
        beforeDaycareDetail.setQuantity(4); // 4天

        MoeGroomingPetDetail updatedDaycareDetail = new MoeGroomingPetDetail();
        updatedDaycareDetail.setId(5);
        updatedDaycareDetail.setDateType(
                PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE); // 每天含最后一天

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeDaycareDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDaycareDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(5); // 4天改5天，多了 checkout day
    }

    @Test
    void should_calculate_quantity_when_daycare_under_boarding_everyday_include_checkout_updated() {
        // Arrange - daycare 关联在 boarding 下，每天都有（含最后一天）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(6, 500, "2024-02-01", 600L, "2024-02-04", 600L); // 3晚boarding

        MoeGroomingPetDetail beforeDaycareDetail =
                createDaycareDetail(7, 600, "2024-02-01", 480L, "2024-02-04", 960L, ServicePriceUnit.PER_SESSION_VALUE);
        beforeDaycareDetail.setAssociatedServiceId(500L);
        beforeDaycareDetail.setDateType(
                PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE); // 每天含最后一天
        beforeDaycareDetail.setQuantity(4); // 4天（含checkout day）

        MoeGroomingPetDetail updatedDaycareDetail = new MoeGroomingPetDetail();
        updatedDaycareDetail.setId(7);
        updatedDaycareDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE);
        updatedDaycareDetail.setStartDate("2024-02-01");
        updatedDaycareDetail.setStartTime(540L);
        updatedDaycareDetail.setEndDate("2024-02-01");
        updatedDaycareDetail.setEndTime(1020L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeDaycareDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDaycareDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(1); // everyday 改为 date point，只算一天
    }

    // ===== GROOMING 场景测试 =====
    @Test
    void should_calculate_quantity_when_standalone_grooming_updated() {
        // Arrange - 独立的 grooming 服务
        MoeGroomingPetDetail beforeDetail = createGroomingDetail(8, 700, "2024-02-01", 600L, "2024-02-01", 720L);
        beforeDetail.setQuantity(1);

        MoeGroomingPetDetail updatedDetail = new MoeGroomingPetDetail();
        updatedDetail.setId(8);
        updatedDetail.setStartDate("2024-02-02"); // 改日期
        updatedDetail.setStartTime(720L);
        updatedDetail.setEndDate("2024-02-02");
        updatedDetail.setEndTime(840L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(beforeDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(1); // grooming 按次计费
    }

    // ===== ADD-ON 场景测试 =====
    @Test
    void should_calculate_quantity_when_addon_date_point_updated() {
        // Arrange - add-on 为某一天（datePoint）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(11, 1000, "2024-02-01", 600L, "2024-02-05", 600L); // 4晚boarding

        MoeGroomingPetDetail beforeAddOnDetail =
                createAddOnDetail(12, 1100, 100, "2024-02-02", 600L, "2024-02-02", 720L); // 某一天的add-on
        beforeAddOnDetail.setQuantity(1);

        MoeGroomingPetDetail updatedAddOnDetail = new MoeGroomingPetDetail();
        updatedAddOnDetail.setId(12);
        updatedAddOnDetail.setStartDate("2024-02-03"); // 改到另一天
        updatedAddOnDetail.setStartTime(600L);
        updatedAddOnDetail.setEndDate("2024-02-03");
        updatedAddOnDetail.setEndTime(720L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeAddOnDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedAddOnDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(1); // datePoint 始终是1
    }

    @Test
    void should_calculate_quantity_when_addon_specific_dates_updated() {
        // Arrange - add-on 为特定几天（specificDates）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(13, 1200, "2024-02-01", 600L, "2024-02-06", 600L); // 5晚boarding

        MoeGroomingPetDetail beforeAddOnDetail = createAddOnDetail(14, 1300, 100, null, null, null, null);
        beforeAddOnDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE);
        beforeAddOnDetail.setSpecificDates("[\"2024-02-02\", \"2024-02-04\"]"); // 2天
        beforeAddOnDetail.setQuantityPerDay(2); // 每天2次
        beforeAddOnDetail.setQuantity(4); // 2天 * 2次/天 = 4

        MoeGroomingPetDetail updatedAddOnDetail = new MoeGroomingPetDetail();
        updatedAddOnDetail.setId(14);
        updatedAddOnDetail.setSpecificDates("[\"2024-02-02\", \"2024-02-04\", \"2024-02-05\"]"); // 增加到3天

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeAddOnDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedAddOnDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getQuantity()).isEqualTo(6); // 3天 * 2次/天 = 6
    }

    @Test
    void should_calculate_quantity_when_addon_everyday_updated() {
        // Arrange - add-on 每天都有（不含最后一天）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(15, 1400, "2024-02-01", 600L, "2024-02-05", 600L); // 4晚boarding

        MoeGroomingPetDetail beforeAddOnDetail = createAddOnDetail(16, 1500, 100, null, null, null, null);
        beforeAddOnDetail.setAssociatedServiceId(1400L);
        beforeAddOnDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE); // 每天不含最后一天
        beforeAddOnDetail.setQuantityPerDay(1);
        beforeAddOnDetail.setQuantity(4); // 4天

        MoeGroomingPetDetail updatedBoardingDetail = new MoeGroomingPetDetail();
        updatedBoardingDetail.setId(15);
        updatedBoardingDetail.setStartDate("2024-02-01");
        updatedBoardingDetail.setStartTime(600L);
        updatedBoardingDetail.setEndDate("2024-02-07"); // boarding延长到6晚
        updatedBoardingDetail.setEndTime(600L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeAddOnDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedBoardingDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(2); // boarding 及其关联的 add-on
        result.stream().filter(r -> r.getId() == 15).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(6)); // boarding 5 晚
        result.stream().filter(r -> r.getId() == 16).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(6)); // boarding延长，add-on也跟着延长到6天
    }

    @Test
    void should_calculate_quantity_when_addon_everyday_include_checkout_updated() {
        // Arrange - add-on 每天都有（含最后一天）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(17, 1600, "2024-02-01", 600L, "2024-02-04", 600L); // 3晚boarding

        MoeGroomingPetDetail beforeAddOnDetail = createAddOnDetail(18, 1700, 100, null, null, null, null);
        beforeAddOnDetail.setAssociatedServiceId(1600L);
        beforeAddOnDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE); // 每天含最后一天
        beforeAddOnDetail.setQuantityPerDay(3); // 每天3次
        beforeAddOnDetail.setQuantity(12); // 4天 * 3次/天 = 12

        MoeGroomingPetDetail updatedBoardingDetail = new MoeGroomingPetDetail();
        updatedBoardingDetail.setId(17);
        updatedBoardingDetail.setStartDate("2024-02-01");
        updatedBoardingDetail.setStartTime(600L);
        updatedBoardingDetail.setEndDate("2024-02-06"); // boarding延长到5晚
        updatedBoardingDetail.setEndTime(600L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeAddOnDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedBoardingDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(2); // boarding 及其关联的 add-on
        result.stream().filter(r -> r.getId() == 17).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(5)); // boarding 5 晚
        result.stream().filter(r -> r.getId() == 18).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(18)); // 6天 * 3次/天 = 18（含checkout day）
    }

    @Test
    void should_calculate_quantity_when_no_associated_addon_everyday_include_checkout_updated() {
        // Arrange - add-on 每天都有（含最后一天）
        MoeGroomingPetDetail boardingDetail =
                createBoardingDetail(19, 1600, "2024-02-01", 600L, "2024-02-04", 600L); // 3晚boarding

        MoeGroomingPetDetail beforeDaycareDetail =
                createDaycareDetail(20, 1650, null, null, null, null, ServicePriceUnit.PER_SESSION_VALUE);
        beforeDaycareDetail.setDateType(
                PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE); // 每天含最后一天
        beforeDaycareDetail.setQuantityPerDay(1);
        beforeDaycareDetail.setQuantity(4); // 4天

        MoeGroomingPetDetail beforeAddOnDetail = createAddOnDetail(21, 1700, 100, null, null, null, null);
        beforeAddOnDetail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE); // 每天含最后一天
        beforeAddOnDetail.setQuantityPerDay(3); // 每天3次
        beforeAddOnDetail.setQuantity(12); // 4天 * 3次/天 = 12

        MoeGroomingPetDetail updatedBoardingDetail = new MoeGroomingPetDetail();
        updatedBoardingDetail.setId(19);
        updatedBoardingDetail.setStartDate("2024-02-01");
        updatedBoardingDetail.setStartTime(600L);
        updatedBoardingDetail.setEndDate("2024-02-06"); // boarding延长到5晚
        updatedBoardingDetail.setEndTime(600L);

        List<MoeGroomingPetDetail> beforePetDetails = List.of(boardingDetail, beforeAddOnDetail);
        List<MoeGroomingPetDetail> updatedPetDetails = List.of(updatedBoardingDetail);

        // Act
        List<MoeGroomingPetDetail> result = calculateUpdatedQuantities(beforePetDetails, updatedPetDetails);

        // Assert
        assertThat(result).hasSize(2); // boarding 及其关联的 add-on
        result.stream().filter(r -> r.getId() == 19).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(5)); // boarding 5 晚
        result.stream().filter(r -> r.getId() == 20).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(6)); // 6次（含checkout day）
        result.stream().filter(r -> r.getId() == 21).findAny().ifPresent(r -> assertThat(r.getQuantity())
                .isEqualTo(18)); // 6天 * 3次/天 = 18（含checkout day）
    }

    // ===== 辅助方法 =====
    private MoeGroomingPetDetail createGroomingDetail(
            int id, int serviceId, String startDate, Long startTime, String endDate, Long endTime) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id);
        detail.setPetId(100);
        detail.setServiceId(serviceId);
        detail.setServiceType(ServiceType.SERVICE_VALUE);
        detail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        detail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        detail.setStartDate(startDate);
        detail.setStartTime(startTime);
        detail.setEndDate(endDate);
        detail.setEndTime(endTime);
        return detail;
    }

    private MoeGroomingPetDetail createBoardingDetail(
            int id, int serviceId, String startDate, Long startTime, String endDate, Long endTime) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id);
        detail.setPetId(100);
        detail.setServiceId(serviceId);
        detail.setServiceType(ServiceType.SERVICE_VALUE);
        detail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail.setPriceUnit(ServicePriceUnit.PER_NIGHT_VALUE);
        detail.setStartDate(startDate);
        detail.setStartTime(startTime);
        detail.setEndDate(endDate);
        detail.setEndTime(endTime);
        return detail;
    }

    private MoeGroomingPetDetail createDaycareDetail(
            int id, int serviceId, String startDate, Long startTime, String endDate, Long endTime, int priceUnit) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id);
        detail.setPetId(100);
        detail.setServiceId(serviceId);
        detail.setServiceType(ServiceType.SERVICE_VALUE);
        detail.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);
        detail.setPriceUnit(priceUnit);
        detail.setStartDate(startDate);
        detail.setStartTime(startTime);
        detail.setEndDate(endDate);
        detail.setEndTime(endTime);
        return detail;
    }

    private MoeGroomingPetDetail createAddOnDetail(
            int id, int serviceId, int petId, String startDate, Long startTime, String endDate, Long endTime) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id);
        detail.setPetId(petId);
        detail.setServiceId(serviceId);
        detail.setServiceType(ServiceType.ADDON_VALUE);
        detail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE); // 默认grooming类型的addon
        detail.setPriceUnit(ServicePriceUnit.PER_SESSION_VALUE);
        detail.setStartDate(startDate);
        detail.setStartTime(startTime);
        detail.setEndDate(endDate);
        detail.setEndTime(endTime);
        detail.setQuantityPerDay(1);
        return detail;
    }
}
