package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PaymentSettingForClientDTO {

    @Schema(description = "自定义费名")
    private String customizedFeeName;

    @Schema(description = "ProcessingFee类型：0 - pay by business, 1 - pay by client")
    private Byte processingFeePayBy;

    @Schema(description = "1: 跳过tips, 0/null： 保持tips")
    private Byte skipTipping;

    @Schema(description = "定制json报文")
    private String customTipping;

    @Schema(description = "0: disable, 1: enable")
    Byte cardAuthEnable;

    private Integer preAuthBspd;
}
