package com.moego.server.business.dto;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayrollEmployeeDetailDTO {

    private Integer type;
    private String title;

    private List<String> fieldHeaders;
    private List<String> fieldKeys;
    private Map<String, String> fieldUnits;

    private List<Map<String, Object>> records;
    private Long totalCount;
}
