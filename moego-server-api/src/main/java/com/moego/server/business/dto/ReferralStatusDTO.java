package com.moego.server.business.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralStatusDTO {

    /**
     * moe_payment.moe_company_plan_order
     */
    private Boolean isPurchased;

    /**
     * referral code valid status
     */
    private Boolean isValid;

    /**
     * moe_payment.moe_company_permission_state
     */
    private Boolean isSubscribing;

    /**
     * referral code
     */
    private String referralCode;

    private BigDecimal percentOff;

    private BigDecimal amountOff;
}
