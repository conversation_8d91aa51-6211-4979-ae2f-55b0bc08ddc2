package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CustomerSignStatus {

    private Integer agreementId;
    private Integer customerId;

    @Schema(description = "是否需要签署")
    private Boolean isNeedSign;

    //
    //    @Schema(description="上次有效签署的时间")
    //    private Long lastSignTime;

    @Schema(description = "agreement 签署 类型")
    private Byte agreementRequiredType;
}
