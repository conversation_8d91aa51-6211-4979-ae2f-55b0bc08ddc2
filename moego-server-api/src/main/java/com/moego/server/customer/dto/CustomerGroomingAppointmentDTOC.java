package com.moego.server.customer.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class CustomerGroomingAppointmentDTOC {

    private Integer id;
    private Integer customerId;
    private String orderId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;

    private List<CustomerGroomingAppointmentPetDetailDTOC> petDetails;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "business id")
    private Integer businessId;
}
