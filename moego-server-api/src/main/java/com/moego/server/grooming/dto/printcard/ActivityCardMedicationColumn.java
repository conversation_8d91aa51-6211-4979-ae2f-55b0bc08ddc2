package com.moego.server.grooming.dto.printcard;

import com.moego.server.grooming.dto.LodgingInfo;
import java.util.List;

public record ActivityCardMedicationColumn(
        Integer petId,
        String petName,
        Integer petTypeId,
        String breed,
        String gender,
        String avatarPath,
        String weight,
        List<PrintPetCodeBinding> petCodeBindings,
        String serviceName,
        List<MedicationInstruction> instruction,
        Integer time,
        String clientFirstName,
        String clientLastName,
        Long lodgingTypeId,
        Long lodgingUnitId,
        String lodgingUnitName,
        LodgingInfo lodgingInfo) {}
