package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MobileSummaryDTO {

    private Integer totalAppts;
    private BigDecimal earnedRevenue;
    private BigDecimal totalUnpaid;
    private BigDecimal expectedRevenue;
    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
    private BigDecimal totalRefund;

    private Integer onlineBookingNum;
    private Integer intakeFormSubmission;

    private Integer finishedAppts;
    private Integer confirmedAppts;
    private Integer unconfirmedAppts;
    private Integer cancelledAppts;
    private Integer noShowAppts;

    private BigDecimal earnedNoTipTax;
    private BigDecimal earnedServiceCharge; // service charge 实收
    private BigDecimal tips;
    private BigDecimal taxes;
    private BigDecimal discount;
    private BigDecimal expectedNoTipTax;
    private BigDecimal expectedServiceCharge; // service charge 应收
    private BigDecimal earnedTips;
    private BigDecimal earnedTax;
    private BigDecimal earnedDiscount;

    private BigDecimal expectedTips;
    private BigDecimal expectedTax;
    private BigDecimal expectedDiscount;

    /*
    时间段内服务过的pets, 可以有重复，来自不同appointment的同一个pet，计入total pets
     */
    private List<Integer> petIds;

    /*
    时间段内服务过的customers, 去重, 用以计算 total clients
     */
    private List<Integer> customerIds;

    // 已完成的pet数
    private Integer finishedPets;

    public static MobileSummaryDTO.MobileSummaryDTOBuilder initDefaultValueBuilder() {
        return MobileSummaryDTO.builder()
                .totalAppts(0)
                .earnedRevenue(BigDecimal.ZERO)
                .totalUnpaid(BigDecimal.ZERO)
                .expectedRevenue(BigDecimal.ZERO)
                .netSaleRevenue(BigDecimal.ZERO)
                .totalRefund(BigDecimal.ZERO)
                .onlineBookingNum(0)
                .intakeFormSubmission(0)
                .finishedAppts(0)
                .confirmedAppts(0)
                .unconfirmedAppts(0)
                .cancelledAppts(0)
                .noShowAppts(0)
                .earnedNoTipTax(BigDecimal.ZERO)
                .earnedServiceCharge(BigDecimal.ZERO)
                .tips(BigDecimal.ZERO)
                .taxes(BigDecimal.ZERO)
                .discount(BigDecimal.ZERO)
                .expectedNoTipTax(BigDecimal.ZERO)
                .expectedServiceCharge(BigDecimal.ZERO)
                .earnedTips(BigDecimal.ZERO)
                .earnedTax(BigDecimal.ZERO)
                .earnedDiscount(BigDecimal.ZERO)
                .expectedTips(BigDecimal.ZERO)
                .expectedTax(BigDecimal.ZERO)
                .expectedDiscount(BigDecimal.ZERO)
                .petIds(List.of())
                .customerIds(List.of())
                .finishedPets(0);
    }
}
