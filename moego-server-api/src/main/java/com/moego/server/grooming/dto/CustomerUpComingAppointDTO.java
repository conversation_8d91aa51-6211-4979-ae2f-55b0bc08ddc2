package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CustomerUpComingAppointDTO {

    private Integer id;
    private Integer customerId;
    private Integer businessId;
    private String businessName;
    private String businessAvatarPath;
    private Integer customerAddressId;
    private Integer createBy;
    private String orderId;

    private String appointmentDate;
    private String appointmentEndDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;

    private BigDecimal estimatePrice;

    private String customerLastName;
    private String customerFirstName;
    // Client phone number
    private String clientPhoneNumber;

    // client full address(包含 city zipcode)
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;

    private String clientFullAddress;

    private String createByLastName;
    private String createByFirstName;

    private String ticketComments;
    private String alertNotes;

    private Long createTime;
    private Long updateTime;

    private String email;

    /**
     * arrival window before
     */
    private Integer arrivalBeforeStartTime;

    /**
     * arrival window after
     */
    private Integer arrivalAfterStartTime;

    private List<CustomerUpcomingPetDetailDTO> petDetails;
}
