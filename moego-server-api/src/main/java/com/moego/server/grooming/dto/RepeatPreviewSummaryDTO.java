package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

public record RepeatPreviewSummaryDTO(
        @Schema(description = "preview date list") List<RepeatPreviewInfoDTO> previewInfoList,
        @Schema(description = "repeat date 总数") Integer totalCount,
        @Schema(description = "有冲突的 date 数量") Integer conflictCount,
        @Schema(description = "save preview 会移除的 existing appointment 数量") Integer removedCount) {}
