package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraTwilioFineDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SystemTwilioFineParams extends NotificationParams {

    private String title = "Non-Compliance fine due to SMS messaging violation";
    private String type = NotificationEnum.TYPE_SYSTEM_TWILIO_FINE;
    private NotificationExtraTwilioFineDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
}
