package com.moego.server.message.dto;

import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-09-21 02:08
 */
@Data
public class ReminderDetailDTO<TDetail> {

    private Integer id;
    private Integer customerId;
    private TDetail detail;
    private Integer sendCount;
    private MessageDetailDTO sendDetail;
    private Integer planSendTime;
    private String order1;
    private Integer order2;
    private Integer order3;
    private Integer reminderType;
    private Integer actualSendTime;
    private GroomingCustomerInfoDTO customerInfo;
}
