server:
  servlet:
    encoding:
      charset: UTF-8
      force-response: true
  shutdown: graceful
  max-http-request-header-size: 16KB
  tomcat:
    threads:
      max: 1000
spring:
  jackson:
    time-zone: UTC
    serialization:
      fail-on-empty-beans: false # 支持序列化空对象，允许在 Controller 中返回空对象
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 10000
  main:
    allow-circular-references: true
    banner-mode: log
  mvc:
    throw-exception-if-no-handler-found: true
  cloud:
    openfeign:
      okhttp:
        enabled: true
      client:
        config:
          default:
            connectTimeout: 10000
            readTimeout: 60000
      lazy-attributes-resolution: true

moego:
  sentry:
    dsn: https://<EMAIL>/5
  grey:
    enabled: ${MOEGO_GREY_ENABLED:true}
  rpc:
    transfer-headers: [ "x-moe-canary", "x-moe-force-log" ]
  grpc:
    server:
      shutdown-timeout: 45000
      max-inbound-message-size: 100MB
      max-inbound-metadata-size: 1MB
      observability:
        metrics:
          port: 8080
    client:
      max-inbound-message-size: 100MB
      max-inbound-metadata-size: 1MB
      base-packages: [ com.moego, backend.proto ]
  http:
    server:
      observability:
        logging:
          enabled: false
        metrics:
          exclude-path-patterns:
            - /**
sentry:
  logging:
    enabled: false
