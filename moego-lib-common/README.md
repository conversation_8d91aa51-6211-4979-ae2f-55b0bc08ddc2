## moego-lib-common

MoeGo 公共 Java 类库，核心技术栈为 `Spring` 和 `gRPC`，这个库是基于 `Spring Boot`，用于构建 `gRPC` 和 `Http`
服务，主要提供以下功能（入口文件 `MoeGoAutoConfiguration`）：

#### Auth（gRPC，Http）

服务鉴权功能，主要处理来自网关解析的各种 id（staffId，businessId），进行简单的鉴权功能。

### Distributed Tracing（gRPC，Http）

使用 `Sentry` 实现分布式追踪功能（该功能暂时处理关闭状态）。

### Grey（gRPC，Http）

灰度调用功能，主要用于支持分支测试，支持 `FeignClient` 和 `gRPC Client`。

### Logging（gRPC，Http）

日志功能，打印请求日志（该功能暂时处理关闭状态，还需要完善）。

### Metrics（gRPC，Http）

使用 `Prometheus` 进行指标采集，扩展了线程池状态和方法执行时间的指标。

### Validation（gRPC）

针对 gRPC 使用 [Pgv](https://github.com/bufbuild/protoc-gen-validate) 进行参数校验，客户端和服务端都支持。Http 服务有
jakarta.validation，所以不需要处理。

### Health Check（gRPC）

健康检查功能，主要针对 gRPC 服务，暂时提供了 MySQL 和 Redis 的健康检查。对于 Http 服务，使用 `Spring Boot`
的默认处理方式（actuator）。

### Exception Handler（gRPC）

异常处理功能，主要针对 gRPC 服务， 对于 Http 服务，使用 `Spring Boot` 的默认处理方式（@ExceptionHandler）。

### Exception Tracker（gRPC，Http）

异常追踪功能，主要针对 gRPC 服务，采用 Sentry 实现（sentry-spring-boot-starter），未处理的异常会上报到 Sentry Server。

### Swagger（Http）

该功能只针对 Http 服务可用，生成 Swagger 文档。

### 动态数据源

这个模块提供了 Mybatis 动态数据源的支持。

#### 添加配置

```yaml
spring:
  datasource: # This configuration will ALWAYS be used as the default datasource.
    url: ***************************
    username: root
    password: root
moego:
  data-sources:
    - name: slave1
      url: ***************************
      username: root
      password: root
    - name: slave2
      url: ***************************
      username: root
      password: root
```

#### 继承 `DynamicDataSource`

```java
@Mapper
public interface AppointmentMapper extends DynamicDataSource<AppointmentMapper> {
    Appointment selectByPrimaryKey(@Param("id") Long id);
}
```

#### 使用

使用 `DynamicDataSource` 接口的 `useDataSource` 方法来切换数据源。 

```java
@Service
@RequiredArgsConstructor
public class AppointmentService {

    private final AppointmentMapper appointmentMapper;

    public Appointment getAppointmentWithMainDataSource(long id) {
        return appointmentMapper.selectByPrimaryKey(id);
    }

    public Appointment getAppointmentWithSlaveDataSource(long id) {
        // 和 moego.data-sources[].name 一致
        return appointmentMapper.useDataSource("slave1").selectByPrimaryKey(id);
    }
}
```

#### 添加插件

因为 mapper 数量较多，为每个 mapper 手动添加 `DynamicDataSource` 会很麻烦，并且 mapper 在重新运行 `mbGenerator` 后会被覆盖，
所以提供了一个插件来支持 mapper 自动继承 `DynamicDataSource`。

在 `gradle.build` 添加依赖：
```gradle
mybatisGenerator {
    // ...

    dependencies {
        mybatisGenerator("org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}")
        mybatisGenerator("com.mysql:mysql-connector-j")
        mybatisGenerator("com.moego.lib:moego-lib-mybatis-plugins")
    }
}

// mbGenerator needs moego-lib-mybatis-plugins.jar, so we need to make sure it is built before mbGenerator
mbGenerator.dependsOn gradle.includedBuild("moego-java-lib").task(":moego-lib-mybatis-plugins:jar")
```

在 `MyBatisGeneratorConfig.xml` 添加插件：

```xml
<plugin type="com.moego.lib.common.mybatisplugins.DynamicDataSourcePlugin"/>
```

参考 [moego-server-business](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/src/main/resources/MyBatisGeneratorConfig.xml#L14)。

#### 参考改动

- [build.gradle](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/build.gradle#L78)
- [MyBatisGeneratorConfig.xml](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/src/main/resources/MyBatisGeneratorConfig.xml#L14)
- [application-local.yml](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/src/main/resources/application-local.yml#L7), [application.prod.yml](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/src/main/resources/application-prod.yml#L10)
- [MoeBusinessMapper.java](https://github.com/MoeGolibrary/moego-server-business/blob/main/moego-server-business/src/main/java/com/moego/server/business/mapper/MoeBusinessMapper.java#L11)

#### 实现原理

当 mapper 调用 `useDataSource` 方法时，会使用传入的数据源创建一个新的 mapper 对象。

参考代码：`MyBatisDynamicDataSourceBeanPostProcessor`。

### 参考示例

基本使用可以参考 [svc-todo](https://github.com/MoeGolibrary/moego-svc-todo)。
