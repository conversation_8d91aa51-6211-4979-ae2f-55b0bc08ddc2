// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/grooming/v1/online_booking_config_api.proto

package groomingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OnlineBookingConfigServiceClient is the client API for OnlineBookingConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnlineBookingConfigServiceClient interface {
	// get business ob config
	GetOnlineBookingConfig(ctx context.Context, in *GetOnlineBookingConfigRequest, opts ...grpc.CallOption) (*GetOnlineBookingConfigResponse, error)
}

type onlineBookingConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOnlineBookingConfigServiceClient(cc grpc.ClientConnInterface) OnlineBookingConfigServiceClient {
	return &onlineBookingConfigServiceClient{cc}
}

func (c *onlineBookingConfigServiceClient) GetOnlineBookingConfig(ctx context.Context, in *GetOnlineBookingConfigRequest, opts ...grpc.CallOption) (*GetOnlineBookingConfigResponse, error) {
	out := new(GetOnlineBookingConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v1.OnlineBookingConfigService/GetOnlineBookingConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnlineBookingConfigServiceServer is the server API for OnlineBookingConfigService service.
// All implementations must embed UnimplementedOnlineBookingConfigServiceServer
// for forward compatibility
type OnlineBookingConfigServiceServer interface {
	// get business ob config
	GetOnlineBookingConfig(context.Context, *GetOnlineBookingConfigRequest) (*GetOnlineBookingConfigResponse, error)
	mustEmbedUnimplementedOnlineBookingConfigServiceServer()
}

// UnimplementedOnlineBookingConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOnlineBookingConfigServiceServer struct {
}

func (UnimplementedOnlineBookingConfigServiceServer) GetOnlineBookingConfig(context.Context, *GetOnlineBookingConfigRequest) (*GetOnlineBookingConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineBookingConfig not implemented")
}
func (UnimplementedOnlineBookingConfigServiceServer) mustEmbedUnimplementedOnlineBookingConfigServiceServer() {
}

// UnsafeOnlineBookingConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnlineBookingConfigServiceServer will
// result in compilation errors.
type UnsafeOnlineBookingConfigServiceServer interface {
	mustEmbedUnimplementedOnlineBookingConfigServiceServer()
}

func RegisterOnlineBookingConfigServiceServer(s grpc.ServiceRegistrar, srv OnlineBookingConfigServiceServer) {
	s.RegisterService(&OnlineBookingConfigService_ServiceDesc, srv)
}

func _OnlineBookingConfigService_GetOnlineBookingConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineBookingConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnlineBookingConfigServiceServer).GetOnlineBookingConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v1.OnlineBookingConfigService/GetOnlineBookingConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnlineBookingConfigServiceServer).GetOnlineBookingConfig(ctx, req.(*GetOnlineBookingConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OnlineBookingConfigService_ServiceDesc is the grpc.ServiceDesc for OnlineBookingConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OnlineBookingConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.grooming.v1.OnlineBookingConfigService",
	HandlerType: (*OnlineBookingConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnlineBookingConfig",
			Handler:    _OnlineBookingConfigService_GetOnlineBookingConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/grooming/v1/online_booking_config_api.proto",
}
