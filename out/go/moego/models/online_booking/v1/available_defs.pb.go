// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/available_defs.proto

package onlinebookingpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// available pet metadata definition
type AvailablePetMetadataDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// allowed pet type
	IsAllowedPetType bool `protobuf:"varint,2,opt,name=is_allowed_pet_type,json=isAllowedPetType,proto3" json:"is_allowed_pet_type,omitempty"`
	// allowed pet weight
	IsAllowedWeight bool `protobuf:"varint,3,opt,name=is_allowed_weight,json=isAllowedWeight,proto3" json:"is_allowed_weight,omitempty"`
}

func (x *AvailablePetMetadataDef) Reset() {
	*x = AvailablePetMetadataDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailablePetMetadataDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetMetadataDef) ProtoMessage() {}

func (x *AvailablePetMetadataDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetMetadataDef.ProtoReflect.Descriptor instead.
func (*AvailablePetMetadataDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{0}
}

func (x *AvailablePetMetadataDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AvailablePetMetadataDef) GetIsAllowedPetType() bool {
	if x != nil {
		return x.IsAllowedPetType
	}
	return false
}

func (x *AvailablePetMetadataDef) GetIsAllowedWeight() bool {
	if x != nil {
		return x.IsAllowedWeight
	}
	return false
}

// available pet definition
type AvailablePetDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff's first available date
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *AvailablePetDef) Reset() {
	*x = AvailablePetDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailablePetDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailablePetDef) ProtoMessage() {}

func (x *AvailablePetDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailablePetDef.ProtoReflect.Descriptor instead.
func (*AvailablePetDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{1}
}

func (x *AvailablePetDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AvailablePetDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

// pet's available service definition
type AvailableServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// applicable service id list
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// applicable add-on id list
	AddonIds []int64 `protobuf:"varint,3,rep,packed,name=addon_ids,json=addonIds,proto3" json:"addon_ids,omitempty"`
}

func (x *AvailableServiceDef) Reset() {
	*x = AvailableServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableServiceDef) ProtoMessage() {}

func (x *AvailableServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableServiceDef.ProtoReflect.Descriptor instead.
func (*AvailableServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{2}
}

func (x *AvailableServiceDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AvailableServiceDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *AvailableServiceDef) GetAddonIds() []int64 {
	if x != nil {
		return x.AddonIds
	}
	return nil
}

// pet's available staff definition
type AvailableStaffDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// staff's available pet list
	AvailablePets []*AvailablePetDef `protobuf:"bytes,6,rep,name=available_pets,json=availablePets,proto3" json:"available_pets,omitempty"`
}

func (x *AvailableStaffDef) Reset() {
	*x = AvailableStaffDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableStaffDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaffDef) ProtoMessage() {}

func (x *AvailableStaffDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaffDef.ProtoReflect.Descriptor instead.
func (*AvailableStaffDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{3}
}

func (x *AvailableStaffDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AvailableStaffDef) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *AvailableStaffDef) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *AvailableStaffDef) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *AvailableStaffDef) GetAvailablePets() []*AvailablePetDef {
	if x != nil {
		return x.AvailablePets
	}
	return nil
}

// staff first available date definition
type AvailableStaffDateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// first available date
	FirstAvailableDate string `protobuf:"bytes,2,opt,name=first_available_date,json=firstAvailableDate,proto3" json:"first_available_date,omitempty"`
}

func (x *AvailableStaffDateDef) Reset() {
	*x = AvailableStaffDateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableStaffDateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaffDateDef) ProtoMessage() {}

func (x *AvailableStaffDateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaffDateDef.ProtoReflect.Descriptor instead.
func (*AvailableStaffDateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{4}
}

func (x *AvailableStaffDateDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AvailableStaffDateDef) GetFirstAvailableDate() string {
	if x != nil {
		return x.FirstAvailableDate
	}
	return ""
}

// available date definition
type AvailableDateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// is available in the morning
	IsAm bool `protobuf:"varint,2,opt,name=is_am,json=isAm,proto3" json:"is_am,omitempty"`
	// is available in the afternoon
	IsPm bool `protobuf:"varint,3,opt,name=is_pm,json=isPm,proto3" json:"is_pm,omitempty"`
}

func (x *AvailableDateDef) Reset() {
	*x = AvailableDateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableDateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableDateDef) ProtoMessage() {}

func (x *AvailableDateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableDateDef.ProtoReflect.Descriptor instead.
func (*AvailableDateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{5}
}

func (x *AvailableDateDef) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AvailableDateDef) GetIsAm() bool {
	if x != nil {
		return x.IsAm
	}
	return false
}

func (x *AvailableDateDef) GetIsPm() bool {
	if x != nil {
		return x.IsPm
	}
	return false
}

// available time slot definition
type AvailableTimeslotDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// is available in the morning
	IsAm bool `protobuf:"varint,2,opt,name=is_am,json=isAm,proto3" json:"is_am,omitempty"`
	// is available in the afternoon
	IsPm bool `protobuf:"varint,3,opt,name=is_pm,json=isPm,proto3" json:"is_pm,omitempty"`
	// available staff list for the specific day
	Staffs []*AvailableStaffSpecificDayDef `protobuf:"bytes,4,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *AvailableTimeslotDef) Reset() {
	*x = AvailableTimeslotDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableTimeslotDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableTimeslotDef) ProtoMessage() {}

func (x *AvailableTimeslotDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableTimeslotDef.ProtoReflect.Descriptor instead.
func (*AvailableTimeslotDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{6}
}

func (x *AvailableTimeslotDef) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AvailableTimeslotDef) GetIsAm() bool {
	if x != nil {
		return x.IsAm
	}
	return false
}

func (x *AvailableTimeslotDef) GetIsPm() bool {
	if x != nil {
		return x.IsPm
	}
	return false
}

func (x *AvailableTimeslotDef) GetStaffs() []*AvailableStaffSpecificDayDef {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// pet's available staff definition
type AvailableStaffSpecificDayDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// staff's AM available time slots
	AmTimeslots []int32 `protobuf:"varint,4,rep,packed,name=am_timeslots,json=amTimeslots,proto3" json:"am_timeslots,omitempty"`
	// staff's PM available time slots
	PmTimeslots []int32 `protobuf:"varint,5,rep,packed,name=pm_timeslots,json=pmTimeslots,proto3" json:"pm_timeslots,omitempty"`
}

func (x *AvailableStaffSpecificDayDef) Reset() {
	*x = AvailableStaffSpecificDayDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableStaffSpecificDayDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaffSpecificDayDef) ProtoMessage() {}

func (x *AvailableStaffSpecificDayDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaffSpecificDayDef.ProtoReflect.Descriptor instead.
func (*AvailableStaffSpecificDayDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{7}
}

func (x *AvailableStaffSpecificDayDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AvailableStaffSpecificDayDef) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *AvailableStaffSpecificDayDef) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *AvailableStaffSpecificDayDef) GetAmTimeslots() []int32 {
	if x != nil {
		return x.AmTimeslots
	}
	return nil
}

func (x *AvailableStaffSpecificDayDef) GetPmTimeslots() []int32 {
	if x != nil {
		return x.PmTimeslots
	}
	return nil
}

// available working hour range definition
type AvailableWorkingHourRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// start time
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *AvailableWorkingHourRangeDef) Reset() {
	*x = AvailableWorkingHourRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableWorkingHourRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableWorkingHourRangeDef) ProtoMessage() {}

func (x *AvailableWorkingHourRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableWorkingHourRangeDef.ProtoReflect.Descriptor instead.
func (*AvailableWorkingHourRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{8}
}

func (x *AvailableWorkingHourRangeDef) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AvailableWorkingHourRangeDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AvailableWorkingHourRangeDef) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// staff's available working hour range definition
type AvailableStaffWorkingHourRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// working hour range list
	WorkingHourRanges []*AvailableWorkingHourRangeDef `protobuf:"bytes,2,rep,name=working_hour_ranges,json=workingHourRanges,proto3" json:"working_hour_ranges,omitempty"`
}

func (x *AvailableStaffWorkingHourRangeDef) Reset() {
	*x = AvailableStaffWorkingHourRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableStaffWorkingHourRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaffWorkingHourRangeDef) ProtoMessage() {}

func (x *AvailableStaffWorkingHourRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_available_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaffWorkingHourRangeDef.ProtoReflect.Descriptor instead.
func (*AvailableStaffWorkingHourRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP(), []int{9}
}

func (x *AvailableStaffWorkingHourRangeDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AvailableStaffWorkingHourRangeDef) GetWorkingHourRanges() []*AvailableWorkingHourRangeDef {
	if x != nil {
		return x.WorkingHourRanges
	}
	return nil
}

var File_moego_models_online_booking_v1_available_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_available_defs_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94,
	0x01, 0x0a, 0x17, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x54, 0x0a, 0x0f, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x13,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d,
	0x08, 0x01, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42,
	0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xf9, 0x01, 0x0a, 0x11,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65,
	0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x63, 0x0a, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x70, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x52, 0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x15, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x14, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b,
	0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52,
	0x12, 0x66, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x22, 0x6c, 0x0a, 0x10, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x69, 0x73, 0x5f, 0x61, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x41, 0x6d, 0x12, 0x13, 0x0a, 0x05,
	0x69, 0x73, 0x5f, 0x70, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x50,
	0x6d, 0x22, 0xd3, 0x01, 0x0a, 0x14, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x69, 0x73,
	0x5f, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x41, 0x6d, 0x12,
	0x13, 0x0a, 0x05, 0x69, 0x73, 0x5f, 0x70, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04,
	0x69, 0x73, 0x50, 0x6d, 0x12, 0x61, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x79, 0x44,
	0x65, 0x66, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xf4, 0x01, 0x0a, 0x1c, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x61, 0x6d, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x42, 0x16,
	0xfa, 0x42, 0x13, 0x92, 0x01, 0x10, 0x08, 0x01, 0x10, 0xa0, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x1a,
	0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x0b, 0x61, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c,
	0x6f, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c,
	0x6f, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x42, 0x16, 0xfa, 0x42, 0x13, 0x92, 0x01,
	0x10, 0x08, 0x01, 0x10, 0xa0, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28,
	0x00, 0x52, 0x0b, 0x70, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0xa0,
	0x01, 0x0a, 0x1c, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xc2, 0x01, 0x0a, 0x21, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x79, 0x0a, 0x13, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01,
	0x10, 0xe8, 0x07, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_available_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_available_defs_proto_rawDescData = file_moego_models_online_booking_v1_available_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_available_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_available_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_available_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_available_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_available_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_available_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_online_booking_v1_available_defs_proto_goTypes = []interface{}{
	(*AvailablePetMetadataDef)(nil),           // 0: moego.models.online_booking.v1.AvailablePetMetadataDef
	(*AvailablePetDef)(nil),                   // 1: moego.models.online_booking.v1.AvailablePetDef
	(*AvailableServiceDef)(nil),               // 2: moego.models.online_booking.v1.AvailableServiceDef
	(*AvailableStaffDef)(nil),                 // 3: moego.models.online_booking.v1.AvailableStaffDef
	(*AvailableStaffDateDef)(nil),             // 4: moego.models.online_booking.v1.AvailableStaffDateDef
	(*AvailableDateDef)(nil),                  // 5: moego.models.online_booking.v1.AvailableDateDef
	(*AvailableTimeslotDef)(nil),              // 6: moego.models.online_booking.v1.AvailableTimeslotDef
	(*AvailableStaffSpecificDayDef)(nil),      // 7: moego.models.online_booking.v1.AvailableStaffSpecificDayDef
	(*AvailableWorkingHourRangeDef)(nil),      // 8: moego.models.online_booking.v1.AvailableWorkingHourRangeDef
	(*AvailableStaffWorkingHourRangeDef)(nil), // 9: moego.models.online_booking.v1.AvailableStaffWorkingHourRangeDef
}
var file_moego_models_online_booking_v1_available_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.online_booking.v1.AvailableStaffDef.available_pets:type_name -> moego.models.online_booking.v1.AvailablePetDef
	7, // 1: moego.models.online_booking.v1.AvailableTimeslotDef.staffs:type_name -> moego.models.online_booking.v1.AvailableStaffSpecificDayDef
	8, // 2: moego.models.online_booking.v1.AvailableStaffWorkingHourRangeDef.working_hour_ranges:type_name -> moego.models.online_booking.v1.AvailableWorkingHourRangeDef
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_available_defs_proto_init() }
func file_moego_models_online_booking_v1_available_defs_proto_init() {
	if File_moego_models_online_booking_v1_available_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailablePetMetadataDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailablePetDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableStaffDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableStaffDateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableDateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableTimeslotDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableStaffSpecificDayDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableWorkingHourRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_available_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableStaffWorkingHourRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_available_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_available_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_available_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_available_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_available_defs_proto = out.File
	file_moego_models_online_booking_v1_available_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_available_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_available_defs_proto_depIdxs = nil
}
