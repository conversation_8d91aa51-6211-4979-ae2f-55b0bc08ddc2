// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/pricing_rule_defs.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pricing rule upsert definition
type PricingRuleUpsertDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// apply service item type
	ServiceItemType ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// apply service type
	ServiceType ServiceType `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// rule name
	RuleName string `protobuf:"bytes,5,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// apply to all service
	IsAllServiceApplicable bool `protobuf:"varint,6,opt,name=is_all_service_applicable,json=isAllServiceApplicable,proto3" json:"is_all_service_applicable,omitempty"`
	// selected service ids, only effective when all_service is false
	SelectedServices []int64 `protobuf:"varint,7,rep,packed,name=selected_services,json=selectedServices,proto3" json:"selected_services,omitempty"`
	// rule configuration
	RuleConfiguration *PricingRuleConfigurationDef `protobuf:"bytes,8,opt,name=rule_configuration,json=ruleConfiguration,proto3" json:"rule_configuration,omitempty"`
	// active, true means active, false means inactive
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// rule group type
	RuleGroupType *RuleGroupType `protobuf:"varint,10,opt,name=rule_group_type,json=ruleGroupType,proto3,enum=moego.models.offering.v1.RuleGroupType,oneof" json:"rule_group_type,omitempty"`
	// rule apply choice type
	RuleApplyChoiceType *RuleApplyChoiceType `protobuf:"varint,11,opt,name=rule_apply_choice_type,json=ruleApplyChoiceType,proto3,enum=moego.models.offering.v1.RuleApplyChoiceType,oneof" json:"rule_apply_choice_type,omitempty"`
}

func (x *PricingRuleUpsertDef) Reset() {
	*x = PricingRuleUpsertDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleUpsertDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleUpsertDef) ProtoMessage() {}

func (x *PricingRuleUpsertDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleUpsertDef.ProtoReflect.Descriptor instead.
func (*PricingRuleUpsertDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PricingRuleUpsertDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *PricingRuleUpsertDef) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *PricingRuleUpsertDef) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *PricingRuleUpsertDef) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *PricingRuleUpsertDef) GetIsAllServiceApplicable() bool {
	if x != nil {
		return x.IsAllServiceApplicable
	}
	return false
}

func (x *PricingRuleUpsertDef) GetSelectedServices() []int64 {
	if x != nil {
		return x.SelectedServices
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetRuleConfiguration() *PricingRuleConfigurationDef {
	if x != nil {
		return x.RuleConfiguration
	}
	return nil
}

func (x *PricingRuleUpsertDef) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *PricingRuleUpsertDef) GetRuleGroupType() RuleGroupType {
	if x != nil && x.RuleGroupType != nil {
		return *x.RuleGroupType
	}
	return RuleGroupType_RULE_GROUP_TYPE_UNSPECIFIED
}

func (x *PricingRuleUpsertDef) GetRuleApplyChoiceType() RuleApplyChoiceType {
	if x != nil && x.RuleApplyChoiceType != nil {
		return *x.RuleApplyChoiceType
	}
	return RuleApplyChoiceType_RULE_GROUP_APPLY_CHOICE_UNSPECIFIED
}

// pricing rule configuration definition
type PricingRuleConfigurationDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule items
	PricingRuleItemDefs []*PricingRuleConfigurationDef_PricingRuleItemDef `protobuf:"bytes,1,rep,name=pricing_rule_item_defs,json=pricingRuleItemDefs,proto3" json:"pricing_rule_item_defs,omitempty"`
}

func (x *PricingRuleConfigurationDef) Reset() {
	*x = PricingRuleConfigurationDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleConfigurationDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleConfigurationDef) ProtoMessage() {}

func (x *PricingRuleConfigurationDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleConfigurationDef.ProtoReflect.Descriptor instead.
func (*PricingRuleConfigurationDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{1}
}

func (x *PricingRuleConfigurationDef) GetPricingRuleItemDefs() []*PricingRuleConfigurationDef_PricingRuleItemDef {
	if x != nil {
		return x.PricingRuleItemDefs
	}
	return nil
}

// pricing rule item definition
type MultiPetDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item def
	ItemDefs []*MultiPetDef_MultiPetItemDef `protobuf:"bytes,1,rep,name=item_defs,json=itemDefs,proto3" json:"item_defs,omitempty"`
	// rule apply type, apply to each one/apply to additional
	RuleApplyType RuleApplyType `protobuf:"varint,2,opt,name=rule_apply_type,json=ruleApplyType,proto3,enum=moego.models.offering.v1.RuleApplyType" json:"rule_apply_type,omitempty"`
	// same lodging unit, only effective when rule_item_type is multiple pet
	NeedInSameLodging bool `protobuf:"varint,3,opt,name=need_in_same_lodging,json=needInSameLodging,proto3" json:"need_in_same_lodging,omitempty"`
}

func (x *MultiPetDef) Reset() {
	*x = MultiPetDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiPetDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiPetDef) ProtoMessage() {}

func (x *MultiPetDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiPetDef.ProtoReflect.Descriptor instead.
func (*MultiPetDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{2}
}

func (x *MultiPetDef) GetItemDefs() []*MultiPetDef_MultiPetItemDef {
	if x != nil {
		return x.ItemDefs
	}
	return nil
}

func (x *MultiPetDef) GetRuleApplyType() RuleApplyType {
	if x != nil {
		return x.RuleApplyType
	}
	return RuleApplyType_RULE_APPLY_TYPE_UNSPECIFIED
}

func (x *MultiPetDef) GetNeedInSameLodging() bool {
	if x != nil {
		return x.NeedInSameLodging
	}
	return false
}

// pricing rule item definition
type MultiNightDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item def
	ItemDefs []*MultiNightDef_MultiNightItemDef `protobuf:"bytes,1,rep,name=item_defs,json=itemDefs,proto3" json:"item_defs,omitempty"`
}

func (x *MultiNightDef) Reset() {
	*x = MultiNightDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiNightDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiNightDef) ProtoMessage() {}

func (x *MultiNightDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiNightDef.ProtoReflect.Descriptor instead.
func (*MultiNightDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{3}
}

func (x *MultiNightDef) GetItemDefs() []*MultiNightDef_MultiNightItemDef {
	if x != nil {
		return x.ItemDefs
	}
	return nil
}

// pricing rule item definition
type PeakDateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item def
	ItemDefs []*PeakDateDef_PeakDateItemDef `protobuf:"bytes,1,rep,name=item_defs,json=itemDefs,proto3" json:"item_defs,omitempty"`
}

func (x *PeakDateDef) Reset() {
	*x = PeakDateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeakDateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeakDateDef) ProtoMessage() {}

func (x *PeakDateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PeakDateDef.ProtoReflect.Descriptor instead.
func (*PeakDateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{4}
}

func (x *PeakDateDef) GetItemDefs() []*PeakDateDef_PeakDateItemDef {
	if x != nil {
		return x.ItemDefs
	}
	return nil
}

// pricing rule item definition
type PriceItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule price type, fixed price/percentage price
	PriceType RulePriceType `protobuf:"varint,1,opt,name=price_type,json=priceType,proto3,enum=moego.models.offering.v1.RulePriceType" json:"price_type,omitempty"`
	// rule price value
	PriceValue float64 `protobuf:"fixed64,2,opt,name=price_value,json=priceValue,proto3" json:"price_value,omitempty"`
}

func (x *PriceItemDef) Reset() {
	*x = PriceItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceItemDef) ProtoMessage() {}

func (x *PriceItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceItemDef.ProtoReflect.Descriptor instead.
func (*PriceItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{5}
}

func (x *PriceItemDef) GetPriceType() RulePriceType {
	if x != nil {
		return x.PriceType
	}
	return RulePriceType_RULE_PRICE_TYPE_UNSPECIFIED
}

func (x *PriceItemDef) GetPriceValue() float64 {
	if x != nil {
		return x.PriceValue
	}
	return 0
}

// pet detail calculate definition, used for pricing rule calculation
type PetDetailCalculateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id, not evaluation id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,4,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// night number
	NightNumber *int32 `protobuf:"varint,3,opt,name=night_number,json=nightNumber,proto3,oneof" json:"night_number,omitempty"`
	// lodging unit id
	LodgingUnitId *int64 `protobuf:"varint,5,opt,name=lodging_unit_id,json=lodgingUnitId,proto3,oneof" json:"lodging_unit_id,omitempty"`
	// scope type price, apply to other services
	ScopeTypePrice *ServiceScopeType `protobuf:"varint,11,opt,name=scope_type_price,json=scopeTypePrice,proto3,enum=moego.models.offering.v1.ServiceScopeType,oneof" json:"scope_type_price,omitempty"`
	// service date, for boarding service
	ServiceDate *string `protobuf:"bytes,12,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
}

func (x *PetDetailCalculateDef) Reset() {
	*x = PetDetailCalculateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailCalculateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailCalculateDef) ProtoMessage() {}

func (x *PetDetailCalculateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailCalculateDef.ProtoReflect.Descriptor instead.
func (*PetDetailCalculateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{6}
}

func (x *PetDetailCalculateDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PetDetailCalculateDef) GetNightNumber() int32 {
	if x != nil && x.NightNumber != nil {
		return *x.NightNumber
	}
	return 0
}

func (x *PetDetailCalculateDef) GetLodgingUnitId() int64 {
	if x != nil && x.LodgingUnitId != nil {
		return *x.LodgingUnitId
	}
	return 0
}

func (x *PetDetailCalculateDef) GetScopeTypePrice() ServiceScopeType {
	if x != nil && x.ScopeTypePrice != nil {
		return *x.ScopeTypePrice
	}
	return ServiceScopeType_SERVICE_SCOPE_TYPE_UNSPECIFIED
}

func (x *PetDetailCalculateDef) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

// pet detail calculate result definition, used for pricing rule calculation
type PetDetailCalculateResultDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service price after calculation
	ServicePrice float64 `protobuf:"fixed64,4,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// used pricing rule id, empty means for preview, deprecated, use applied_rule_ids
	//
	// Deprecated: Do not use.
	Id *int64 `protobuf:"varint,5,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// service date
	ServiceDate *string `protobuf:"bytes,6,opt,name=service_date,json=serviceDate,proto3,oneof" json:"service_date,omitempty"`
	// applied rule ids,  empty means for preview
	AppliedRuleIds []int64 `protobuf:"varint,7,rep,packed,name=applied_rule_ids,json=appliedRuleIds,proto3" json:"applied_rule_ids,omitempty"`
}

func (x *PetDetailCalculateResultDef) Reset() {
	*x = PetDetailCalculateResultDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailCalculateResultDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailCalculateResultDef) ProtoMessage() {}

func (x *PetDetailCalculateResultDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailCalculateResultDef.ProtoReflect.Descriptor instead.
func (*PetDetailCalculateResultDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{7}
}

func (x *PetDetailCalculateResultDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

// Deprecated: Do not use.
func (x *PetDetailCalculateResultDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *PetDetailCalculateResultDef) GetServiceDate() string {
	if x != nil && x.ServiceDate != nil {
		return *x.ServiceDate
	}
	return ""
}

func (x *PetDetailCalculateResultDef) GetAppliedRuleIds() []int64 {
	if x != nil {
		return x.AppliedRuleIds
	}
	return nil
}

// list pricing rule filter
type ListPricingRuleFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item: grooming/boarding/daycare
	ServiceItemType *ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// service type: service/addon
	ServiceType *ServiceType `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"service_type,omitempty"`
	// active
	IsActive *bool `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// rule group type
	RuleGroupType *RuleGroupType `protobuf:"varint,4,opt,name=rule_group_type,json=ruleGroupType,proto3,enum=moego.models.offering.v1.RuleGroupType,oneof" json:"rule_group_type,omitempty"`
	// include deleted
	IncludeDeleted *bool `protobuf:"varint,5,opt,name=include_deleted,json=includeDeleted,proto3,oneof" json:"include_deleted,omitempty"`
}

func (x *ListPricingRuleFilter) Reset() {
	*x = ListPricingRuleFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleFilter) ProtoMessage() {}

func (x *ListPricingRuleFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleFilter.ProtoReflect.Descriptor instead.
func (*ListPricingRuleFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{8}
}

func (x *ListPricingRuleFilter) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ListPricingRuleFilter) GetServiceType() ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ListPricingRuleFilter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListPricingRuleFilter) GetRuleGroupType() RuleGroupType {
	if x != nil && x.RuleGroupType != nil {
		return *x.RuleGroupType
	}
	return RuleGroupType_RULE_GROUP_TYPE_UNSPECIFIED
}

func (x *ListPricingRuleFilter) GetIncludeDeleted() bool {
	if x != nil && x.IncludeDeleted != nil {
		return *x.IncludeDeleted
	}
	return false
}

// pricing rule item definition
type PricingRuleConfigurationDef_PricingRuleItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item
	//
	// Types that are assignable to Item:
	//
	//	*PricingRuleConfigurationDef_PricingRuleItemDef_MultiPetDef
	//	*PricingRuleConfigurationDef_PricingRuleItemDef_MultiNightDef
	//	*PricingRuleConfigurationDef_PricingRuleItemDef_PeakDateDef
	Item isPricingRuleConfigurationDef_PricingRuleItemDef_Item `protobuf_oneof:"item"`
}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) Reset() {
	*x = PricingRuleConfigurationDef_PricingRuleItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingRuleConfigurationDef_PricingRuleItemDef) ProtoMessage() {}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingRuleConfigurationDef_PricingRuleItemDef.ProtoReflect.Descriptor instead.
func (*PricingRuleConfigurationDef_PricingRuleItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{1, 0}
}

func (m *PricingRuleConfigurationDef_PricingRuleItemDef) GetItem() isPricingRuleConfigurationDef_PricingRuleItemDef_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) GetMultiPetDef() *MultiPetDef {
	if x, ok := x.GetItem().(*PricingRuleConfigurationDef_PricingRuleItemDef_MultiPetDef); ok {
		return x.MultiPetDef
	}
	return nil
}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) GetMultiNightDef() *MultiNightDef {
	if x, ok := x.GetItem().(*PricingRuleConfigurationDef_PricingRuleItemDef_MultiNightDef); ok {
		return x.MultiNightDef
	}
	return nil
}

func (x *PricingRuleConfigurationDef_PricingRuleItemDef) GetPeakDateDef() *PeakDateDef {
	if x, ok := x.GetItem().(*PricingRuleConfigurationDef_PricingRuleItemDef_PeakDateDef); ok {
		return x.PeakDateDef
	}
	return nil
}

type isPricingRuleConfigurationDef_PricingRuleItemDef_Item interface {
	isPricingRuleConfigurationDef_PricingRuleItemDef_Item()
}

type PricingRuleConfigurationDef_PricingRuleItemDef_MultiPetDef struct {
	// multiple pets
	MultiPetDef *MultiPetDef `protobuf:"bytes,1,opt,name=multi_pet_def,json=multiPetDef,proto3,oneof"`
}

type PricingRuleConfigurationDef_PricingRuleItemDef_MultiNightDef struct {
	// multiple nights/days
	MultiNightDef *MultiNightDef `protobuf:"bytes,2,opt,name=multi_night_def,json=multiNightDef,proto3,oneof"`
}

type PricingRuleConfigurationDef_PricingRuleItemDef_PeakDateDef struct {
	// peak date
	PeakDateDef *PeakDateDef `protobuf:"bytes,3,opt,name=peak_date_def,json=peakDateDef,proto3,oneof"`
}

func (*PricingRuleConfigurationDef_PricingRuleItemDef_MultiPetDef) isPricingRuleConfigurationDef_PricingRuleItemDef_Item() {
}

func (*PricingRuleConfigurationDef_PricingRuleItemDef_MultiNightDef) isPricingRuleConfigurationDef_PricingRuleItemDef_Item() {
}

func (*PricingRuleConfigurationDef_PricingRuleItemDef_PeakDateDef) isPricingRuleConfigurationDef_PricingRuleItemDef_Item() {
}

// Multi Pet Item Model
type MultiPetDef_MultiPetItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price detail
	PriceItemDef *PriceItemDef `protobuf:"bytes,1,opt,name=price_item_def,json=priceItemDef,proto3" json:"price_item_def,omitempty"`
	// number above which the rule is effective
	EffectiveNumber int32 `protobuf:"varint,2,opt,name=effective_number,json=effectiveNumber,proto3" json:"effective_number,omitempty"`
}

func (x *MultiPetDef_MultiPetItemDef) Reset() {
	*x = MultiPetDef_MultiPetItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiPetDef_MultiPetItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiPetDef_MultiPetItemDef) ProtoMessage() {}

func (x *MultiPetDef_MultiPetItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiPetDef_MultiPetItemDef.ProtoReflect.Descriptor instead.
func (*MultiPetDef_MultiPetItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{2, 0}
}

func (x *MultiPetDef_MultiPetItemDef) GetPriceItemDef() *PriceItemDef {
	if x != nil {
		return x.PriceItemDef
	}
	return nil
}

func (x *MultiPetDef_MultiPetItemDef) GetEffectiveNumber() int32 {
	if x != nil {
		return x.EffectiveNumber
	}
	return 0
}

// Multi Pet Item Model
type MultiNightDef_MultiNightItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price detail
	PriceItemDef *PriceItemDef `protobuf:"bytes,1,opt,name=price_item_def,json=priceItemDef,proto3" json:"price_item_def,omitempty"`
	// number above which the rule is effective
	EffectiveNumber int32 `protobuf:"varint,2,opt,name=effective_number,json=effectiveNumber,proto3" json:"effective_number,omitempty"`
}

func (x *MultiNightDef_MultiNightItemDef) Reset() {
	*x = MultiNightDef_MultiNightItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiNightDef_MultiNightItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiNightDef_MultiNightItemDef) ProtoMessage() {}

func (x *MultiNightDef_MultiNightItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiNightDef_MultiNightItemDef.ProtoReflect.Descriptor instead.
func (*MultiNightDef_MultiNightItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{3, 0}
}

func (x *MultiNightDef_MultiNightItemDef) GetPriceItemDef() *PriceItemDef {
	if x != nil {
		return x.PriceItemDef
	}
	return nil
}

func (x *MultiNightDef_MultiNightItemDef) GetEffectiveNumber() int32 {
	if x != nil {
		return x.EffectiveNumber
	}
	return 0
}

// Peak date Item Model
type PeakDateDef_PeakDateItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price detail
	PriceItemDef *PriceItemDef `protobuf:"bytes,1,opt,name=price_item_def,json=priceItemDef,proto3" json:"price_item_def,omitempty"`
	// start date which the rule is effective
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date which the rule is effective
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *PeakDateDef_PeakDateItemDef) Reset() {
	*x = PeakDateDef_PeakDateItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeakDateDef_PeakDateItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeakDateDef_PeakDateItemDef) ProtoMessage() {}

func (x *PeakDateDef_PeakDateItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PeakDateDef_PeakDateItemDef.ProtoReflect.Descriptor instead.
func (*PeakDateDef_PeakDateItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP(), []int{4, 0}
}

func (x *PeakDateDef_PeakDateItemDef) GetPriceItemDef() *PriceItemDef {
	if x != nil {
		return x.PriceItemDef
	}
	return nil
}

func (x *PeakDateDef_PeakDateItemDef) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *PeakDateDef_PeakDateItemDef) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

var File_moego_models_offering_v1_pricing_rule_defs_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_pricing_rule_defs_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x06, 0x0a, 0x14, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x13,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x09,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x08, 0x72, 0x75, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x40, 0x0a, 0x11, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10,
	0x92, 0x01, 0x0d, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01,
	0x52, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x6e, 0x0a, 0x12, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x11, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x60, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x0d, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x73, 0x0a, 0x16, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x13,
	0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xb8, 0x03, 0x0a,
	0x1b, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x87, 0x01, 0x0a,
	0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x13, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x65, 0x66, 0x73, 0x1a, 0x8e, 0x02, 0x0a, 0x12, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x4b, 0x0a,
	0x0d, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0b, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x12, 0x51, 0x0a, 0x0f, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x5f, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0d,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x44, 0x65, 0x66, 0x12, 0x4b, 0x0a,
	0x0d, 0x70, 0x65, 0x61, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x61, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0b, 0x70,
	0x65, 0x61, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0b, 0x0a, 0x04, 0x69, 0x74,
	0x65, 0x6d, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x99, 0x03, 0x0a, 0x0b, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x12, 0x5c, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x66, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x69, 0x74, 0x65,
	0x6d, 0x44, 0x65, 0x66, 0x73, 0x12, 0x5b, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x61,
	0x6d, 0x65, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x53, 0x61, 0x6d, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x1a, 0x9d, 0x01, 0x0a, 0x0f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x65, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12,
	0x32, 0x0a, 0x10, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x93, 0x02, 0x0a, 0x0d, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4e, 0x69, 0x67,
	0x68, 0x74, 0x44, 0x65, 0x66, 0x12, 0x60, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x44, 0x65,
	0x66, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x69,
	0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x73, 0x1a, 0x9f, 0x01, 0x0a, 0x11, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x4e, 0x69, 0x67, 0x68, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x56, 0x0a,
	0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x10, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xc9, 0x02, 0x0a, 0x0b, 0x50, 0x65,
	0x61, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x5c, 0x0a, 0x09, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x61, 0x6b, 0x44, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x2e, 0x50, 0x65, 0x61, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x69,
	0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x73, 0x1a, 0xdb, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x61, 0x6b,
	0x44, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x56, 0x0a, 0x0e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x65, 0x66, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x52, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xf1, 0x03, 0x0a, 0x15,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28,
	0x00, 0x48, 0x00, 0x52, 0x0b, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0e, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x42, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e,
	0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x24, 0x48, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xc7, 0x02, 0x0a, 0x1b, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x66, 0x12,
	0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x10, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x64, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x73, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xe9, 0x03, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x66, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x03, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescData = file_moego_models_offering_v1_pricing_rule_defs_proto_rawDesc
)

func file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescData)
	})
	return file_moego_models_offering_v1_pricing_rule_defs_proto_rawDescData
}

var file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_models_offering_v1_pricing_rule_defs_proto_goTypes = []interface{}{
	(*PricingRuleUpsertDef)(nil),                           // 0: moego.models.offering.v1.PricingRuleUpsertDef
	(*PricingRuleConfigurationDef)(nil),                    // 1: moego.models.offering.v1.PricingRuleConfigurationDef
	(*MultiPetDef)(nil),                                    // 2: moego.models.offering.v1.MultiPetDef
	(*MultiNightDef)(nil),                                  // 3: moego.models.offering.v1.MultiNightDef
	(*PeakDateDef)(nil),                                    // 4: moego.models.offering.v1.PeakDateDef
	(*PriceItemDef)(nil),                                   // 5: moego.models.offering.v1.PriceItemDef
	(*PetDetailCalculateDef)(nil),                          // 6: moego.models.offering.v1.PetDetailCalculateDef
	(*PetDetailCalculateResultDef)(nil),                    // 7: moego.models.offering.v1.PetDetailCalculateResultDef
	(*ListPricingRuleFilter)(nil),                          // 8: moego.models.offering.v1.ListPricingRuleFilter
	(*PricingRuleConfigurationDef_PricingRuleItemDef)(nil), // 9: moego.models.offering.v1.PricingRuleConfigurationDef.PricingRuleItemDef
	(*MultiPetDef_MultiPetItemDef)(nil),                    // 10: moego.models.offering.v1.MultiPetDef.MultiPetItemDef
	(*MultiNightDef_MultiNightItemDef)(nil),                // 11: moego.models.offering.v1.MultiNightDef.MultiNightItemDef
	(*PeakDateDef_PeakDateItemDef)(nil),                    // 12: moego.models.offering.v1.PeakDateDef.PeakDateItemDef
	(ServiceItemType)(0),                                   // 13: moego.models.offering.v1.ServiceItemType
	(ServiceType)(0),                                       // 14: moego.models.offering.v1.ServiceType
	(RuleGroupType)(0),                                     // 15: moego.models.offering.v1.RuleGroupType
	(RuleApplyChoiceType)(0),                               // 16: moego.models.offering.v1.RuleApplyChoiceType
	(RuleApplyType)(0),                                     // 17: moego.models.offering.v1.RuleApplyType
	(RulePriceType)(0),                                     // 18: moego.models.offering.v1.RulePriceType
	(ServiceScopeType)(0),                                  // 19: moego.models.offering.v1.ServiceScopeType
}
var file_moego_models_offering_v1_pricing_rule_defs_proto_depIdxs = []int32{
	13, // 0: moego.models.offering.v1.PricingRuleUpsertDef.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	14, // 1: moego.models.offering.v1.PricingRuleUpsertDef.service_type:type_name -> moego.models.offering.v1.ServiceType
	1,  // 2: moego.models.offering.v1.PricingRuleUpsertDef.rule_configuration:type_name -> moego.models.offering.v1.PricingRuleConfigurationDef
	15, // 3: moego.models.offering.v1.PricingRuleUpsertDef.rule_group_type:type_name -> moego.models.offering.v1.RuleGroupType
	16, // 4: moego.models.offering.v1.PricingRuleUpsertDef.rule_apply_choice_type:type_name -> moego.models.offering.v1.RuleApplyChoiceType
	9,  // 5: moego.models.offering.v1.PricingRuleConfigurationDef.pricing_rule_item_defs:type_name -> moego.models.offering.v1.PricingRuleConfigurationDef.PricingRuleItemDef
	10, // 6: moego.models.offering.v1.MultiPetDef.item_defs:type_name -> moego.models.offering.v1.MultiPetDef.MultiPetItemDef
	17, // 7: moego.models.offering.v1.MultiPetDef.rule_apply_type:type_name -> moego.models.offering.v1.RuleApplyType
	11, // 8: moego.models.offering.v1.MultiNightDef.item_defs:type_name -> moego.models.offering.v1.MultiNightDef.MultiNightItemDef
	12, // 9: moego.models.offering.v1.PeakDateDef.item_defs:type_name -> moego.models.offering.v1.PeakDateDef.PeakDateItemDef
	18, // 10: moego.models.offering.v1.PriceItemDef.price_type:type_name -> moego.models.offering.v1.RulePriceType
	19, // 11: moego.models.offering.v1.PetDetailCalculateDef.scope_type_price:type_name -> moego.models.offering.v1.ServiceScopeType
	13, // 12: moego.models.offering.v1.ListPricingRuleFilter.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	14, // 13: moego.models.offering.v1.ListPricingRuleFilter.service_type:type_name -> moego.models.offering.v1.ServiceType
	15, // 14: moego.models.offering.v1.ListPricingRuleFilter.rule_group_type:type_name -> moego.models.offering.v1.RuleGroupType
	2,  // 15: moego.models.offering.v1.PricingRuleConfigurationDef.PricingRuleItemDef.multi_pet_def:type_name -> moego.models.offering.v1.MultiPetDef
	3,  // 16: moego.models.offering.v1.PricingRuleConfigurationDef.PricingRuleItemDef.multi_night_def:type_name -> moego.models.offering.v1.MultiNightDef
	4,  // 17: moego.models.offering.v1.PricingRuleConfigurationDef.PricingRuleItemDef.peak_date_def:type_name -> moego.models.offering.v1.PeakDateDef
	5,  // 18: moego.models.offering.v1.MultiPetDef.MultiPetItemDef.price_item_def:type_name -> moego.models.offering.v1.PriceItemDef
	5,  // 19: moego.models.offering.v1.MultiNightDef.MultiNightItemDef.price_item_def:type_name -> moego.models.offering.v1.PriceItemDef
	5,  // 20: moego.models.offering.v1.PeakDateDef.PeakDateItemDef.price_item_def:type_name -> moego.models.offering.v1.PriceItemDef
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_pricing_rule_defs_proto_init() }
func file_moego_models_offering_v1_pricing_rule_defs_proto_init() {
	if File_moego_models_offering_v1_pricing_rule_defs_proto != nil {
		return
	}
	file_moego_models_offering_v1_pricing_rule_enums_proto_init()
	file_moego_models_offering_v1_service_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleUpsertDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleConfigurationDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiPetDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiNightDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeakDateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailCalculateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailCalculateResultDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricingRuleConfigurationDef_PricingRuleItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiPetDef_MultiPetItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiNightDef_MultiNightItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeakDateDef_PeakDateItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*PricingRuleConfigurationDef_PricingRuleItemDef_MultiPetDef)(nil),
		(*PricingRuleConfigurationDef_PricingRuleItemDef_MultiNightDef)(nil),
		(*PricingRuleConfigurationDef_PricingRuleItemDef_PeakDateDef)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_pricing_rule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_pricing_rule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_pricing_rule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_pricing_rule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_pricing_rule_defs_proto = out.File
	file_moego_models_offering_v1_pricing_rule_defs_proto_rawDesc = nil
	file_moego_models_offering_v1_pricing_rule_defs_proto_goTypes = nil
	file_moego_models_offering_v1_pricing_rule_defs_proto_depIdxs = nil
}
