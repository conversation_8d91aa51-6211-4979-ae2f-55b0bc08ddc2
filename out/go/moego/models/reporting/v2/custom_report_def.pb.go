// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/custom_report_def.proto

package reportingpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SaveCustomReportParams
type SaveCustomReportParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id, exists when update
	DiagramId *string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3,oneof" json:"diagram_id,omitempty"`
	// name of custom report, should be trimmed
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description of custom report
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// metric field keys
	MetricKeys []string `protobuf:"bytes,4,rep,name=metric_keys,json=metricKeys,proto3" json:"metric_keys,omitempty"`
	// dimension fields
	Dimensions []*DimensionField `protobuf:"bytes,5,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
	// saved filters
	SavedFilters []*FilterRequest `protobuf:"bytes,6,rep,name=saved_filters,json=savedFilters,proto3" json:"saved_filters,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode *bool `protobuf:"varint,7,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3,oneof" json:"dynamic_column_mode,omitempty"`
}

func (x *SaveCustomReportParams) Reset() {
	*x = SaveCustomReportParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCustomReportParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCustomReportParams) ProtoMessage() {}

func (x *SaveCustomReportParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCustomReportParams.ProtoReflect.Descriptor instead.
func (*SaveCustomReportParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{0}
}

func (x *SaveCustomReportParams) GetDiagramId() string {
	if x != nil && x.DiagramId != nil {
		return *x.DiagramId
	}
	return ""
}

func (x *SaveCustomReportParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SaveCustomReportParams) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *SaveCustomReportParams) GetMetricKeys() []string {
	if x != nil {
		return x.MetricKeys
	}
	return nil
}

func (x *SaveCustomReportParams) GetDimensions() []*DimensionField {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *SaveCustomReportParams) GetSavedFilters() []*FilterRequest {
	if x != nil {
		return x.SavedFilters
	}
	return nil
}

func (x *SaveCustomReportParams) GetDynamicColumnMode() bool {
	if x != nil && x.DynamicColumnMode != nil {
		return *x.DynamicColumnMode
	}
	return false
}

// SaveCustomReportResult
type SaveCustomReportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// return created or updated diagram_id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
}

func (x *SaveCustomReportResult) Reset() {
	*x = SaveCustomReportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCustomReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCustomReportResult) ProtoMessage() {}

func (x *SaveCustomReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCustomReportResult.ProtoReflect.Descriptor instead.
func (*SaveCustomReportResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{1}
}

func (x *SaveCustomReportResult) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

// ModifyCustomDiagramParams
type ModifyCustomDiagramParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id, required
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// name of custom report, should be trimmed
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description of custom report
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode *bool `protobuf:"varint,4,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3,oneof" json:"dynamic_column_mode,omitempty"`
}

func (x *ModifyCustomDiagramParams) Reset() {
	*x = ModifyCustomDiagramParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCustomDiagramParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCustomDiagramParams) ProtoMessage() {}

func (x *ModifyCustomDiagramParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCustomDiagramParams.ProtoReflect.Descriptor instead.
func (*ModifyCustomDiagramParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{2}
}

func (x *ModifyCustomDiagramParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ModifyCustomDiagramParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ModifyCustomDiagramParams) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ModifyCustomDiagramParams) GetDynamicColumnMode() bool {
	if x != nil && x.DynamicColumnMode != nil {
		return *x.DynamicColumnMode
	}
	return false
}

// ModifyCustomDiagramResult
type ModifyCustomDiagramResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// modify success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ModifyCustomDiagramResult) Reset() {
	*x = ModifyCustomDiagramResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCustomDiagramResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCustomDiagramResult) ProtoMessage() {}

func (x *ModifyCustomDiagramResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCustomDiagramResult.ProtoReflect.Descriptor instead.
func (*ModifyCustomDiagramResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{3}
}

func (x *ModifyCustomDiagramResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// DuplicateCustomReportParams
type DuplicateCustomReportParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
}

func (x *DuplicateCustomReportParams) Reset() {
	*x = DuplicateCustomReportParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateCustomReportParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateCustomReportParams) ProtoMessage() {}

func (x *DuplicateCustomReportParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateCustomReportParams.ProtoReflect.Descriptor instead.
func (*DuplicateCustomReportParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{4}
}

func (x *DuplicateCustomReportParams) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

// DuplicateCustomReportResult
type DuplicateCustomReportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duplicate success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DuplicateCustomReportResult) Reset() {
	*x = DuplicateCustomReportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateCustomReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateCustomReportResult) ProtoMessage() {}

func (x *DuplicateCustomReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateCustomReportResult.ProtoReflect.Descriptor instead.
func (*DuplicateCustomReportResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{5}
}

func (x *DuplicateCustomReportResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// DeleteCustomReportParams
type DeleteCustomReportParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
}

func (x *DeleteCustomReportParams) Reset() {
	*x = DeleteCustomReportParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomReportParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomReportParams) ProtoMessage() {}

func (x *DeleteCustomReportParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomReportParams.ProtoReflect.Descriptor instead.
func (*DeleteCustomReportParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteCustomReportParams) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

// DeleteCustomReportResult
type DeleteCustomReportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteCustomReportResult) Reset() {
	*x = DeleteCustomReportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomReportResult) ProtoMessage() {}

func (x *DeleteCustomReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomReportResult.ProtoReflect.Descriptor instead.
func (*DeleteCustomReportResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteCustomReportResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_moego_models_reporting_v2_custom_report_def_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_custom_report_def_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x03, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x22, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x10, 0x01, 0x18, 0xff, 0x01, 0x32,
	0x0e, 0x5e, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x2e, 0x2a, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x24, 0x48,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a,
	0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x4b, 0x65, 0x79,
	0x73, 0x12, 0x49, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x0d,
	0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0c, 0x73,
	0x61, 0x76, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x13, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x11, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x22, 0x37, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x88, 0x02, 0x0a, 0x19, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x10, 0x01, 0x18, 0xff, 0x01,
	0x32, 0x0e, 0x5e, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x2e, 0x2a, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x24,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x33,
	0x0a, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x11, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14,
	0x5f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x22, 0x35, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x51, 0x0a, 0x1b, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x64, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0x37,
	0x0a, 0x1b, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x4e, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b,
	0x18, 0x01, 0x22, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0x34, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x42, 0x81, 0x01,
	0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_custom_report_def_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_custom_report_def_proto_rawDescData = file_moego_models_reporting_v2_custom_report_def_proto_rawDesc
)

func file_moego_models_reporting_v2_custom_report_def_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_custom_report_def_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_custom_report_def_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_custom_report_def_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_custom_report_def_proto_rawDescData
}

var file_moego_models_reporting_v2_custom_report_def_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_models_reporting_v2_custom_report_def_proto_goTypes = []interface{}{
	(*SaveCustomReportParams)(nil),      // 0: moego.models.reporting.v2.SaveCustomReportParams
	(*SaveCustomReportResult)(nil),      // 1: moego.models.reporting.v2.SaveCustomReportResult
	(*ModifyCustomDiagramParams)(nil),   // 2: moego.models.reporting.v2.ModifyCustomDiagramParams
	(*ModifyCustomDiagramResult)(nil),   // 3: moego.models.reporting.v2.ModifyCustomDiagramResult
	(*DuplicateCustomReportParams)(nil), // 4: moego.models.reporting.v2.DuplicateCustomReportParams
	(*DuplicateCustomReportResult)(nil), // 5: moego.models.reporting.v2.DuplicateCustomReportResult
	(*DeleteCustomReportParams)(nil),    // 6: moego.models.reporting.v2.DeleteCustomReportParams
	(*DeleteCustomReportResult)(nil),    // 7: moego.models.reporting.v2.DeleteCustomReportResult
	(*DimensionField)(nil),              // 8: moego.models.reporting.v2.DimensionField
	(*FilterRequest)(nil),               // 9: moego.models.reporting.v2.FilterRequest
}
var file_moego_models_reporting_v2_custom_report_def_proto_depIdxs = []int32{
	8, // 0: moego.models.reporting.v2.SaveCustomReportParams.dimensions:type_name -> moego.models.reporting.v2.DimensionField
	9, // 1: moego.models.reporting.v2.SaveCustomReportParams.saved_filters:type_name -> moego.models.reporting.v2.FilterRequest
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_custom_report_def_proto_init() }
func file_moego_models_reporting_v2_custom_report_def_proto_init() {
	if File_moego_models_reporting_v2_custom_report_def_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_report_meta_def_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCustomReportParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCustomReportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCustomDiagramParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCustomDiagramResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateCustomReportParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateCustomReportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomReportParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomReportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_custom_report_def_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_custom_report_def_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_custom_report_def_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_custom_report_def_proto_depIdxs,
		MessageInfos:      file_moego_models_reporting_v2_custom_report_def_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_custom_report_def_proto = out.File
	file_moego_models_reporting_v2_custom_report_def_proto_rawDesc = nil
	file_moego_models_reporting_v2_custom_report_def_proto_goTypes = nil
	file_moego_models_reporting_v2_custom_report_def_proto_depIdxs = nil
}
