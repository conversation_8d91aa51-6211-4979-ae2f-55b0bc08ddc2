// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/schedule_message_models.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Schedule message model, for management of scheduled tasks
type ScheduleMessageModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// receipt customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// receipt customer contact id
	ContactId int64 `protobuf:"varint,5,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	// the staff id of created this record
	CreatorId int64 `protobuf:"varint,6,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// the staff id of updated this record
	UpdaterId int64 `protobuf:"varint,7,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	// the staff id of sent this record
	SenderId int64 `protobuf:"varint,8,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// message content
	Content string `protobuf:"bytes,9,opt,name=content,proto3" json:"content,omitempty"`
	// auto message type
	AutoMessageType AutoMessageType `protobuf:"varint,10,opt,name=auto_message_type,json=autoMessageType,proto3,enum=moego.models.message.v1.AutoMessageType" json:"auto_message_type,omitempty"`
	// auto message resource id, such as appointment id
	AutoMessageResourceId int64 `protobuf:"varint,11,opt,name=auto_message_resource_id,json=autoMessageResourceId,proto3" json:"auto_message_resource_id,omitempty"`
	// status
	Status ScheduleMessageStatus `protobuf:"varint,12,opt,name=status,proto3,enum=moego.models.message.v1.ScheduleMessageStatus" json:"status,omitempty"`
	// send out at, preset sending time
	SendOutAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=send_out_at,json=sendOutAt,proto3" json:"send_out_at,omitempty"`
	// actual successful sent time
	SentAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deleted at
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// method
	Method Method `protobuf:"varint,18,opt,name=method,proto3,enum=moego.models.message.v1.Method" json:"method,omitempty"`
}

func (x *ScheduleMessageModel) Reset() {
	*x = ScheduleMessageModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_schedule_message_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleMessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMessageModel) ProtoMessage() {}

func (x *ScheduleMessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_schedule_message_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMessageModel.ProtoReflect.Descriptor instead.
func (*ScheduleMessageModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_schedule_message_models_proto_rawDescGZIP(), []int{0}
}

func (x *ScheduleMessageModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ScheduleMessageModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ScheduleMessageModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ScheduleMessageModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ScheduleMessageModel) GetContactId() int64 {
	if x != nil {
		return x.ContactId
	}
	return 0
}

func (x *ScheduleMessageModel) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *ScheduleMessageModel) GetUpdaterId() int64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *ScheduleMessageModel) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *ScheduleMessageModel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ScheduleMessageModel) GetAutoMessageType() AutoMessageType {
	if x != nil {
		return x.AutoMessageType
	}
	return AutoMessageType_AUTO_MESSAGE_TYPE_UNSPECIFIED
}

func (x *ScheduleMessageModel) GetAutoMessageResourceId() int64 {
	if x != nil {
		return x.AutoMessageResourceId
	}
	return 0
}

func (x *ScheduleMessageModel) GetStatus() ScheduleMessageStatus {
	if x != nil {
		return x.Status
	}
	return ScheduleMessageStatus_SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED
}

func (x *ScheduleMessageModel) GetSendOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SendOutAt
	}
	return nil
}

func (x *ScheduleMessageModel) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *ScheduleMessageModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ScheduleMessageModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ScheduleMessageModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *ScheduleMessageModel) GetMethod() Method {
	if x != nil {
		return x.Method
	}
	return Method_METHOD_UNSPECIFIED
}

// Schedule message model public view
type ScheduleMessagePublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// receipt customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// the staff id of created this record
	CreatorId int64 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// the staff id of updated this record
	UpdaterId int64 `protobuf:"varint,4,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	// the staff id of sent this record
	SenderId int64 `protobuf:"varint,5,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// message content
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	// receipt contact id
	ContactId int64 `protobuf:"varint,7,opt,name=contact_id,json=contactId,proto3" json:"contact_id,omitempty"`
	// auto message type
	AutoMessageType AutoMessageType `protobuf:"varint,8,opt,name=auto_message_type,json=autoMessageType,proto3,enum=moego.models.message.v1.AutoMessageType" json:"auto_message_type,omitempty"`
	// auto message resource id, such as appointment id
	AutoMessageResourceId int64 `protobuf:"varint,9,opt,name=auto_message_resource_id,json=autoMessageResourceId,proto3" json:"auto_message_resource_id,omitempty"`
	// status
	Status ScheduleMessageStatus `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.message.v1.ScheduleMessageStatus" json:"status,omitempty"`
	// send out at, preset sending time
	SendOutAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=send_out_at,json=sendOutAt,proto3" json:"send_out_at,omitempty"`
	// actual successful sent time
	SentAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	// method
	Method Method `protobuf:"varint,13,opt,name=method,proto3,enum=moego.models.message.v1.Method" json:"method,omitempty"`
}

func (x *ScheduleMessagePublicView) Reset() {
	*x = ScheduleMessagePublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_schedule_message_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleMessagePublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMessagePublicView) ProtoMessage() {}

func (x *ScheduleMessagePublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_schedule_message_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMessagePublicView.ProtoReflect.Descriptor instead.
func (*ScheduleMessagePublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_schedule_message_models_proto_rawDescGZIP(), []int{1}
}

func (x *ScheduleMessagePublicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetUpdaterId() int64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ScheduleMessagePublicView) GetContactId() int64 {
	if x != nil {
		return x.ContactId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetAutoMessageType() AutoMessageType {
	if x != nil {
		return x.AutoMessageType
	}
	return AutoMessageType_AUTO_MESSAGE_TYPE_UNSPECIFIED
}

func (x *ScheduleMessagePublicView) GetAutoMessageResourceId() int64 {
	if x != nil {
		return x.AutoMessageResourceId
	}
	return 0
}

func (x *ScheduleMessagePublicView) GetStatus() ScheduleMessageStatus {
	if x != nil {
		return x.Status
	}
	return ScheduleMessageStatus_SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED
}

func (x *ScheduleMessagePublicView) GetSendOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SendOutAt
	}
	return nil
}

func (x *ScheduleMessagePublicView) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *ScheduleMessagePublicView) GetMethod() Method {
	if x != nil {
		return x.Method
	}
	return Method_METHOD_UNSPECIFIED
}

var File_moego_models_message_v1_schedule_message_models_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_schedule_message_models_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd,
	0x06, 0x0a, 0x14, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x11,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x75, 0x74, 0x41, 0x74, 0x12,
	0x33, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x73, 0x65,
	0x6e, 0x74, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xe1,
	0x04, 0x0a, 0x19, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x54, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x4f,
	0x75, 0x74, 0x41, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_schedule_message_models_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_schedule_message_models_proto_rawDescData = file_moego_models_message_v1_schedule_message_models_proto_rawDesc
)

func file_moego_models_message_v1_schedule_message_models_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_schedule_message_models_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_schedule_message_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_schedule_message_models_proto_rawDescData)
	})
	return file_moego_models_message_v1_schedule_message_models_proto_rawDescData
}

var file_moego_models_message_v1_schedule_message_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_message_v1_schedule_message_models_proto_goTypes = []interface{}{
	(*ScheduleMessageModel)(nil),      // 0: moego.models.message.v1.ScheduleMessageModel
	(*ScheduleMessagePublicView)(nil), // 1: moego.models.message.v1.ScheduleMessagePublicView
	(AutoMessageType)(0),              // 2: moego.models.message.v1.AutoMessageType
	(ScheduleMessageStatus)(0),        // 3: moego.models.message.v1.ScheduleMessageStatus
	(*timestamppb.Timestamp)(nil),     // 4: google.protobuf.Timestamp
	(Method)(0),                       // 5: moego.models.message.v1.Method
}
var file_moego_models_message_v1_schedule_message_models_proto_depIdxs = []int32{
	2,  // 0: moego.models.message.v1.ScheduleMessageModel.auto_message_type:type_name -> moego.models.message.v1.AutoMessageType
	3,  // 1: moego.models.message.v1.ScheduleMessageModel.status:type_name -> moego.models.message.v1.ScheduleMessageStatus
	4,  // 2: moego.models.message.v1.ScheduleMessageModel.send_out_at:type_name -> google.protobuf.Timestamp
	4,  // 3: moego.models.message.v1.ScheduleMessageModel.sent_at:type_name -> google.protobuf.Timestamp
	4,  // 4: moego.models.message.v1.ScheduleMessageModel.created_at:type_name -> google.protobuf.Timestamp
	4,  // 5: moego.models.message.v1.ScheduleMessageModel.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 6: moego.models.message.v1.ScheduleMessageModel.deleted_at:type_name -> google.protobuf.Timestamp
	5,  // 7: moego.models.message.v1.ScheduleMessageModel.method:type_name -> moego.models.message.v1.Method
	2,  // 8: moego.models.message.v1.ScheduleMessagePublicView.auto_message_type:type_name -> moego.models.message.v1.AutoMessageType
	3,  // 9: moego.models.message.v1.ScheduleMessagePublicView.status:type_name -> moego.models.message.v1.ScheduleMessageStatus
	4,  // 10: moego.models.message.v1.ScheduleMessagePublicView.send_out_at:type_name -> google.protobuf.Timestamp
	4,  // 11: moego.models.message.v1.ScheduleMessagePublicView.sent_at:type_name -> google.protobuf.Timestamp
	5,  // 12: moego.models.message.v1.ScheduleMessagePublicView.method:type_name -> moego.models.message.v1.Method
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_schedule_message_models_proto_init() }
func file_moego_models_message_v1_schedule_message_models_proto_init() {
	if File_moego_models_message_v1_schedule_message_models_proto != nil {
		return
	}
	file_moego_models_message_v1_message_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v1_schedule_message_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleMessageModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_schedule_message_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleMessagePublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_schedule_message_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_schedule_message_models_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_schedule_message_models_proto_depIdxs,
		MessageInfos:      file_moego_models_message_v1_schedule_message_models_proto_msgTypes,
	}.Build()
	File_moego_models_message_v1_schedule_message_models_proto = out.File
	file_moego_models_message_v1_schedule_message_models_proto_rawDesc = nil
	file_moego_models_message_v1_schedule_message_models_proto_goTypes = nil
	file_moego_models_message_v1_schedule_message_models_proto_depIdxs = nil
}
