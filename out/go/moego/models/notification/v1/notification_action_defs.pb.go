// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/notification/v1/notification_action_defs.proto

package notificationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// notification action definition
type NotificationActionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action information
	//
	// Types that are assignable to Action:
	//
	//	*NotificationActionDef_OpenAppointmentDetail
	//	*NotificationActionDef_GoToHomePage
	Action isNotificationActionDef_Action `protobuf_oneof:"action"`
}

func (x *NotificationActionDef) Reset() {
	*x = NotificationActionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationActionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationActionDef) ProtoMessage() {}

func (x *NotificationActionDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationActionDef.ProtoReflect.Descriptor instead.
func (*NotificationActionDef) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_action_defs_proto_rawDescGZIP(), []int{0}
}

func (m *NotificationActionDef) GetAction() isNotificationActionDef_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *NotificationActionDef) GetOpenAppointmentDetail() *OpenAppointmentDetail {
	if x, ok := x.GetAction().(*NotificationActionDef_OpenAppointmentDetail); ok {
		return x.OpenAppointmentDetail
	}
	return nil
}

func (x *NotificationActionDef) GetGoToHomePage() *GoToHomePage {
	if x, ok := x.GetAction().(*NotificationActionDef_GoToHomePage); ok {
		return x.GoToHomePage
	}
	return nil
}

type isNotificationActionDef_Action interface {
	isNotificationActionDef_Action()
}

type NotificationActionDef_OpenAppointmentDetail struct {
	// open appointment detail
	OpenAppointmentDetail *OpenAppointmentDetail `protobuf:"bytes,1,opt,name=open_appointment_detail,json=openAppointmentDetail,proto3,oneof"`
}

type NotificationActionDef_GoToHomePage struct {
	// go to home page
	GoToHomePage *GoToHomePage `protobuf:"bytes,3,opt,name=go_to_home_page,json=goToHomePage,proto3,oneof"`
}

func (*NotificationActionDef_OpenAppointmentDetail) isNotificationActionDef_Action() {}

func (*NotificationActionDef_GoToHomePage) isNotificationActionDef_Action() {}

// open appointment detail
type OpenAppointmentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *OpenAppointmentDetail) Reset() {
	*x = OpenAppointmentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenAppointmentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenAppointmentDetail) ProtoMessage() {}

func (x *OpenAppointmentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenAppointmentDetail.ProtoReflect.Descriptor instead.
func (*OpenAppointmentDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_action_defs_proto_rawDescGZIP(), []int{1}
}

func (x *OpenAppointmentDetail) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// go to home page
type GoToHomePage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GoToHomePage) Reset() {
	*x = GoToHomePage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoToHomePage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoToHomePage) ProtoMessage() {}

func (x *GoToHomePage) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoToHomePage.ProtoReflect.Descriptor instead.
func (*GoToHomePage) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_action_defs_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_notification_v1_notification_action_defs_proto protoreflect.FileDescriptor

var file_moego_models_notification_v1_notification_action_defs_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x22, 0xe5, 0x01, 0x0a, 0x15,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x6d, 0x0a, 0x17, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x15, 0x6f,
	0x70, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x53, 0x0a, 0x0f, 0x67, 0x6f, 0x5f, 0x74, 0x6f, 0x5f, 0x68, 0x6f,
	0x6d, 0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x54,
	0x6f, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x67, 0x6f, 0x54,
	0x6f, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x67, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x3e, 0x0a, 0x15, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x47, 0x6f, 0x54, 0x6f, 0x48, 0x6f, 0x6d, 0x65, 0x50,
	0x61, 0x67, 0x65, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_notification_v1_notification_action_defs_proto_rawDescOnce sync.Once
	file_moego_models_notification_v1_notification_action_defs_proto_rawDescData = file_moego_models_notification_v1_notification_action_defs_proto_rawDesc
)

func file_moego_models_notification_v1_notification_action_defs_proto_rawDescGZIP() []byte {
	file_moego_models_notification_v1_notification_action_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_notification_v1_notification_action_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_notification_v1_notification_action_defs_proto_rawDescData)
	})
	return file_moego_models_notification_v1_notification_action_defs_proto_rawDescData
}

var file_moego_models_notification_v1_notification_action_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_notification_v1_notification_action_defs_proto_goTypes = []interface{}{
	(*NotificationActionDef)(nil), // 0: moego.models.notification.v1.NotificationActionDef
	(*OpenAppointmentDetail)(nil), // 1: moego.models.notification.v1.OpenAppointmentDetail
	(*GoToHomePage)(nil),          // 2: moego.models.notification.v1.GoToHomePage
}
var file_moego_models_notification_v1_notification_action_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.notification.v1.NotificationActionDef.open_appointment_detail:type_name -> moego.models.notification.v1.OpenAppointmentDetail
	2, // 1: moego.models.notification.v1.NotificationActionDef.go_to_home_page:type_name -> moego.models.notification.v1.GoToHomePage
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_notification_v1_notification_action_defs_proto_init() }
func file_moego_models_notification_v1_notification_action_defs_proto_init() {
	if File_moego_models_notification_v1_notification_action_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationActionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenAppointmentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoToHomePage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_notification_v1_notification_action_defs_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*NotificationActionDef_OpenAppointmentDetail)(nil),
		(*NotificationActionDef_GoToHomePage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_notification_v1_notification_action_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_notification_v1_notification_action_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_notification_v1_notification_action_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_notification_v1_notification_action_defs_proto_msgTypes,
	}.Build()
	File_moego_models_notification_v1_notification_action_defs_proto = out.File
	file_moego_models_notification_v1_notification_action_defs_proto_rawDesc = nil
	file_moego_models_notification_v1_notification_action_defs_proto_goTypes = nil
	file_moego_models_notification_v1_notification_action_defs_proto_depIdxs = nil
}
