// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/grooming/v1/grooming_report_api.proto

package groomingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list grooming report card params
type ListGroomingReportCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// report status
	Status *v1.GroomingReportStatus `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.grooming.v1.GroomingReportStatus,oneof" json:"status,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,6,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListGroomingReportCardParams) Reset() {
	*x = ListGroomingReportCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGroomingReportCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroomingReportCardParams) ProtoMessage() {}

func (x *ListGroomingReportCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroomingReportCardParams.ProtoReflect.Descriptor instead.
func (*ListGroomingReportCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListGroomingReportCardParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListGroomingReportCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListGroomingReportCardParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListGroomingReportCardParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListGroomingReportCardParams) GetStatus() v1.GroomingReportStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.GroomingReportStatus(0)
}

func (x *ListGroomingReportCardParams) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *ListGroomingReportCardParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list grooming report card result
type ListGroomingReportCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming report card list
	GroomingReportCards []*GroomingReportCardDef `protobuf:"bytes,1,rep,name=grooming_report_cards,json=groomingReportCards,proto3" json:"grooming_report_cards,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListGroomingReportCardResult) Reset() {
	*x = ListGroomingReportCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGroomingReportCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroomingReportCardResult) ProtoMessage() {}

func (x *ListGroomingReportCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroomingReportCardResult.ProtoReflect.Descriptor instead.
func (*ListGroomingReportCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListGroomingReportCardResult) GetGroomingReportCards() []*GroomingReportCardDef {
	if x != nil {
		return x.GroomingReportCards
	}
	return nil
}

func (x *ListGroomingReportCardResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// grooming report card def
type GroomingReportCardDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report card id
	ReportCardId int64 `protobuf:"varint,1,opt,name=report_card_id,json=reportCardId,proto3" json:"report_card_id,omitempty"`
	// pet overview
	PetOverview *ReportCardPetOverview `protobuf:"bytes,2,opt,name=pet_overview,json=petOverview,proto3" json:"pet_overview,omitempty"`
	// last update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// send method
	SendMethod v1.GroomingReportSendMethod `protobuf:"varint,6,opt,name=send_method,json=sendMethod,proto3,enum=moego.models.grooming.v1.GroomingReportSendMethod" json:"send_method,omitempty"`
	// media(image/video) count
	MediaCount int32 `protobuf:"varint,7,opt,name=media_count,json=mediaCount,proto3" json:"media_count,omitempty"`
	// service date, date of report
	ServiceDate *date.Date `protobuf:"bytes,8,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,9,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GroomingReportCardDef) Reset() {
	*x = GroomingReportCardDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingReportCardDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingReportCardDef) ProtoMessage() {}

func (x *GroomingReportCardDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingReportCardDef.ProtoReflect.Descriptor instead.
func (*GroomingReportCardDef) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{2}
}

func (x *GroomingReportCardDef) GetReportCardId() int64 {
	if x != nil {
		return x.ReportCardId
	}
	return 0
}

func (x *GroomingReportCardDef) GetPetOverview() *ReportCardPetOverview {
	if x != nil {
		return x.PetOverview
	}
	return nil
}

func (x *GroomingReportCardDef) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *GroomingReportCardDef) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *GroomingReportCardDef) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GroomingReportCardDef) GetSendMethod() v1.GroomingReportSendMethod {
	if x != nil {
		return x.SendMethod
	}
	return v1.GroomingReportSendMethod(0)
}

func (x *GroomingReportCardDef) GetMediaCount() int32 {
	if x != nil {
		return x.MediaCount
	}
	return 0
}

func (x *GroomingReportCardDef) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

func (x *GroomingReportCardDef) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// report card pet overview
type ReportCardPetOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType v11.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *ReportCardPetOverview) Reset() {
	*x = ReportCardPetOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportCardPetOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportCardPetOverview) ProtoMessage() {}

func (x *ReportCardPetOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportCardPetOverview.ProtoReflect.Descriptor instead.
func (*ReportCardPetOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{3}
}

func (x *ReportCardPetOverview) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ReportCardPetOverview) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ReportCardPetOverview) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ReportCardPetOverview) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

// batch delete grooming report params
type BatchDeleteGroomingReportCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// report card ids
	ReportCardIds []int64 `protobuf:"varint,3,rep,packed,name=report_card_ids,json=reportCardIds,proto3" json:"report_card_ids,omitempty"`
}

func (x *BatchDeleteGroomingReportCardParams) Reset() {
	*x = BatchDeleteGroomingReportCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteGroomingReportCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteGroomingReportCardParams) ProtoMessage() {}

func (x *BatchDeleteGroomingReportCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteGroomingReportCardParams.ProtoReflect.Descriptor instead.
func (*BatchDeleteGroomingReportCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{4}
}

func (x *BatchDeleteGroomingReportCardParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchDeleteGroomingReportCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchDeleteGroomingReportCardParams) GetReportCardIds() []int64 {
	if x != nil {
		return x.ReportCardIds
	}
	return nil
}

// batch delete grooming report result
type BatchDeleteGroomingReportCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteGroomingReportCardResult) Reset() {
	*x = BatchDeleteGroomingReportCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteGroomingReportCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteGroomingReportCardResult) ProtoMessage() {}

func (x *BatchDeleteGroomingReportCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteGroomingReportCardResult.ProtoReflect.Descriptor instead.
func (*BatchDeleteGroomingReportCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{5}
}

// batch send grooming report params
type BatchSendGroomingReportCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// report card ids
	ReportCardIds []int64 `protobuf:"varint,3,rep,packed,name=report_card_ids,json=reportCardIds,proto3" json:"report_card_ids,omitempty"`
	// send method
	SendMethod v1.GroomingReportSendMethod `protobuf:"varint,4,opt,name=send_method,json=sendMethod,proto3,enum=moego.models.grooming.v1.GroomingReportSendMethod" json:"send_method,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *BatchSendGroomingReportCardParams) Reset() {
	*x = BatchSendGroomingReportCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSendGroomingReportCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendGroomingReportCardParams) ProtoMessage() {}

func (x *BatchSendGroomingReportCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendGroomingReportCardParams.ProtoReflect.Descriptor instead.
func (*BatchSendGroomingReportCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{6}
}

func (x *BatchSendGroomingReportCardParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchSendGroomingReportCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchSendGroomingReportCardParams) GetReportCardIds() []int64 {
	if x != nil {
		return x.ReportCardIds
	}
	return nil
}

func (x *BatchSendGroomingReportCardParams) GetSendMethod() v1.GroomingReportSendMethod {
	if x != nil {
		return x.SendMethod
	}
	return v1.GroomingReportSendMethod(0)
}

func (x *BatchSendGroomingReportCardParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// batch send grooming report card result
type BatchSendGroomingReportCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchSendGroomingReportCardResult) Reset() {
	*x = BatchSendGroomingReportCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSendGroomingReportCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendGroomingReportCardResult) ProtoMessage() {}

func (x *BatchSendGroomingReportCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendGroomingReportCardResult.ProtoReflect.Descriptor instead.
func (*BatchSendGroomingReportCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{7}
}

var File_moego_api_grooming_v1_grooming_report_api_proto protoreflect.FileDescriptor

var file_moego_api_grooming_v1_grooming_report_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x03,
	0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a, 0x15, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x66, 0x52, 0x13, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xeb,
	0x03, 0x0a, 0x15, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x66, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x4f,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12,
	0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0xa8, 0x01, 0x0a,
	0x15, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x65, 0x74, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x22, 0x9f, 0x01, 0x0a, 0x23, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x22, 0x25, 0x0a, 0x23, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xa2, 0x02, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73,
	0x12, 0x5f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65,
	0x6e, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xca, 0x03, 0x0a, 0x15, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x1d, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x6e,
	0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x6e, 0x64, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_grooming_v1_grooming_report_api_proto_rawDescOnce sync.Once
	file_moego_api_grooming_v1_grooming_report_api_proto_rawDescData = file_moego_api_grooming_v1_grooming_report_api_proto_rawDesc
)

func file_moego_api_grooming_v1_grooming_report_api_proto_rawDescGZIP() []byte {
	file_moego_api_grooming_v1_grooming_report_api_proto_rawDescOnce.Do(func() {
		file_moego_api_grooming_v1_grooming_report_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_grooming_v1_grooming_report_api_proto_rawDescData)
	})
	return file_moego_api_grooming_v1_grooming_report_api_proto_rawDescData
}

var file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_api_grooming_v1_grooming_report_api_proto_goTypes = []interface{}{
	(*ListGroomingReportCardParams)(nil),        // 0: moego.api.grooming.v1.ListGroomingReportCardParams
	(*ListGroomingReportCardResult)(nil),        // 1: moego.api.grooming.v1.ListGroomingReportCardResult
	(*GroomingReportCardDef)(nil),               // 2: moego.api.grooming.v1.GroomingReportCardDef
	(*ReportCardPetOverview)(nil),               // 3: moego.api.grooming.v1.ReportCardPetOverview
	(*BatchDeleteGroomingReportCardParams)(nil), // 4: moego.api.grooming.v1.BatchDeleteGroomingReportCardParams
	(*BatchDeleteGroomingReportCardResult)(nil), // 5: moego.api.grooming.v1.BatchDeleteGroomingReportCardResult
	(*BatchSendGroomingReportCardParams)(nil),   // 6: moego.api.grooming.v1.BatchSendGroomingReportCardParams
	(*BatchSendGroomingReportCardResult)(nil),   // 7: moego.api.grooming.v1.BatchSendGroomingReportCardResult
	(*date.Date)(nil),                           // 8: google.type.Date
	(v1.GroomingReportStatus)(0),                // 9: moego.models.grooming.v1.GroomingReportStatus
	(*v2.PaginationRequest)(nil),                // 10: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),               // 11: moego.utils.v2.PaginationResponse
	(*timestamppb.Timestamp)(nil),               // 12: google.protobuf.Timestamp
	(v1.GroomingReportSendMethod)(0),            // 13: moego.models.grooming.v1.GroomingReportSendMethod
	(v11.PetType)(0),                            // 14: moego.models.customer.v1.PetType
}
var file_moego_api_grooming_v1_grooming_report_api_proto_depIdxs = []int32{
	8,  // 0: moego.api.grooming.v1.ListGroomingReportCardParams.start_date:type_name -> google.type.Date
	8,  // 1: moego.api.grooming.v1.ListGroomingReportCardParams.end_date:type_name -> google.type.Date
	9,  // 2: moego.api.grooming.v1.ListGroomingReportCardParams.status:type_name -> moego.models.grooming.v1.GroomingReportStatus
	10, // 3: moego.api.grooming.v1.ListGroomingReportCardParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	2,  // 4: moego.api.grooming.v1.ListGroomingReportCardResult.grooming_report_cards:type_name -> moego.api.grooming.v1.GroomingReportCardDef
	11, // 5: moego.api.grooming.v1.ListGroomingReportCardResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	3,  // 6: moego.api.grooming.v1.GroomingReportCardDef.pet_overview:type_name -> moego.api.grooming.v1.ReportCardPetOverview
	12, // 7: moego.api.grooming.v1.GroomingReportCardDef.update_time:type_name -> google.protobuf.Timestamp
	12, // 8: moego.api.grooming.v1.GroomingReportCardDef.send_time:type_name -> google.protobuf.Timestamp
	13, // 9: moego.api.grooming.v1.GroomingReportCardDef.send_method:type_name -> moego.models.grooming.v1.GroomingReportSendMethod
	8,  // 10: moego.api.grooming.v1.GroomingReportCardDef.service_date:type_name -> google.type.Date
	14, // 11: moego.api.grooming.v1.ReportCardPetOverview.pet_type:type_name -> moego.models.customer.v1.PetType
	13, // 12: moego.api.grooming.v1.BatchSendGroomingReportCardParams.send_method:type_name -> moego.models.grooming.v1.GroomingReportSendMethod
	0,  // 13: moego.api.grooming.v1.GroomingReportService.ListGroomingReportCard:input_type -> moego.api.grooming.v1.ListGroomingReportCardParams
	4,  // 14: moego.api.grooming.v1.GroomingReportService.BatchDeleteGroomingReportCard:input_type -> moego.api.grooming.v1.BatchDeleteGroomingReportCardParams
	6,  // 15: moego.api.grooming.v1.GroomingReportService.BatchSendGroomingReportCard:input_type -> moego.api.grooming.v1.BatchSendGroomingReportCardParams
	1,  // 16: moego.api.grooming.v1.GroomingReportService.ListGroomingReportCard:output_type -> moego.api.grooming.v1.ListGroomingReportCardResult
	5,  // 17: moego.api.grooming.v1.GroomingReportService.BatchDeleteGroomingReportCard:output_type -> moego.api.grooming.v1.BatchDeleteGroomingReportCardResult
	7,  // 18: moego.api.grooming.v1.GroomingReportService.BatchSendGroomingReportCard:output_type -> moego.api.grooming.v1.BatchSendGroomingReportCardResult
	16, // [16:19] is the sub-list for method output_type
	13, // [13:16] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_api_grooming_v1_grooming_report_api_proto_init() }
func file_moego_api_grooming_v1_grooming_report_api_proto_init() {
	if File_moego_api_grooming_v1_grooming_report_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGroomingReportCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGroomingReportCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingReportCardDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportCardPetOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteGroomingReportCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteGroomingReportCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSendGroomingReportCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSendGroomingReportCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_grooming_v1_grooming_report_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_grooming_v1_grooming_report_api_proto_goTypes,
		DependencyIndexes: file_moego_api_grooming_v1_grooming_report_api_proto_depIdxs,
		MessageInfos:      file_moego_api_grooming_v1_grooming_report_api_proto_msgTypes,
	}.Build()
	File_moego_api_grooming_v1_grooming_report_api_proto = out.File
	file_moego_api_grooming_v1_grooming_report_api_proto_rawDesc = nil
	file_moego_api_grooming_v1_grooming_report_api_proto_goTypes = nil
	file_moego_api_grooming_v1_grooming_report_api_proto_depIdxs = nil
}
