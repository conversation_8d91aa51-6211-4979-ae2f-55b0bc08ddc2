// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/online_booking/v1/ob_address_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OBAddressServiceClient is the client API for OBAddressService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OBAddressServiceClient interface {
	// Deprecated: Do not use.
	// Upsert an address.
	UpsertAddress(ctx context.Context, in *UpsertAddressParams, opts ...grpc.CallOption) (*UpsertAddressResult, error)
}

type oBAddressServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOBAddressServiceClient(cc grpc.ClientConnInterface) OBAddressServiceClient {
	return &oBAddressServiceClient{cc}
}

// Deprecated: Do not use.
func (c *oBAddressServiceClient) UpsertAddress(ctx context.Context, in *UpsertAddressParams, opts ...grpc.CallOption) (*UpsertAddressResult, error) {
	out := new(UpsertAddressResult)
	err := c.cc.Invoke(ctx, "/moego.api.online_booking.v1.OBAddressService/UpsertAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OBAddressServiceServer is the server API for OBAddressService service.
// All implementations must embed UnimplementedOBAddressServiceServer
// for forward compatibility
type OBAddressServiceServer interface {
	// Deprecated: Do not use.
	// Upsert an address.
	UpsertAddress(context.Context, *UpsertAddressParams) (*UpsertAddressResult, error)
	mustEmbedUnimplementedOBAddressServiceServer()
}

// UnimplementedOBAddressServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOBAddressServiceServer struct {
}

func (UnimplementedOBAddressServiceServer) UpsertAddress(context.Context, *UpsertAddressParams) (*UpsertAddressResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertAddress not implemented")
}
func (UnimplementedOBAddressServiceServer) mustEmbedUnimplementedOBAddressServiceServer() {}

// UnsafeOBAddressServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OBAddressServiceServer will
// result in compilation errors.
type UnsafeOBAddressServiceServer interface {
	mustEmbedUnimplementedOBAddressServiceServer()
}

func RegisterOBAddressServiceServer(s grpc.ServiceRegistrar, srv OBAddressServiceServer) {
	s.RegisterService(&OBAddressService_ServiceDesc, srv)
}

func _OBAddressService_UpsertAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertAddressParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAddressServiceServer).UpsertAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.online_booking.v1.OBAddressService/UpsertAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAddressServiceServer).UpsertAddress(ctx, req.(*UpsertAddressParams))
	}
	return interceptor(ctx, in, info, handler)
}

// OBAddressService_ServiceDesc is the grpc.ServiceDesc for OBAddressService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OBAddressService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.online_booking.v1.OBAddressService",
	HandlerType: (*OBAddressServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertAddress",
			Handler:    _OBAddressService_UpsertAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/online_booking/v1/ob_address_api.proto",
}
