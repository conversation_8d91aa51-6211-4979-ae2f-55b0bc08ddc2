// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/appointment_tracking_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update appointment tracking status request
type UpdateAppointmentTrackingStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff location status
	StaffLocationStatus v1.StaffLocationStatus `protobuf:"varint,2,opt,name=staff_location_status,json=staffLocationStatus,proto3,enum=moego.models.appointment.v1.StaffLocationStatus" json:"staff_location_status,omitempty"`
	// staff coordinate, only available when staff location status is change to IN_TRANSIT
	StaffCoordinate *latlng.LatLng `protobuf:"bytes,3,opt,name=staff_coordinate,json=staffCoordinate,proto3,oneof" json:"staff_coordinate,omitempty"`
	// staff device id, must have when staff location status is change to IN_TRANSIT
	// if not provided, will use the header device id
	LocationSharingDeviceId *string `protobuf:"bytes,4,opt,name=location_sharing_device_id,json=locationSharingDeviceId,proto3,oneof" json:"location_sharing_device_id,omitempty"`
}

func (x *UpdateAppointmentTrackingStatusParams) Reset() {
	*x = UpdateAppointmentTrackingStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentTrackingStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentTrackingStatusParams) ProtoMessage() {}

func (x *UpdateAppointmentTrackingStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentTrackingStatusParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentTrackingStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateAppointmentTrackingStatusParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentTrackingStatusParams) GetStaffLocationStatus() v1.StaffLocationStatus {
	if x != nil {
		return x.StaffLocationStatus
	}
	return v1.StaffLocationStatus(0)
}

func (x *UpdateAppointmentTrackingStatusParams) GetStaffCoordinate() *latlng.LatLng {
	if x != nil {
		return x.StaffCoordinate
	}
	return nil
}

func (x *UpdateAppointmentTrackingStatusParams) GetLocationSharingDeviceId() string {
	if x != nil && x.LocationSharingDeviceId != nil {
		return *x.LocationSharingDeviceId
	}
	return ""
}

// update appointment tracking status response
type UpdateAppointmentTrackingStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTrackingView `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpdateAppointmentTrackingStatusResult) Reset() {
	*x = UpdateAppointmentTrackingStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentTrackingStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentTrackingStatusResult) ProtoMessage() {}

func (x *UpdateAppointmentTrackingStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentTrackingStatusResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentTrackingStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateAppointmentTrackingStatusResult) GetAppointmentTracking() *v1.AppointmentTrackingView {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// get appointment tracking request
type GetAppointmentTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAppointmentTrackingParams) Reset() {
	*x = GetAppointmentTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentTrackingParams) ProtoMessage() {}

func (x *GetAppointmentTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentTrackingParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetAppointmentTrackingParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// get appointment tracking response
type GetAppointmentTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTrackingView `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *GetAppointmentTrackingResult) Reset() {
	*x = GetAppointmentTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentTrackingResult) ProtoMessage() {}

func (x *GetAppointmentTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentTrackingResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAppointmentTrackingResult) GetAppointmentTracking() *v1.AppointmentTrackingView {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// list staff appointment tracking request
// use staff id in session data
type ListStaffAppointmentTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListStaffAppointmentTrackingParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// extra info request
	ExtraInfoRequest *ListStaffAppointmentTrackingParams_ExtraInfoRequest `protobuf:"bytes,3,opt,name=extra_info_request,json=extraInfoRequest,proto3" json:"extra_info_request,omitempty"`
}

func (x *ListStaffAppointmentTrackingParams) Reset() {
	*x = ListStaffAppointmentTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingParams) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingParams.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListStaffAppointmentTrackingParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListStaffAppointmentTrackingParams) GetFilter() *ListStaffAppointmentTrackingParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListStaffAppointmentTrackingParams) GetExtraInfoRequest() *ListStaffAppointmentTrackingParams_ExtraInfoRequest {
	if x != nil {
		return x.ExtraInfoRequest
	}
	return nil
}

// list staff appointment tracking response
type ListStaffAppointmentTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking list
	AppointmentTracking []*v1.AppointmentTrackingView `protobuf:"bytes,1,rep,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// appointment info, key is appointment id
	AppointmentInfo map[int64]*ListStaffAppointmentTrackingResult_AppointmentView `protobuf:"bytes,3,rep,name=appointment_info,json=appointmentInfo,proto3" json:"appointment_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// customer info, key is customer id
	CustomerInfo map[int64]*ListStaffAppointmentTrackingResult_CustomerView `protobuf:"bytes,4,rep,name=customer_info,json=customerInfo,proto3" json:"customer_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListStaffAppointmentTrackingResult) Reset() {
	*x = ListStaffAppointmentTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingResult) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingResult.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListStaffAppointmentTrackingResult) GetAppointmentTracking() []*v1.AppointmentTrackingView {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

func (x *ListStaffAppointmentTrackingResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListStaffAppointmentTrackingResult) GetAppointmentInfo() map[int64]*ListStaffAppointmentTrackingResult_AppointmentView {
	if x != nil {
		return x.AppointmentInfo
	}
	return nil
}

func (x *ListStaffAppointmentTrackingResult) GetCustomerInfo() map[int64]*ListStaffAppointmentTrackingResult_CustomerView {
	if x != nil {
		return x.CustomerInfo
	}
	return nil
}

// filter
type ListStaffAppointmentTrackingParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	StaffLocationStatuses []v1.StaffLocationStatus `protobuf:"varint,1,rep,packed,name=staff_location_statuses,json=staffLocationStatuses,proto3,enum=moego.models.appointment.v1.StaffLocationStatus" json:"staff_location_statuses,omitempty"`
	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *ListStaffAppointmentTrackingParams_Filter) Reset() {
	*x = ListStaffAppointmentTrackingParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingParams_Filter) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingParams_Filter.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListStaffAppointmentTrackingParams_Filter) GetStaffLocationStatuses() []v1.StaffLocationStatus {
	if x != nil {
		return x.StaffLocationStatuses
	}
	return nil
}

func (x *ListStaffAppointmentTrackingParams_Filter) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// extra info request
type ListStaffAppointmentTrackingParams_ExtraInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request appointment info
	RequestAppointmentInfo bool `protobuf:"varint,1,opt,name=request_appointment_info,json=requestAppointmentInfo,proto3" json:"request_appointment_info,omitempty"`
	// request customer info
	RequestCustomerInfo bool `protobuf:"varint,2,opt,name=request_customer_info,json=requestCustomerInfo,proto3" json:"request_customer_info,omitempty"`
}

func (x *ListStaffAppointmentTrackingParams_ExtraInfoRequest) Reset() {
	*x = ListStaffAppointmentTrackingParams_ExtraInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingParams_ExtraInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingParams_ExtraInfoRequest) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingParams_ExtraInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingParams_ExtraInfoRequest.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingParams_ExtraInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ListStaffAppointmentTrackingParams_ExtraInfoRequest) GetRequestAppointmentInfo() bool {
	if x != nil {
		return x.RequestAppointmentInfo
	}
	return false
}

func (x *ListStaffAppointmentTrackingParams_ExtraInfoRequest) GetRequestCustomerInfo() bool {
	if x != nil {
		return x.RequestCustomerInfo
	}
	return false
}

// Customer view
type ListStaffAppointmentTrackingResult_CustomerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer profile
	CustomerProfile *v11.BusinessCustomerInfoModel `protobuf:"bytes,1,opt,name=customer_profile,json=customerProfile,proto3" json:"customer_profile,omitempty"`
}

func (x *ListStaffAppointmentTrackingResult_CustomerView) Reset() {
	*x = ListStaffAppointmentTrackingResult_CustomerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingResult_CustomerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingResult_CustomerView) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingResult_CustomerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingResult_CustomerView.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingResult_CustomerView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ListStaffAppointmentTrackingResult_CustomerView) GetCustomerProfile() *v11.BusinessCustomerInfoModel {
	if x != nil {
		return x.CustomerProfile
	}
	return nil
}

// Appointment view
type ListStaffAppointmentTrackingResult_AppointmentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment
	Appointment *v1.AppointmentModel `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
}

func (x *ListStaffAppointmentTrackingResult_AppointmentView) Reset() {
	*x = ListStaffAppointmentTrackingResult_AppointmentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffAppointmentTrackingResult_AppointmentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffAppointmentTrackingResult_AppointmentView) ProtoMessage() {}

func (x *ListStaffAppointmentTrackingResult_AppointmentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffAppointmentTrackingResult_AppointmentView.ProtoReflect.Descriptor instead.
func (*ListStaffAppointmentTrackingResult_AppointmentView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{5, 1}
}

func (x *ListStaffAppointmentTrackingResult_AppointmentView) GetAppointment() *v1.AppointmentModel {
	if x != nil {
		return x.Appointment
	}
	return nil
}

var File_moego_api_appointment_v1_appointment_tracking_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xef, 0x02, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x64, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x13, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x1a,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x17, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x61, 0x72,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x13,
	0x0a, 0x11, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x14,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x45, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a,
	0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x88, 0x05, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x7b, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0xb7, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x79, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x64, 0x22, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x1a, 0x80,
	0x01, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a,
	0x15, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0xc1, 0x07, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7c, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x73, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x77, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x67, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x1a, 0x62, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x4f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x90, 0x01, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x62, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8a, 0x01, 0x0a, 0x11, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x5f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xea, 0x03, 0x0a, 0x1a, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0xa3, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescData = file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDesc
)

func file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDescData
}

var file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_appointment_v1_appointment_tracking_api_proto_goTypes = []interface{}{
	(*UpdateAppointmentTrackingStatusParams)(nil),               // 0: moego.api.appointment.v1.UpdateAppointmentTrackingStatusParams
	(*UpdateAppointmentTrackingStatusResult)(nil),               // 1: moego.api.appointment.v1.UpdateAppointmentTrackingStatusResult
	(*GetAppointmentTrackingParams)(nil),                        // 2: moego.api.appointment.v1.GetAppointmentTrackingParams
	(*GetAppointmentTrackingResult)(nil),                        // 3: moego.api.appointment.v1.GetAppointmentTrackingResult
	(*ListStaffAppointmentTrackingParams)(nil),                  // 4: moego.api.appointment.v1.ListStaffAppointmentTrackingParams
	(*ListStaffAppointmentTrackingResult)(nil),                  // 5: moego.api.appointment.v1.ListStaffAppointmentTrackingResult
	(*ListStaffAppointmentTrackingParams_Filter)(nil),           // 6: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.Filter
	(*ListStaffAppointmentTrackingParams_ExtraInfoRequest)(nil), // 7: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.ExtraInfoRequest
	(*ListStaffAppointmentTrackingResult_CustomerView)(nil),     // 8: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerView
	(*ListStaffAppointmentTrackingResult_AppointmentView)(nil),  // 9: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentView
	nil,                                   // 10: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentInfoEntry
	nil,                                   // 11: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerInfoEntry
	(v1.StaffLocationStatus)(0),           // 12: moego.models.appointment.v1.StaffLocationStatus
	(*latlng.LatLng)(nil),                 // 13: google.type.LatLng
	(*v1.AppointmentTrackingView)(nil),    // 14: moego.models.appointment.v1.AppointmentTrackingView
	(*v2.PaginationRequest)(nil),          // 15: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),         // 16: moego.utils.v2.PaginationResponse
	(*v11.BusinessCustomerInfoModel)(nil), // 17: moego.models.business_customer.v1.BusinessCustomerInfoModel
	(*v1.AppointmentModel)(nil),           // 18: moego.models.appointment.v1.AppointmentModel
}
var file_moego_api_appointment_v1_appointment_tracking_api_proto_depIdxs = []int32{
	12, // 0: moego.api.appointment.v1.UpdateAppointmentTrackingStatusParams.staff_location_status:type_name -> moego.models.appointment.v1.StaffLocationStatus
	13, // 1: moego.api.appointment.v1.UpdateAppointmentTrackingStatusParams.staff_coordinate:type_name -> google.type.LatLng
	14, // 2: moego.api.appointment.v1.UpdateAppointmentTrackingStatusResult.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTrackingView
	14, // 3: moego.api.appointment.v1.GetAppointmentTrackingResult.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTrackingView
	15, // 4: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	6,  // 5: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.filter:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingParams.Filter
	7,  // 6: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.extra_info_request:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingParams.ExtraInfoRequest
	14, // 7: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTrackingView
	16, // 8: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	10, // 9: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.appointment_info:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentInfoEntry
	11, // 10: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.customer_info:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerInfoEntry
	12, // 11: moego.api.appointment.v1.ListStaffAppointmentTrackingParams.Filter.staff_location_statuses:type_name -> moego.models.appointment.v1.StaffLocationStatus
	17, // 12: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerView.customer_profile:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel
	18, // 13: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentView.appointment:type_name -> moego.models.appointment.v1.AppointmentModel
	9,  // 14: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentInfoEntry.value:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingResult.AppointmentView
	8,  // 15: moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerInfoEntry.value:type_name -> moego.api.appointment.v1.ListStaffAppointmentTrackingResult.CustomerView
	2,  // 16: moego.api.appointment.v1.AppointmentTrackingService.GetAppointmentTracking:input_type -> moego.api.appointment.v1.GetAppointmentTrackingParams
	0,  // 17: moego.api.appointment.v1.AppointmentTrackingService.UpdateAppointmentTrackingStatus:input_type -> moego.api.appointment.v1.UpdateAppointmentTrackingStatusParams
	4,  // 18: moego.api.appointment.v1.AppointmentTrackingService.ListStaffAppointmentTracking:input_type -> moego.api.appointment.v1.ListStaffAppointmentTrackingParams
	3,  // 19: moego.api.appointment.v1.AppointmentTrackingService.GetAppointmentTracking:output_type -> moego.api.appointment.v1.GetAppointmentTrackingResult
	1,  // 20: moego.api.appointment.v1.AppointmentTrackingService.UpdateAppointmentTrackingStatus:output_type -> moego.api.appointment.v1.UpdateAppointmentTrackingStatusResult
	5,  // 21: moego.api.appointment.v1.AppointmentTrackingService.ListStaffAppointmentTracking:output_type -> moego.api.appointment.v1.ListStaffAppointmentTrackingResult
	19, // [19:22] is the sub-list for method output_type
	16, // [16:19] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_appointment_tracking_api_proto_init() }
func file_moego_api_appointment_v1_appointment_tracking_api_proto_init() {
	if File_moego_api_appointment_v1_appointment_tracking_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentTrackingStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentTrackingStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingParams_ExtraInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingResult_CustomerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffAppointmentTrackingResult_AppointmentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_appointment_tracking_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_appointment_tracking_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_appointment_tracking_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_appointment_tracking_api_proto = out.File
	file_moego_api_appointment_v1_appointment_tracking_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_appointment_tracking_api_proto_goTypes = nil
	file_moego_api_appointment_v1_appointment_tracking_api_proto_depIdxs = nil
}
