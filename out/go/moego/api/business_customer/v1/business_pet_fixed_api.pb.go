// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_fixed_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet fixed template params
type ListPetFixedTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetFixedTemplateParams) Reset() {
	*x = ListPetFixedTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedTemplateParams) ProtoMessage() {}

func (x *ListPetFixedTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedTemplateParams.ProtoReflect.Descriptor instead.
func (*ListPetFixedTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{0}
}

// list pet fixed template result
type ListPetFixedTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed list
	Fixeds []*v1.BusinessPetFixedNameView `protobuf:"bytes,1,rep,name=fixeds,proto3" json:"fixeds,omitempty"`
}

func (x *ListPetFixedTemplateResult) Reset() {
	*x = ListPetFixedTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedTemplateResult) ProtoMessage() {}

func (x *ListPetFixedTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedTemplateResult.ProtoReflect.Descriptor instead.
func (*ListPetFixedTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetFixedTemplateResult) GetFixeds() []*v1.BusinessPetFixedNameView {
	if x != nil {
		return x.Fixeds
	}
	return nil
}

// list pet fixed params
type ListPetFixedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetFixedParams) Reset() {
	*x = ListPetFixedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedParams) ProtoMessage() {}

func (x *ListPetFixedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedParams.ProtoReflect.Descriptor instead.
func (*ListPetFixedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{2}
}

// list pet fixed result
type ListPetFixedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed list
	Fixeds []*v1.BusinessPetFixedModel `protobuf:"bytes,1,rep,name=fixeds,proto3" json:"fixeds,omitempty"`
}

func (x *ListPetFixedResult) Reset() {
	*x = ListPetFixedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedResult) ProtoMessage() {}

func (x *ListPetFixedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedResult.ProtoReflect.Descriptor instead.
func (*ListPetFixedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetFixedResult) GetFixeds() []*v1.BusinessPetFixedModel {
	if x != nil {
		return x.Fixeds
	}
	return nil
}

// create pet fixed params
type CreatePetFixedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed
	Fixed *v1.BusinessPetFixedCreateDef `protobuf:"bytes,1,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *CreatePetFixedParams) Reset() {
	*x = CreatePetFixedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetFixedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetFixedParams) ProtoMessage() {}

func (x *CreatePetFixedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetFixedParams.ProtoReflect.Descriptor instead.
func (*CreatePetFixedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetFixedParams) GetFixed() *v1.BusinessPetFixedCreateDef {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// create pet fixed result
type CreatePetFixedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed
	Fixed *v1.BusinessPetFixedModel `protobuf:"bytes,1,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *CreatePetFixedResult) Reset() {
	*x = CreatePetFixedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetFixedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetFixedResult) ProtoMessage() {}

func (x *CreatePetFixedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetFixedResult.ProtoReflect.Descriptor instead.
func (*CreatePetFixedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetFixedResult) GetFixed() *v1.BusinessPetFixedModel {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// update pet fixed params
type UpdatePetFixedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet fixed
	Fixed *v1.BusinessPetFixedUpdateDef `protobuf:"bytes,2,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *UpdatePetFixedParams) Reset() {
	*x = UpdatePetFixedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetFixedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetFixedParams) ProtoMessage() {}

func (x *UpdatePetFixedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetFixedParams.ProtoReflect.Descriptor instead.
func (*UpdatePetFixedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetFixedParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetFixedParams) GetFixed() *v1.BusinessPetFixedUpdateDef {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// update pet fixed result
type UpdatePetFixedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetFixedResult) Reset() {
	*x = UpdatePetFixedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetFixedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetFixedResult) ProtoMessage() {}

func (x *UpdatePetFixedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetFixedResult.ProtoReflect.Descriptor instead.
func (*UpdatePetFixedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{7}
}

// sort pet fixed params
type SortPetFixedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id list, should contain all pet fixed ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetFixedParams) Reset() {
	*x = SortPetFixedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetFixedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetFixedParams) ProtoMessage() {}

func (x *SortPetFixedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetFixedParams.ProtoReflect.Descriptor instead.
func (*SortPetFixedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{8}
}

func (x *SortPetFixedParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet fixed result
type SortPetFixedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetFixedResult) Reset() {
	*x = SortPetFixedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetFixedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetFixedResult) ProtoMessage() {}

func (x *SortPetFixedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetFixedResult.ProtoReflect.Descriptor instead.
func (*SortPetFixedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{9}
}

// delete pet fixed params
type DeletePetFixedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetFixedParams) Reset() {
	*x = DeletePetFixedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetFixedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetFixedParams) ProtoMessage() {}

func (x *DeletePetFixedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetFixedParams.ProtoReflect.Descriptor instead.
func (*DeletePetFixedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePetFixedParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet fixed result
type DeletePetFixedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetFixedResult) Reset() {
	*x = DeletePetFixedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetFixedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetFixedResult) ProtoMessage() {}

func (x *DeletePetFixedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetFixedResult.ProtoReflect.Descriptor instead.
func (*DeletePetFixedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_business_customer_v1_business_pet_fixed_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1c, 0x0a, 0x1a, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x71, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x06, 0x66, 0x69, 0x78, 0x65, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x06, 0x66, 0x69, 0x78, 0x65, 0x64, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x66, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x66, 0x69, 0x78, 0x65, 0x64, 0x73, 0x22, 0x74, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x5c, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x22,
	0x66, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5c, 0x0a, 0x05, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x36, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x50,
	0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x16,
	0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x94, 0x06, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0c, 0x53, 0x6f, 0x72, 0x74, 0x50,
	0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7c, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x95, 0x01,
	0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_business_customer_v1_business_pet_fixed_api_proto_goTypes = []interface{}{
	(*ListPetFixedTemplateParams)(nil),   // 0: moego.api.business_customer.v1.ListPetFixedTemplateParams
	(*ListPetFixedTemplateResult)(nil),   // 1: moego.api.business_customer.v1.ListPetFixedTemplateResult
	(*ListPetFixedParams)(nil),           // 2: moego.api.business_customer.v1.ListPetFixedParams
	(*ListPetFixedResult)(nil),           // 3: moego.api.business_customer.v1.ListPetFixedResult
	(*CreatePetFixedParams)(nil),         // 4: moego.api.business_customer.v1.CreatePetFixedParams
	(*CreatePetFixedResult)(nil),         // 5: moego.api.business_customer.v1.CreatePetFixedResult
	(*UpdatePetFixedParams)(nil),         // 6: moego.api.business_customer.v1.UpdatePetFixedParams
	(*UpdatePetFixedResult)(nil),         // 7: moego.api.business_customer.v1.UpdatePetFixedResult
	(*SortPetFixedParams)(nil),           // 8: moego.api.business_customer.v1.SortPetFixedParams
	(*SortPetFixedResult)(nil),           // 9: moego.api.business_customer.v1.SortPetFixedResult
	(*DeletePetFixedParams)(nil),         // 10: moego.api.business_customer.v1.DeletePetFixedParams
	(*DeletePetFixedResult)(nil),         // 11: moego.api.business_customer.v1.DeletePetFixedResult
	(*v1.BusinessPetFixedNameView)(nil),  // 12: moego.models.business_customer.v1.BusinessPetFixedNameView
	(*v1.BusinessPetFixedModel)(nil),     // 13: moego.models.business_customer.v1.BusinessPetFixedModel
	(*v1.BusinessPetFixedCreateDef)(nil), // 14: moego.models.business_customer.v1.BusinessPetFixedCreateDef
	(*v1.BusinessPetFixedUpdateDef)(nil), // 15: moego.models.business_customer.v1.BusinessPetFixedUpdateDef
}
var file_moego_api_business_customer_v1_business_pet_fixed_api_proto_depIdxs = []int32{
	12, // 0: moego.api.business_customer.v1.ListPetFixedTemplateResult.fixeds:type_name -> moego.models.business_customer.v1.BusinessPetFixedNameView
	13, // 1: moego.api.business_customer.v1.ListPetFixedResult.fixeds:type_name -> moego.models.business_customer.v1.BusinessPetFixedModel
	14, // 2: moego.api.business_customer.v1.CreatePetFixedParams.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedCreateDef
	13, // 3: moego.api.business_customer.v1.CreatePetFixedResult.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedModel
	15, // 4: moego.api.business_customer.v1.UpdatePetFixedParams.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedUpdateDef
	0,  // 5: moego.api.business_customer.v1.BusinessPetFixedService.ListPetFixedTemplate:input_type -> moego.api.business_customer.v1.ListPetFixedTemplateParams
	2,  // 6: moego.api.business_customer.v1.BusinessPetFixedService.ListPetFixed:input_type -> moego.api.business_customer.v1.ListPetFixedParams
	4,  // 7: moego.api.business_customer.v1.BusinessPetFixedService.CreatePetFixed:input_type -> moego.api.business_customer.v1.CreatePetFixedParams
	6,  // 8: moego.api.business_customer.v1.BusinessPetFixedService.UpdatePetFixed:input_type -> moego.api.business_customer.v1.UpdatePetFixedParams
	8,  // 9: moego.api.business_customer.v1.BusinessPetFixedService.SortPetFixed:input_type -> moego.api.business_customer.v1.SortPetFixedParams
	10, // 10: moego.api.business_customer.v1.BusinessPetFixedService.DeletePetFixed:input_type -> moego.api.business_customer.v1.DeletePetFixedParams
	1,  // 11: moego.api.business_customer.v1.BusinessPetFixedService.ListPetFixedTemplate:output_type -> moego.api.business_customer.v1.ListPetFixedTemplateResult
	3,  // 12: moego.api.business_customer.v1.BusinessPetFixedService.ListPetFixed:output_type -> moego.api.business_customer.v1.ListPetFixedResult
	5,  // 13: moego.api.business_customer.v1.BusinessPetFixedService.CreatePetFixed:output_type -> moego.api.business_customer.v1.CreatePetFixedResult
	7,  // 14: moego.api.business_customer.v1.BusinessPetFixedService.UpdatePetFixed:output_type -> moego.api.business_customer.v1.UpdatePetFixedResult
	9,  // 15: moego.api.business_customer.v1.BusinessPetFixedService.SortPetFixed:output_type -> moego.api.business_customer.v1.SortPetFixedResult
	11, // 16: moego.api.business_customer.v1.BusinessPetFixedService.DeletePetFixed:output_type -> moego.api.business_customer.v1.DeletePetFixedResult
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_fixed_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_fixed_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_fixed_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetFixedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetFixedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetFixedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetFixedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetFixedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetFixedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetFixedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetFixedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_fixed_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_fixed_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_fixed_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_fixed_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_fixed_api_proto_depIdxs = nil
}
