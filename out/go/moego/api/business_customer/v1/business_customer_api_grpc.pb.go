// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_customer_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerServiceClient is the client API for BusinessCustomerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerServiceClient interface {
	// Update customer's preferred business
	UpdateCustomerPreferredBusiness(ctx context.Context, in *UpdateCustomerPreferredBusinessParams, opts ...grpc.CallOption) (*UpdateCustomerPreferredBusinessResult, error)
	// List duplicate customer groups
	ListDuplicateCustomerGroups(ctx context.Context, in *ListDuplicateCustomerGroupsParams, opts ...grpc.CallOption) (*ListDuplicateCustomerGroupsResult, error)
	// preview customer merge
	PreviewCustomerMerge(ctx context.Context, in *PreviewCustomerMergeParams, opts ...grpc.CallOption) (*PreviewCustomerMergeResult, error)
	// Merge customers
	MergeCustomers(ctx context.Context, in *MergeCustomersParams, opts ...grpc.CallOption) (*MergeCustomersResult, error)
	// check customer merge status
	CheckCustomerMergeStatus(ctx context.Context, in *CheckCustomerMergeStatusParams, opts ...grpc.CallOption) (*CheckCustomerMergeStatusResult, error)
}

type businessCustomerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerServiceClient(cc grpc.ClientConnInterface) BusinessCustomerServiceClient {
	return &businessCustomerServiceClient{cc}
}

func (c *businessCustomerServiceClient) UpdateCustomerPreferredBusiness(ctx context.Context, in *UpdateCustomerPreferredBusinessParams, opts ...grpc.CallOption) (*UpdateCustomerPreferredBusinessResult, error) {
	out := new(UpdateCustomerPreferredBusinessResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerService/UpdateCustomerPreferredBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) ListDuplicateCustomerGroups(ctx context.Context, in *ListDuplicateCustomerGroupsParams, opts ...grpc.CallOption) (*ListDuplicateCustomerGroupsResult, error) {
	out := new(ListDuplicateCustomerGroupsResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerService/ListDuplicateCustomerGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) PreviewCustomerMerge(ctx context.Context, in *PreviewCustomerMergeParams, opts ...grpc.CallOption) (*PreviewCustomerMergeResult, error) {
	out := new(PreviewCustomerMergeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerService/PreviewCustomerMerge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) MergeCustomers(ctx context.Context, in *MergeCustomersParams, opts ...grpc.CallOption) (*MergeCustomersResult, error) {
	out := new(MergeCustomersResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerService/MergeCustomers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) CheckCustomerMergeStatus(ctx context.Context, in *CheckCustomerMergeStatusParams, opts ...grpc.CallOption) (*CheckCustomerMergeStatusResult, error) {
	out := new(CheckCustomerMergeStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerService/CheckCustomerMergeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerServiceServer is the server API for BusinessCustomerService service.
// All implementations must embed UnimplementedBusinessCustomerServiceServer
// for forward compatibility
type BusinessCustomerServiceServer interface {
	// Update customer's preferred business
	UpdateCustomerPreferredBusiness(context.Context, *UpdateCustomerPreferredBusinessParams) (*UpdateCustomerPreferredBusinessResult, error)
	// List duplicate customer groups
	ListDuplicateCustomerGroups(context.Context, *ListDuplicateCustomerGroupsParams) (*ListDuplicateCustomerGroupsResult, error)
	// preview customer merge
	PreviewCustomerMerge(context.Context, *PreviewCustomerMergeParams) (*PreviewCustomerMergeResult, error)
	// Merge customers
	MergeCustomers(context.Context, *MergeCustomersParams) (*MergeCustomersResult, error)
	// check customer merge status
	CheckCustomerMergeStatus(context.Context, *CheckCustomerMergeStatusParams) (*CheckCustomerMergeStatusResult, error)
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

// UnimplementedBusinessCustomerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerServiceServer struct {
}

func (UnimplementedBusinessCustomerServiceServer) UpdateCustomerPreferredBusiness(context.Context, *UpdateCustomerPreferredBusinessParams) (*UpdateCustomerPreferredBusinessResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerPreferredBusiness not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) ListDuplicateCustomerGroups(context.Context, *ListDuplicateCustomerGroupsParams) (*ListDuplicateCustomerGroupsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDuplicateCustomerGroups not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) PreviewCustomerMerge(context.Context, *PreviewCustomerMergeParams) (*PreviewCustomerMergeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewCustomerMerge not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) MergeCustomers(context.Context, *MergeCustomersParams) (*MergeCustomersResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeCustomers not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) CheckCustomerMergeStatus(context.Context, *CheckCustomerMergeStatusParams) (*CheckCustomerMergeStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCustomerMergeStatus not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) mustEmbedUnimplementedBusinessCustomerServiceServer() {
}

// UnsafeBusinessCustomerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

func RegisterBusinessCustomerServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerServiceServer) {
	s.RegisterService(&BusinessCustomerService_ServiceDesc, srv)
}

func _BusinessCustomerService_UpdateCustomerPreferredBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerPreferredBusinessParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).UpdateCustomerPreferredBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerService/UpdateCustomerPreferredBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).UpdateCustomerPreferredBusiness(ctx, req.(*UpdateCustomerPreferredBusinessParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_ListDuplicateCustomerGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDuplicateCustomerGroupsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).ListDuplicateCustomerGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerService/ListDuplicateCustomerGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).ListDuplicateCustomerGroups(ctx, req.(*ListDuplicateCustomerGroupsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_PreviewCustomerMerge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewCustomerMergeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).PreviewCustomerMerge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerService/PreviewCustomerMerge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).PreviewCustomerMerge(ctx, req.(*PreviewCustomerMergeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_MergeCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeCustomersParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).MergeCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerService/MergeCustomers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).MergeCustomers(ctx, req.(*MergeCustomersParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_CheckCustomerMergeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCustomerMergeStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).CheckCustomerMergeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerService/CheckCustomerMergeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).CheckCustomerMergeStatus(ctx, req.(*CheckCustomerMergeStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessCustomerService",
	HandlerType: (*BusinessCustomerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateCustomerPreferredBusiness",
			Handler:    _BusinessCustomerService_UpdateCustomerPreferredBusiness_Handler,
		},
		{
			MethodName: "ListDuplicateCustomerGroups",
			Handler:    _BusinessCustomerService_ListDuplicateCustomerGroups_Handler,
		},
		{
			MethodName: "PreviewCustomerMerge",
			Handler:    _BusinessCustomerService_PreviewCustomerMerge_Handler,
		},
		{
			MethodName: "MergeCustomers",
			Handler:    _BusinessCustomerService_MergeCustomers_Handler,
		},
		{
			MethodName: "CheckCustomerMergeStatus",
			Handler:    _BusinessCustomerService_CheckCustomerMergeStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_customer_api.proto",
}
