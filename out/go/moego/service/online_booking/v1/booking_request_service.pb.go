// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/booking_request_service.proto

package onlinebookingsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create BookingRequest request
type CreateBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// businessId
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customerId
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointmentId
	AppointmentId *int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// startDate
	StartDate *string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// startTime
	StartTime *int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// endDate
	EndDate *string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// endTime
	EndTime *int32 `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// status
	Status *int32 `protobuf:"varint,8,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// isPrepaid
	IsPrepaid *bool `protobuf:"varint,9,opt,name=is_prepaid,json=isPrepaid,proto3,oneof" json:"is_prepaid,omitempty"`
	// additionalNote
	AdditionalNote *string `protobuf:"bytes,10,opt,name=additional_note,json=additionalNote,proto3,oneof" json:"additional_note,omitempty"`
	// sourcePlatform
	SourcePlatform *string `protobuf:"bytes,11,opt,name=source_platform,json=sourcePlatform,proto3,oneof" json:"source_platform,omitempty"`
	// serviceTypeInclude
	//
	//	optional int32 service_type_include = 12;
	//
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	// Company id
	CompanyId *int64 `protobuf:"varint,15,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// Services
	Services []*CreateBookingRequestRequest_Service `protobuf:"bytes,16,rep,name=services,proto3" json:"services,omitempty"`
	// Additional attributes
	Attr *v1.BookingRequestModel_Attr `protobuf:"bytes,17,opt,name=attr,proto3,oneof" json:"attr,omitempty"`
	// payment status, use NO_PAYMENT if not set
	PaymentStatus *v1.BookingRequestModel_PaymentStatus `protobuf:"varint,18,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_PaymentStatus,oneof" json:"payment_status,omitempty"`
	// need create order, default false
	NeedCreateOrder bool `protobuf:"varint,19,opt,name=need_create_order,json=needCreateOrder,proto3" json:"need_create_order,omitempty"`
	// member ship apply info
	Membership *CreateBookingRequestRequest_Membership `protobuf:"bytes,20,opt,name=membership,proto3,oneof" json:"membership,omitempty"`
	// comment business private note
	Comment *string `protobuf:"bytes,21,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	// booking request source
	Source *v1.BookingRequestModel_Source `protobuf:"varint,22,opt,name=source,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source,oneof" json:"source,omitempty"`
	// source id
	SourceId *int64 `protobuf:"varint,23,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
}

func (x *CreateBookingRequestRequest) Reset() {
	*x = CreateBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest) ProtoMessage() {}

func (x *CreateBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBookingRequestRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetIsPrepaid() bool {
	if x != nil && x.IsPrepaid != nil {
		return *x.IsPrepaid
	}
	return false
}

func (x *CreateBookingRequestRequest) GetAdditionalNote() string {
	if x != nil && x.AdditionalNote != nil {
		return *x.AdditionalNote
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetSourcePlatform() string {
	if x != nil && x.SourcePlatform != nil {
		return *x.SourcePlatform
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetServices() []*CreateBookingRequestRequest_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetAttr() *v1.BookingRequestModel_Attr {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetPaymentStatus() v1.BookingRequestModel_PaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.BookingRequestModel_PaymentStatus(0)
}

func (x *CreateBookingRequestRequest) GetNeedCreateOrder() bool {
	if x != nil {
		return x.NeedCreateOrder
	}
	return false
}

func (x *CreateBookingRequestRequest) GetMembership() *CreateBookingRequestRequest_Membership {
	if x != nil {
		return x.Membership
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetSource() v1.BookingRequestModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.BookingRequestModel_Source(0)
}

func (x *CreateBookingRequestRequest) GetSourceId() int64 {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return 0
}

// Get Booking Request detail request
type GetBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// associated models
	AssociatedModels []v1.BookingRequestAssociatedModel `protobuf:"varint,2,rep,packed,name=associated_models,json=associatedModels,proto3,enum=moego.models.online_booking.v1.BookingRequestAssociatedModel" json:"associated_models,omitempty"`
	// payment statuses
	PaymentStatuses []v1.BookingRequestModel_PaymentStatus `protobuf:"varint,3,rep,packed,name=payment_statuses,json=paymentStatuses,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_PaymentStatus" json:"payment_statuses,omitempty"`
}

func (x *GetBookingRequestRequest) Reset() {
	*x = GetBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestRequest) ProtoMessage() {}

func (x *GetBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*GetBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetBookingRequestRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *GetBookingRequestRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetBookingRequestRequest) GetAssociatedModels() []v1.BookingRequestAssociatedModel {
	if x != nil {
		return x.AssociatedModels
	}
	return nil
}

func (x *GetBookingRequestRequest) GetPaymentStatuses() []v1.BookingRequestModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatuses
	}
	return nil
}

// Get BookingRequest response
type GetBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Existing record
	BookingRequest *v1.BookingRequestModel `protobuf:"bytes,1,opt,name=booking_request,json=bookingRequest,proto3" json:"booking_request,omitempty"`
	// waitlist extra
	WaitlistExtras *WaitlistExtra `protobuf:"bytes,2,opt,name=waitlist_extras,json=waitlistExtras,proto3" json:"waitlist_extras,omitempty"`
}

func (x *GetBookingRequestResponse) Reset() {
	*x = GetBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequestResponse) ProtoMessage() {}

func (x *GetBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*GetBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetBookingRequestResponse) GetBookingRequest() *v1.BookingRequestModel {
	if x != nil {
		return x.BookingRequest
	}
	return nil
}

func (x *GetBookingRequestResponse) GetWaitlistExtras() *WaitlistExtra {
	if x != nil {
		return x.WaitlistExtras
	}
	return nil
}

// List BookingRequest request
type ListBookingRequestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// associated models
	AssociatedModels []v1.BookingRequestAssociatedModel `protobuf:"varint,2,rep,packed,name=associated_models,json=associatedModels,proto3,enum=moego.models.online_booking.v1.BookingRequestAssociatedModel" json:"associated_models,omitempty"`
	// contains statuses
	Statuses []v1.BookingRequestStatus `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus" json:"statuses,omitempty"`
	// business ids, optional
	BusinessIds []int64 `protobuf:"varint,4,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// start date filter, used to filter start date
	StartDate *date.Date `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date filter, used to filter start date
	EndDate *date.Date `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// order by (support multi fields), optional
	OrderBys []*v2.OrderBy `protobuf:"bytes,7,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,8,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// search keywords
	Keywords *string `protobuf:"bytes,9,opt,name=keywords,proto3,oneof" json:"keywords,omitempty"`
	// service item type, If empty, it means all service item types
	ServiceItems []v11.ServiceItemType `protobuf:"varint,10,rep,packed,name=service_items,json=serviceItems,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_items,omitempty"`
	// service type include, If empty, it means all service item types
	ServiceTypeIncludes []int32 `protobuf:"varint,11,rep,packed,name=service_type_includes,json=serviceTypeIncludes,proto3" json:"service_type_includes,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,12,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// customer ids
	CustomerId []int64 `protobuf:"varint,13,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,14,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// payment status
	PaymentStatuses []v1.BookingRequestModel_PaymentStatus `protobuf:"varint,15,rep,packed,name=payment_statuses,json=paymentStatuses,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_PaymentStatus" json:"payment_statuses,omitempty"`
	// Booking request source
	Source *v1.BookingRequestModel_Source `protobuf:"varint,16,opt,name=source,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source,oneof" json:"source,omitempty"`
	// waitlist expired
	IsWaitlistExpired *bool `protobuf:"varint,17,opt,name=is_waitlist_expired,json=isWaitlistExpired,proto3,oneof" json:"is_waitlist_expired,omitempty"`
	// waitlist expired
	IsWaitlistAvailable *bool `protobuf:"varint,18,opt,name=is_waitlist_available,json=isWaitlistAvailable,proto3,oneof" json:"is_waitlist_available,omitempty"`
	// waitlist order by price desc
	OrderPriceDesc *bool `protobuf:"varint,19,opt,name=order_price_desc,json=orderPriceDesc,proto3,oneof" json:"order_price_desc,omitempty"`
	// latest end date
	LatestEndDate *date.Date `protobuf:"bytes,20,opt,name=latest_end_date,json=latestEndDate,proto3,oneof" json:"latest_end_date,omitempty"`
	// Booking request source list
	Sources []v1.BookingRequestModel_Source `protobuf:"varint,21,rep,packed,name=sources,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source" json:"sources,omitempty"`
}

func (x *ListBookingRequestsRequest) Reset() {
	*x = ListBookingRequestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingRequestsRequest) ProtoMessage() {}

func (x *ListBookingRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingRequestsRequest.ProtoReflect.Descriptor instead.
func (*ListBookingRequestsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListBookingRequestsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListBookingRequestsRequest) GetAssociatedModels() []v1.BookingRequestAssociatedModel {
	if x != nil {
		return x.AssociatedModels
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetStatuses() []v1.BookingRequestStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *ListBookingRequestsRequest) GetServiceItems() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItems
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetServiceTypeIncludes() []int32 {
	if x != nil {
		return x.ServiceTypeIncludes
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListBookingRequestsRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetPaymentStatuses() []v1.BookingRequestModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatuses
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetSource() v1.BookingRequestModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.BookingRequestModel_Source(0)
}

func (x *ListBookingRequestsRequest) GetIsWaitlistExpired() bool {
	if x != nil && x.IsWaitlistExpired != nil {
		return *x.IsWaitlistExpired
	}
	return false
}

func (x *ListBookingRequestsRequest) GetIsWaitlistAvailable() bool {
	if x != nil && x.IsWaitlistAvailable != nil {
		return *x.IsWaitlistAvailable
	}
	return false
}

func (x *ListBookingRequestsRequest) GetOrderPriceDesc() bool {
	if x != nil && x.OrderPriceDesc != nil {
		return *x.OrderPriceDesc
	}
	return false
}

func (x *ListBookingRequestsRequest) GetLatestEndDate() *date.Date {
	if x != nil {
		return x.LatestEndDate
	}
	return nil
}

func (x *ListBookingRequestsRequest) GetSources() []v1.BookingRequestModel_Source {
	if x != nil {
		return x.Sources
	}
	return nil
}

// List BookingRequest response
type ListBookingRequestsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request list
	BookingRequests []*v1.BookingRequestModel `protobuf:"bytes,1,rep,name=booking_requests,json=bookingRequests,proto3" json:"booking_requests,omitempty"`
	// waitlist extra
	WaitlistExtras []*WaitlistExtra `protobuf:"bytes,3,rep,name=waitlist_extras,json=waitlistExtras,proto3" json:"waitlist_extras,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListBookingRequestsResponse) Reset() {
	*x = ListBookingRequestsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingRequestsResponse) ProtoMessage() {}

func (x *ListBookingRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingRequestsResponse.ProtoReflect.Descriptor instead.
func (*ListBookingRequestsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListBookingRequestsResponse) GetBookingRequests() []*v1.BookingRequestModel {
	if x != nil {
		return x.BookingRequests
	}
	return nil
}

func (x *ListBookingRequestsResponse) GetWaitlistExtras() []*WaitlistExtra {
	if x != nil {
		return x.WaitlistExtras
	}
	return nil
}

func (x *ListBookingRequestsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// List Waitlists request
type ListWaitlistsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// associated models
	AssociatedModels []v1.BookingRequestAssociatedModel `protobuf:"varint,2,rep,packed,name=associated_models,json=associatedModels,proto3,enum=moego.models.online_booking.v1.BookingRequestAssociatedModel" json:"associated_models,omitempty"`
	// business ids, optional
	BusinessIds []int64 `protobuf:"varint,4,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// start date filter, used to filter start date
	StartDate *date.Date `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date filter, used to filter start date
	EndDate *date.Date `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// order by (support multi fields), optional
	OrderBys []*v2.OrderBy `protobuf:"bytes,7,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,8,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// search keywords
	Keywords *string `protobuf:"bytes,9,opt,name=keywords,proto3,oneof" json:"keywords,omitempty"`
	// service item type, If empty, it means all service item types
	ServiceItems []v11.ServiceItemType `protobuf:"varint,10,rep,packed,name=service_items,json=serviceItems,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_items,omitempty"`
	// service type include, If empty, it means all service item types
	ServiceTypeIncludes []int32 `protobuf:"varint,11,rep,packed,name=service_type_includes,json=serviceTypeIncludes,proto3" json:"service_type_includes,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,12,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// customer ids
	CustomerId []int64 `protobuf:"varint,13,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Booking request source
	Source *v1.BookingRequestModel_Source `protobuf:"varint,16,opt,name=source,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source,oneof" json:"source,omitempty"`
	// waitlist expired
	IsWaitlistExpired *bool `protobuf:"varint,17,opt,name=is_waitlist_expired,json=isWaitlistExpired,proto3,oneof" json:"is_waitlist_expired,omitempty"`
	// waitlist expired
	IsWaitlistAvailable *bool `protobuf:"varint,18,opt,name=is_waitlist_available,json=isWaitlistAvailable,proto3,oneof" json:"is_waitlist_available,omitempty"`
	// waitlist order by price desc
	OrderPriceDesc *bool `protobuf:"varint,19,opt,name=order_price_desc,json=orderPriceDesc,proto3,oneof" json:"order_price_desc,omitempty"`
	// latest end date
	LatestEndDate *date.Date `protobuf:"bytes,20,opt,name=latest_end_date,json=latestEndDate,proto3,oneof" json:"latest_end_date,omitempty"`
	// Booking request source list
	Sources []v1.BookingRequestModel_Source `protobuf:"varint,21,rep,packed,name=sources,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_Source" json:"sources,omitempty"`
}

func (x *ListWaitlistsRequest) Reset() {
	*x = ListWaitlistsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWaitlistsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWaitlistsRequest) ProtoMessage() {}

func (x *ListWaitlistsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWaitlistsRequest.ProtoReflect.Descriptor instead.
func (*ListWaitlistsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListWaitlistsRequest) GetAssociatedModels() []v1.BookingRequestAssociatedModel {
	if x != nil {
		return x.AssociatedModels
	}
	return nil
}

func (x *ListWaitlistsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListWaitlistsRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListWaitlistsRequest) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListWaitlistsRequest) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ListWaitlistsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWaitlistsRequest) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *ListWaitlistsRequest) GetServiceItems() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItems
	}
	return nil
}

func (x *ListWaitlistsRequest) GetServiceTypeIncludes() []int32 {
	if x != nil {
		return x.ServiceTypeIncludes
	}
	return nil
}

func (x *ListWaitlistsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListWaitlistsRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *ListWaitlistsRequest) GetSource() v1.BookingRequestModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.BookingRequestModel_Source(0)
}

func (x *ListWaitlistsRequest) GetIsWaitlistExpired() bool {
	if x != nil && x.IsWaitlistExpired != nil {
		return *x.IsWaitlistExpired
	}
	return false
}

func (x *ListWaitlistsRequest) GetIsWaitlistAvailable() bool {
	if x != nil && x.IsWaitlistAvailable != nil {
		return *x.IsWaitlistAvailable
	}
	return false
}

func (x *ListWaitlistsRequest) GetOrderPriceDesc() bool {
	if x != nil && x.OrderPriceDesc != nil {
		return *x.OrderPriceDesc
	}
	return false
}

func (x *ListWaitlistsRequest) GetLatestEndDate() *date.Date {
	if x != nil {
		return x.LatestEndDate
	}
	return nil
}

func (x *ListWaitlistsRequest) GetSources() []v1.BookingRequestModel_Source {
	if x != nil {
		return x.Sources
	}
	return nil
}

// List waitlist response
type ListWaitlistsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request list
	BookingRequests []*v1.BookingRequestModel `protobuf:"bytes,1,rep,name=booking_requests,json=bookingRequests,proto3" json:"booking_requests,omitempty"`
	// waitlist extra
	WaitlistExtras []*WaitlistExtra `protobuf:"bytes,3,rep,name=waitlist_extras,json=waitlistExtras,proto3" json:"waitlist_extras,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListWaitlistsResponse) Reset() {
	*x = ListWaitlistsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWaitlistsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWaitlistsResponse) ProtoMessage() {}

func (x *ListWaitlistsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWaitlistsResponse.ProtoReflect.Descriptor instead.
func (*ListWaitlistsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListWaitlistsResponse) GetBookingRequests() []*v1.BookingRequestModel {
	if x != nil {
		return x.BookingRequests
	}
	return nil
}

func (x *ListWaitlistsResponse) GetWaitlistExtras() []*WaitlistExtra {
	if x != nil {
		return x.WaitlistExtras
	}
	return nil
}

func (x *ListWaitlistsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// waitlist extra
type WaitlistExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// is expired
	IsExpired bool `protobuf:"varint,3,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
}

func (x *WaitlistExtra) Reset() {
	*x = WaitlistExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistExtra) ProtoMessage() {}

func (x *WaitlistExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistExtra.ProtoReflect.Descriptor instead.
func (*WaitlistExtra) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{7}
}

func (x *WaitlistExtra) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WaitlistExtra) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *WaitlistExtra) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

// Create a grooming only request
type CreateGroomingOnlyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request
	BookingRequest *v1.BookingRequestDef `protobuf:"bytes,1,opt,name=booking_request,json=bookingRequest,proto3" json:"booking_request,omitempty"`
	// Grooming service detail
	GroomingServiceDetails []*v1.GroomingServiceDetailDef `protobuf:"bytes,2,rep,name=grooming_service_details,json=groomingServiceDetails,proto3" json:"grooming_service_details,omitempty"`
}

func (x *CreateGroomingOnlyRequest) Reset() {
	*x = CreateGroomingOnlyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGroomingOnlyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroomingOnlyRequest) ProtoMessage() {}

func (x *CreateGroomingOnlyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroomingOnlyRequest.ProtoReflect.Descriptor instead.
func (*CreateGroomingOnlyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateGroomingOnlyRequest) GetBookingRequest() *v1.BookingRequestDef {
	if x != nil {
		return x.BookingRequest
	}
	return nil
}

func (x *CreateGroomingOnlyRequest) GetGroomingServiceDetails() []*v1.GroomingServiceDetailDef {
	if x != nil {
		return x.GroomingServiceDetails
	}
	return nil
}

// Create a grooming only response
type CreateGroomingOnlyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateGroomingOnlyResponse) Reset() {
	*x = CreateGroomingOnlyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGroomingOnlyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroomingOnlyResponse) ProtoMessage() {}

func (x *CreateGroomingOnlyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroomingOnlyResponse.ProtoReflect.Descriptor instead.
func (*CreateGroomingOnlyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{9}
}

// Update booking request status request
type UpdateBookingRequestStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// updated status
	Status v1.BookingRequestStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus" json:"status,omitempty"`
}

func (x *UpdateBookingRequestStatusRequest) Reset() {
	*x = UpdateBookingRequestStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestStatusRequest) ProtoMessage() {}

func (x *UpdateBookingRequestStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateBookingRequestStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateBookingRequestStatusRequest) GetStatus() v1.BookingRequestStatus {
	if x != nil {
		return x.Status
	}
	return v1.BookingRequestStatus(0)
}

// Update booking request status response
type UpdateBookingRequestStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated result
	UpdatedResult bool `protobuf:"varint,1,opt,name=updated_result,json=updatedResult,proto3" json:"updated_result,omitempty"`
}

func (x *UpdateBookingRequestStatusResponse) Reset() {
	*x = UpdateBookingRequestStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestStatusResponse) ProtoMessage() {}

func (x *UpdateBookingRequestStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateBookingRequestStatusResponse) GetUpdatedResult() bool {
	if x != nil {
		return x.UpdatedResult
	}
	return false
}

// retry failed events request
type RetryFailedEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RetryFailedEventsRequest) Reset() {
	*x = RetryFailedEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryFailedEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryFailedEventsRequest) ProtoMessage() {}

func (x *RetryFailedEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryFailedEventsRequest.ProtoReflect.Descriptor instead.
func (*RetryFailedEventsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{12}
}

// retry failed events response
type RetryFailedEventsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RetryFailedEventsResponse) Reset() {
	*x = RetryFailedEventsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryFailedEventsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryFailedEventsResponse) ProtoMessage() {}

func (x *RetryFailedEventsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryFailedEventsResponse.ProtoReflect.Descriptor instead.
func (*RetryFailedEventsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{13}
}

// Update booking request request
type UpdateBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,15,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,16,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id, generated after the booking request is scheduled
	AppointmentId *int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// start time, the minutes from 00:00
	StartTime *int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end date, format: yyyy-mm-dd
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// end time, the minutes from 00:00
	EndTime *int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// updated status
	Status *v1.BookingRequestStatus `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus,oneof" json:"status,omitempty"`
	// the services, If this parameter is not passed, it will not be updated
	// deprecated by Freeman since 2025/6/9, use service_details instead
	//
	// Deprecated: Do not use.
	Services []*UpdateBookingRequestRequest_Service `protobuf:"bytes,10,rep,name=services,proto3" json:"services,omitempty"`
	// the services detail to update
	// services 只支持修改操作，service_details 可以支持增删改
	ServiceDetails []*UpdateBookingRequestRequest_ServiceDetail `protobuf:"bytes,14,rep,name=service_details,json=serviceDetails,proto3" json:"service_details,omitempty"`
	// Additional attributes
	// NOTE：这个操作会全量更新 attr 字段，如果只需要更新部分字段，需要先获取原始数据，然后合并更新字段
	Attr *v1.BookingRequestModel_Attr `protobuf:"bytes,11,opt,name=attr,proto3,oneof" json:"attr,omitempty"`
	// payment status
	PaymentStatus *v1.BookingRequestModel_PaymentStatus `protobuf:"varint,12,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_PaymentStatus,oneof" json:"payment_status,omitempty"`
	// comment business private note
	Comment *string `protobuf:"bytes,13,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *UpdateBookingRequestRequest) Reset() {
	*x = UpdateBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest) ProtoMessage() {}

func (x *UpdateBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateBookingRequestRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *UpdateBookingRequestRequest) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *UpdateBookingRequestRequest) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UpdateBookingRequestRequest) GetStatus() v1.BookingRequestStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.BookingRequestStatus(0)
}

// Deprecated: Do not use.
func (x *UpdateBookingRequestRequest) GetServices() []*UpdateBookingRequestRequest_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *UpdateBookingRequestRequest) GetServiceDetails() []*UpdateBookingRequestRequest_ServiceDetail {
	if x != nil {
		return x.ServiceDetails
	}
	return nil
}

func (x *UpdateBookingRequestRequest) GetAttr() *v1.BookingRequestModel_Attr {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *UpdateBookingRequestRequest) GetPaymentStatus() v1.BookingRequestModel_PaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.BookingRequestModel_PaymentStatus(0)
}

func (x *UpdateBookingRequestRequest) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

// Update booking request response
type UpdateBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateBookingRequestResponse) Reset() {
	*x = UpdateBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestResponse) ProtoMessage() {}

func (x *UpdateBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{15}
}

// replace booking request request
type ReplaceBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// start time, the minutes from 00:00
	StartTime *int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end date, format: yyyy-mm-dd
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// end time, the minutes from 00:00
	EndTime *int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// updated status
	Status *v1.BookingRequestStatus `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus,oneof" json:"status,omitempty"`
	// the services, If this parameter is not passed, it will not be updated
	Services []*ReplaceBookingRequestRequest_Service `protobuf:"bytes,10,rep,name=services,proto3" json:"services,omitempty"`
	// comment business private note
	Comment *string `protobuf:"bytes,13,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *ReplaceBookingRequestRequest) Reset() {
	*x = ReplaceBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReplaceBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplaceBookingRequestRequest) ProtoMessage() {}

func (x *ReplaceBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplaceBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*ReplaceBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{16}
}

func (x *ReplaceBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReplaceBookingRequestRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *ReplaceBookingRequestRequest) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *ReplaceBookingRequestRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *ReplaceBookingRequestRequest) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *ReplaceBookingRequestRequest) GetStatus() v1.BookingRequestStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.BookingRequestStatus(0)
}

func (x *ReplaceBookingRequestRequest) GetServices() []*ReplaceBookingRequestRequest_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ReplaceBookingRequestRequest) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

// replace booking request response
type ReplaceBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReplaceBookingRequestResponse) Reset() {
	*x = ReplaceBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReplaceBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplaceBookingRequestResponse) ProtoMessage() {}

func (x *ReplaceBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplaceBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*ReplaceBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{17}
}

// get auto assign request
type GetAutoAssignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAutoAssignRequest) Reset() {
	*x = GetAutoAssignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoAssignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoAssignRequest) ProtoMessage() {}

func (x *GetAutoAssignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoAssignRequest.ProtoReflect.Descriptor instead.
func (*GetAutoAssignRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetAutoAssignRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAutoAssignRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAutoAssignRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get auto assign response
type GetAutoAssignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding assign info require
	BoardingAssignRequires []*GetAutoAssignResponse_AssignRequire `protobuf:"bytes,1,rep,name=boarding_assign_requires,json=boardingAssignRequires,proto3" json:"boarding_assign_requires,omitempty"`
	// assign result. pet to lodging unit id
	PetToLodgings []*v1.PetToLodgingDef `protobuf:"bytes,2,rep,name=pet_to_lodgings,json=petToLodgings,proto3" json:"pet_to_lodgings,omitempty"`
	// lodging details for auto assign
	Lodgings []*GetAutoAssignResponse_LodgingDetail `protobuf:"bytes,3,rep,name=lodgings,proto3" json:"lodgings,omitempty"`
	// evaluation assign info require
	EvaluationAssignRequires []*GetAutoAssignResponse_AssignRequire `protobuf:"bytes,4,rep,name=evaluation_assign_requires,json=evaluationAssignRequires,proto3" json:"evaluation_assign_requires,omitempty"`
	// assign result. evaluation pet to staff
	EvaluationPetToStaffs []*v1.PetToStaffDef `protobuf:"bytes,5,rep,name=evaluation_pet_to_staffs,json=evaluationPetToStaffs,proto3" json:"evaluation_pet_to_staffs,omitempty"`
	// daycare assign info require
	DaycareAssignRequires []*GetAutoAssignResponse_AssignRequire `protobuf:"bytes,6,rep,name=daycare_assign_requires,json=daycareAssignRequires,proto3" json:"daycare_assign_requires,omitempty"`
}

func (x *GetAutoAssignResponse) Reset() {
	*x = GetAutoAssignResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoAssignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoAssignResponse) ProtoMessage() {}

func (x *GetAutoAssignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoAssignResponse.ProtoReflect.Descriptor instead.
func (*GetAutoAssignResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetAutoAssignResponse) GetBoardingAssignRequires() []*GetAutoAssignResponse_AssignRequire {
	if x != nil {
		return x.BoardingAssignRequires
	}
	return nil
}

func (x *GetAutoAssignResponse) GetPetToLodgings() []*v1.PetToLodgingDef {
	if x != nil {
		return x.PetToLodgings
	}
	return nil
}

func (x *GetAutoAssignResponse) GetLodgings() []*GetAutoAssignResponse_LodgingDetail {
	if x != nil {
		return x.Lodgings
	}
	return nil
}

func (x *GetAutoAssignResponse) GetEvaluationAssignRequires() []*GetAutoAssignResponse_AssignRequire {
	if x != nil {
		return x.EvaluationAssignRequires
	}
	return nil
}

func (x *GetAutoAssignResponse) GetEvaluationPetToStaffs() []*v1.PetToStaffDef {
	if x != nil {
		return x.EvaluationPetToStaffs
	}
	return nil
}

func (x *GetAutoAssignResponse) GetDaycareAssignRequires() []*GetAutoAssignResponse_AssignRequire {
	if x != nil {
		return x.DaycareAssignRequires
	}
	return nil
}

// Accept booking request request
type AcceptBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// assign lodging unit id
	PetToLodgings []*v1.PetToLodgingDef `protobuf:"bytes,2,rep,name=pet_to_lodgings,json=petToLodgings,proto3" json:"pet_to_lodgings,omitempty"`
	// assign staff
	PetToStaffs []*v1.PetToStaffDef `protobuf:"bytes,3,rep,name=pet_to_staffs,json=petToStaffs,proto3" json:"pet_to_staffs,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// assign service
	PetToServices []*v1.PetToServiceDef `protobuf:"bytes,7,rep,name=pet_to_services,json=petToServices,proto3" json:"pet_to_services,omitempty"`
	// assign evaluation staff
	EvaluationPetToStaffs []*v1.PetToStaffDef `protobuf:"bytes,9,rep,name=evaluation_pet_to_staffs,json=evaluationPetToStaffs,proto3" json:"evaluation_pet_to_staffs,omitempty"`
}

func (x *AcceptBookingRequestRequest) Reset() {
	*x = AcceptBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestRequest) ProtoMessage() {}

func (x *AcceptBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{20}
}

func (x *AcceptBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestRequest) GetPetToLodgings() []*v1.PetToLodgingDef {
	if x != nil {
		return x.PetToLodgings
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetPetToStaffs() []*v1.PetToStaffDef {
	if x != nil {
		return x.PetToStaffs
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AcceptBookingRequestRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AcceptBookingRequestRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestRequest) GetPetToServices() []*v1.PetToServiceDef {
	if x != nil {
		return x.PetToServices
	}
	return nil
}

func (x *AcceptBookingRequestRequest) GetEvaluationPetToStaffs() []*v1.PetToStaffDef {
	if x != nil {
		return x.EvaluationPetToStaffs
	}
	return nil
}

// Accept booking request response
type AcceptBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// need to send notification appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *AcceptBookingRequestResponse) Reset() {
	*x = AcceptBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestResponse) ProtoMessage() {}

func (x *AcceptBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{21}
}

func (x *AcceptBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *AcceptBookingRequestResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Decline booking request request
type DeclineBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *DeclineBookingRequestRequest) Reset() {
	*x = DeclineBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineBookingRequestRequest) ProtoMessage() {}

func (x *DeclineBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*DeclineBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{22}
}

func (x *DeclineBookingRequestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeclineBookingRequestRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeclineBookingRequestRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeclineBookingRequestRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// Decline booking request response
type DeclineBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// need to send notification appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *DeclineBookingRequestResponse) Reset() {
	*x = DeclineBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineBookingRequestResponse) ProtoMessage() {}

func (x *DeclineBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*DeclineBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeclineBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *DeclineBookingRequestResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Count booking requests request
type CountBookingRequestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v12.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// filters
	Filters *CountBookingRequestsRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3,oneof" json:"filters,omitempty"`
}

func (x *CountBookingRequestsRequest) Reset() {
	*x = CountBookingRequestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountBookingRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountBookingRequestsRequest) ProtoMessage() {}

func (x *CountBookingRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountBookingRequestsRequest.ProtoReflect.Descriptor instead.
func (*CountBookingRequestsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{24}
}

func (x *CountBookingRequestsRequest) GetTenant() *v12.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *CountBookingRequestsRequest) GetFilters() *CountBookingRequestsRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Count booking requests response
type CountBookingRequestsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// count
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CountBookingRequestsResponse) Reset() {
	*x = CountBookingRequestsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountBookingRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountBookingRequestsResponse) ProtoMessage() {}

func (x *CountBookingRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountBookingRequestsResponse.ProtoReflect.Descriptor instead.
func (*CountBookingRequestsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{25}
}

func (x *CountBookingRequestsResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// ListBookingRequestId request
type ListBookingRequestIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *ListBookingRequestIdRequest) Reset() {
	*x = ListBookingRequestIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingRequestIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingRequestIdRequest) ProtoMessage() {}

func (x *ListBookingRequestIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingRequestIdRequest.ProtoReflect.Descriptor instead.
func (*ListBookingRequestIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListBookingRequestIdRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// ListBookingRequestId response
type ListBookingRequestIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id to booking request id map
	AppointmentIdToBookingRequestId map[int64]int64 `protobuf:"bytes,1,rep,name=appointment_id_to_booking_request_id,json=appointmentIdToBookingRequestId,proto3" json:"appointment_id_to_booking_request_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *ListBookingRequestIdResponse) Reset() {
	*x = ListBookingRequestIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingRequestIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingRequestIdResponse) ProtoMessage() {}

func (x *ListBookingRequestIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingRequestIdResponse.ProtoReflect.Descriptor instead.
func (*ListBookingRequestIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListBookingRequestIdResponse) GetAppointmentIdToBookingRequestId() map[int64]int64 {
	if x != nil {
		return x.AppointmentIdToBookingRequestId
	}
	return nil
}

// Sync BookingRequest from appointment request
type SyncBookingRequestFromAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId []int64 `protobuf:"varint,1,rep,packed,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *SyncBookingRequestFromAppointmentRequest) Reset() {
	*x = SyncBookingRequestFromAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBookingRequestFromAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBookingRequestFromAppointmentRequest) ProtoMessage() {}

func (x *SyncBookingRequestFromAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBookingRequestFromAppointmentRequest.ProtoReflect.Descriptor instead.
func (*SyncBookingRequestFromAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{28}
}

func (x *SyncBookingRequestFromAppointmentRequest) GetAppointmentId() []int64 {
	if x != nil {
		return x.AppointmentId
	}
	return nil
}

// Sync BookingRequest from appointment response
type SyncBookingRequestFromAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncBookingRequestFromAppointmentResponse) Reset() {
	*x = SyncBookingRequestFromAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBookingRequestFromAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBookingRequestFromAppointmentResponse) ProtoMessage() {}

func (x *SyncBookingRequestFromAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBookingRequestFromAppointmentResponse.ProtoReflect.Descriptor instead.
func (*SyncBookingRequestFromAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{29}
}

// TriggerBookingRequestAutoAccepted request
type TriggerBookingRequestAutoAcceptedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *TriggerBookingRequestAutoAcceptedRequest) Reset() {
	*x = TriggerBookingRequestAutoAcceptedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerBookingRequestAutoAcceptedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerBookingRequestAutoAcceptedRequest) ProtoMessage() {}

func (x *TriggerBookingRequestAutoAcceptedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerBookingRequestAutoAcceptedRequest.ProtoReflect.Descriptor instead.
func (*TriggerBookingRequestAutoAcceptedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{30}
}

func (x *TriggerBookingRequestAutoAcceptedRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// TriggerBookingRequestAutoAccepted response
type TriggerBookingRequestAutoAcceptedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the booking request is auto accepted
	IsAutoAccepted bool `protobuf:"varint,1,opt,name=is_auto_accepted,json=isAutoAccepted,proto3" json:"is_auto_accepted,omitempty"`
}

func (x *TriggerBookingRequestAutoAcceptedResponse) Reset() {
	*x = TriggerBookingRequestAutoAcceptedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerBookingRequestAutoAcceptedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerBookingRequestAutoAcceptedResponse) ProtoMessage() {}

func (x *TriggerBookingRequestAutoAcceptedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerBookingRequestAutoAcceptedResponse.ProtoReflect.Descriptor instead.
func (*TriggerBookingRequestAutoAcceptedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{31}
}

func (x *TriggerBookingRequestAutoAcceptedResponse) GetIsAutoAccepted() bool {
	if x != nil {
		return x.IsAutoAccepted
	}
	return false
}

// AcceptBookingRequestV2 request
type AcceptBookingRequestV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// booking request id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// staff id, operator
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// grooming services
	GroomingServices []*AcceptBookingRequestV2Request_GroomingService `protobuf:"bytes,10,rep,name=grooming_services,json=groomingServices,proto3" json:"grooming_services,omitempty"`
	// boarding services
	BoardingServices []*AcceptBookingRequestV2Request_BoardingService `protobuf:"bytes,11,rep,name=boarding_services,json=boardingServices,proto3" json:"boarding_services,omitempty"`
	// daycare services
	DaycareServices []*AcceptBookingRequestV2Request_DaycareService `protobuf:"bytes,12,rep,name=daycare_services,json=daycareServices,proto3" json:"daycare_services,omitempty"`
	// evaluation services
	EvaluationServices []*AcceptBookingRequestV2Request_EvaluationService `protobuf:"bytes,13,rep,name=evaluation_services,json=evaluationServices,proto3" json:"evaluation_services,omitempty"`
	// grooming addons
	GroomingAddons []*AcceptBookingRequestV2Request_GroomingAddon `protobuf:"bytes,14,rep,name=grooming_addons,json=groomingAddons,proto3" json:"grooming_addons,omitempty"`
	// boarding addons
	BoardingAddons []*AcceptBookingRequestV2Request_BoardingAddon `protobuf:"bytes,15,rep,name=boarding_addons,json=boardingAddons,proto3" json:"boarding_addons,omitempty"`
	// daycare addons
	DaycareAddons []*AcceptBookingRequestV2Request_DaycareAddon `protobuf:"bytes,16,rep,name=daycare_addons,json=daycareAddons,proto3" json:"daycare_addons,omitempty"`
	// 新增 evaluation
	CreateEvaluationRequests []*AcceptBookingRequestV2Request_CreateEvaluationRequest `protobuf:"bytes,17,rep,name=create_evaluation_requests,json=createEvaluationRequests,proto3" json:"create_evaluation_requests,omitempty"`
}

func (x *AcceptBookingRequestV2Request) Reset() {
	*x = AcceptBookingRequestV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32}
}

func (x *AcceptBookingRequestV2Request) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request) GetGroomingServices() []*AcceptBookingRequestV2Request_GroomingService {
	if x != nil {
		return x.GroomingServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetBoardingServices() []*AcceptBookingRequestV2Request_BoardingService {
	if x != nil {
		return x.BoardingServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetDaycareServices() []*AcceptBookingRequestV2Request_DaycareService {
	if x != nil {
		return x.DaycareServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetEvaluationServices() []*AcceptBookingRequestV2Request_EvaluationService {
	if x != nil {
		return x.EvaluationServices
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetGroomingAddons() []*AcceptBookingRequestV2Request_GroomingAddon {
	if x != nil {
		return x.GroomingAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetBoardingAddons() []*AcceptBookingRequestV2Request_BoardingAddon {
	if x != nil {
		return x.BoardingAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetDaycareAddons() []*AcceptBookingRequestV2Request_DaycareAddon {
	if x != nil {
		return x.DaycareAddons
	}
	return nil
}

func (x *AcceptBookingRequestV2Request) GetCreateEvaluationRequests() []*AcceptBookingRequestV2Request_CreateEvaluationRequest {
	if x != nil {
		return x.CreateEvaluationRequests
	}
	return nil
}

// AcceptBookingRequestV2 response
type AcceptBookingRequestV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *AcceptBookingRequestV2Response) Reset() {
	*x = AcceptBookingRequestV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Response) ProtoMessage() {}

func (x *AcceptBookingRequestV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Response.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Response) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{33}
}

func (x *AcceptBookingRequestV2Response) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *AcceptBookingRequestV2Response) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// AutoAssign request
type AutoAssignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request id
	BookingRequestId int64 `protobuf:"varint,1,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// company id, optional
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *AutoAssignRequest) Reset() {
	*x = AutoAssignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignRequest) ProtoMessage() {}

func (x *AutoAssignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignRequest.ProtoReflect.Descriptor instead.
func (*AutoAssignRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{34}
}

func (x *AutoAssignRequest) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *AutoAssignRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// AutoAssign response
type AutoAssignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding services
	BoardingServices []*AutoAssignResponse_BoardingService `protobuf:"bytes,1,rep,name=boarding_services,json=boardingServices,proto3" json:"boarding_services,omitempty"`
	// evaluation services
	EvaluationServices []*AutoAssignResponse_EvaluationService `protobuf:"bytes,2,rep,name=evaluation_services,json=evaluationServices,proto3" json:"evaluation_services,omitempty"`
	// daycare services
	DaycareServices []*AutoAssignResponse_DaycareService `protobuf:"bytes,3,rep,name=daycare_services,json=daycareServices,proto3" json:"daycare_services,omitempty"`
}

func (x *AutoAssignResponse) Reset() {
	*x = AutoAssignResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse) ProtoMessage() {}

func (x *AutoAssignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{35}
}

func (x *AutoAssignResponse) GetBoardingServices() []*AutoAssignResponse_BoardingService {
	if x != nil {
		return x.BoardingServices
	}
	return nil
}

func (x *AutoAssignResponse) GetEvaluationServices() []*AutoAssignResponse_EvaluationService {
	if x != nil {
		return x.EvaluationServices
	}
	return nil
}

func (x *AutoAssignResponse) GetDaycareServices() []*AutoAssignResponse_DaycareService {
	if x != nil {
		return x.DaycareServices
	}
	return nil
}

// CreateBoardingServiceWaitlist request
type CreateBoardingServiceWaitlistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *CreateBoardingServiceWaitlistRequest) Reset() {
	*x = CreateBoardingServiceWaitlistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBoardingServiceWaitlistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBoardingServiceWaitlistRequest) ProtoMessage() {}

func (x *CreateBoardingServiceWaitlistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBoardingServiceWaitlistRequest.ProtoReflect.Descriptor instead.
func (*CreateBoardingServiceWaitlistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{36}
}

func (x *CreateBoardingServiceWaitlistRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CreateBoardingServiceWaitlistRequest) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// CreateDaycareServiceWaitlist request
type CreateDaycareServiceWaitlistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specific dates
	SpecificDates []*date.Date `protobuf:"bytes,1,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
}

func (x *CreateDaycareServiceWaitlistRequest) Reset() {
	*x = CreateDaycareServiceWaitlistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDaycareServiceWaitlistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDaycareServiceWaitlistRequest) ProtoMessage() {}

func (x *CreateDaycareServiceWaitlistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDaycareServiceWaitlistRequest.ProtoReflect.Descriptor instead.
func (*CreateDaycareServiceWaitlistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{37}
}

func (x *CreateDaycareServiceWaitlistRequest) GetSpecificDates() []*date.Date {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// UpdateBoardingServiceWaitlist request
type UpdateBoardingServiceWaitlistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *UpdateBoardingServiceWaitlistRequest) Reset() {
	*x = UpdateBoardingServiceWaitlistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingServiceWaitlistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingServiceWaitlistRequest) ProtoMessage() {}

func (x *UpdateBoardingServiceWaitlistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingServiceWaitlistRequest.ProtoReflect.Descriptor instead.
func (*UpdateBoardingServiceWaitlistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateBoardingServiceWaitlistRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *UpdateBoardingServiceWaitlistRequest) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// UpdateDaycareServiceWaitlist request
type UpdateDaycareServiceWaitlistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specific dates
	SpecificDates *v2.DateList `protobuf:"bytes,1,opt,name=specific_dates,json=specificDates,proto3,oneof" json:"specific_dates,omitempty"`
}

func (x *UpdateDaycareServiceWaitlistRequest) Reset() {
	*x = UpdateDaycareServiceWaitlistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDaycareServiceWaitlistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDaycareServiceWaitlistRequest) ProtoMessage() {}

func (x *UpdateDaycareServiceWaitlistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDaycareServiceWaitlistRequest.ProtoReflect.Descriptor instead.
func (*UpdateDaycareServiceWaitlistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateDaycareServiceWaitlistRequest) GetSpecificDates() *v2.DateList {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// check waitlist available request
type CheckWaitlistAvailableTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckWaitlistAvailableTaskRequest) Reset() {
	*x = CheckWaitlistAvailableTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckWaitlistAvailableTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWaitlistAvailableTaskRequest) ProtoMessage() {}

func (x *CheckWaitlistAvailableTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWaitlistAvailableTaskRequest.ProtoReflect.Descriptor instead.
func (*CheckWaitlistAvailableTaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{40}
}

// check waitlist available Response
type CheckWaitlistAvailableTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckWaitlistAvailableTaskResponse) Reset() {
	*x = CheckWaitlistAvailableTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckWaitlistAvailableTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWaitlistAvailableTaskResponse) ProtoMessage() {}

func (x *CheckWaitlistAvailableTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWaitlistAvailableTaskResponse.ProtoReflect.Descriptor instead.
func (*CheckWaitlistAvailableTaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{41}
}

// MoveBookingRequestToWaitlist request
type MoveBookingRequestToWaitlistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
}

func (x *MoveBookingRequestToWaitlistRequest) Reset() {
	*x = MoveBookingRequestToWaitlistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveBookingRequestToWaitlistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveBookingRequestToWaitlistRequest) ProtoMessage() {}

func (x *MoveBookingRequestToWaitlistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveBookingRequestToWaitlistRequest.ProtoReflect.Descriptor instead.
func (*MoveBookingRequestToWaitlistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{42}
}

func (x *MoveBookingRequestToWaitlistRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *MoveBookingRequestToWaitlistRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *MoveBookingRequestToWaitlistRequest) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

// MoveBookingRequestToWaitlist response
type MoveBookingRequestToWaitlistResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MoveBookingRequestToWaitlistResponse) Reset() {
	*x = MoveBookingRequestToWaitlistResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveBookingRequestToWaitlistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveBookingRequestToWaitlistResponse) ProtoMessage() {}

func (x *MoveBookingRequestToWaitlistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveBookingRequestToWaitlistResponse.ProtoReflect.Descriptor instead.
func (*MoveBookingRequestToWaitlistResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{43}
}

// The request to preview pricing of booking request
type PreviewBookingRequestPricingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// exising customer id, optional
	CustomerId *int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// selected pet and services
	PetServices []*v1.PetServiceDetails `protobuf:"bytes,4,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *PreviewBookingRequestPricingRequest) Reset() {
	*x = PreviewBookingRequestPricingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewBookingRequestPricingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewBookingRequestPricingRequest) ProtoMessage() {}

func (x *PreviewBookingRequestPricingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewBookingRequestPricingRequest.ProtoReflect.Descriptor instead.
func (*PreviewBookingRequestPricingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{44}
}

func (x *PreviewBookingRequestPricingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PreviewBookingRequestPricingRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewBookingRequestPricingRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *PreviewBookingRequestPricingRequest) GetPetServices() []*v1.PetServiceDetails {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// The response of preview pricing of booking request
type PreviewBookingRequestPricingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Line items of the booking request
	LineItems []*PreviewBookingRequestPricingResponse_LineItem `protobuf:"bytes,1,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
}

func (x *PreviewBookingRequestPricingResponse) Reset() {
	*x = PreviewBookingRequestPricingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewBookingRequestPricingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewBookingRequestPricingResponse) ProtoMessage() {}

func (x *PreviewBookingRequestPricingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewBookingRequestPricingResponse.ProtoReflect.Descriptor instead.
func (*PreviewBookingRequestPricingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{45}
}

func (x *PreviewBookingRequestPricingResponse) GetLineItems() []*PreviewBookingRequestPricingResponse_LineItem {
	if x != nil {
		return x.LineItems
	}
	return nil
}

// count booking request by filter
type CountBookingRequestByFilterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// evaluation_ids
	EvaluationIds []int64 `protobuf:"varint,2,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
}

func (x *CountBookingRequestByFilterRequest) Reset() {
	*x = CountBookingRequestByFilterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountBookingRequestByFilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountBookingRequestByFilterRequest) ProtoMessage() {}

func (x *CountBookingRequestByFilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountBookingRequestByFilterRequest.ProtoReflect.Descriptor instead.
func (*CountBookingRequestByFilterRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{46}
}

func (x *CountBookingRequestByFilterRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CountBookingRequestByFilterRequest) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

// check service in use response
type CountBookingRequestByFilterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation in use
	EvaluationInUse map[int64]int32 `protobuf:"bytes,1,rep,name=evaluation_in_use,json=evaluationInUse,proto3" json:"evaluation_in_use,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *CountBookingRequestByFilterResponse) Reset() {
	*x = CountBookingRequestByFilterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountBookingRequestByFilterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountBookingRequestByFilterResponse) ProtoMessage() {}

func (x *CountBookingRequestByFilterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountBookingRequestByFilterResponse.ProtoReflect.Descriptor instead.
func (*CountBookingRequestByFilterResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{47}
}

func (x *CountBookingRequestByFilterResponse) GetEvaluationInUse() map[int64]int32 {
	if x != nil {
		return x.EvaluationInUse
	}
	return nil
}

// Service
type CreateBookingRequestRequest_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	//
	// Types that are assignable to Service:
	//
	//	*CreateBookingRequestRequest_Service_Grooming
	//	*CreateBookingRequestRequest_Service_Boarding
	//	*CreateBookingRequestRequest_Service_Daycare
	//	*CreateBookingRequestRequest_Service_Evaluation
	//	*CreateBookingRequestRequest_Service_DogWalking
	//	*CreateBookingRequestRequest_Service_GroupClass
	Service isCreateBookingRequestRequest_Service_Service `protobuf_oneof:"service"`
}

func (x *CreateBookingRequestRequest_Service) Reset() {
	*x = CreateBookingRequestRequest_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_Service) ProtoMessage() {}

func (x *CreateBookingRequestRequest_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_Service.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_Service) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 0}
}

func (m *CreateBookingRequestRequest_Service) GetService() isCreateBookingRequestRequest_Service_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetGrooming() *CreateBookingRequestRequest_GroomingService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_Grooming); ok {
		return x.Grooming
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetBoarding() *CreateBookingRequestRequest_BoardingService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetDaycare() *CreateBookingRequestRequest_DaycareService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_Daycare); ok {
		return x.Daycare
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetEvaluation() *CreateBookingRequestRequest_EvaluationService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_Evaluation); ok {
		return x.Evaluation
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetDogWalking() *CreateBookingRequestRequest_DogWalkingService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_DogWalking); ok {
		return x.DogWalking
	}
	return nil
}

func (x *CreateBookingRequestRequest_Service) GetGroupClass() *CreateBookingRequestRequest_GroupClassService {
	if x, ok := x.GetService().(*CreateBookingRequestRequest_Service_GroupClass); ok {
		return x.GroupClass
	}
	return nil
}

type isCreateBookingRequestRequest_Service_Service interface {
	isCreateBookingRequestRequest_Service_Service()
}

type CreateBookingRequestRequest_Service_Grooming struct {
	// Grooming
	Grooming *CreateBookingRequestRequest_GroomingService `protobuf:"bytes,1,opt,name=grooming,proto3,oneof"`
}

type CreateBookingRequestRequest_Service_Boarding struct {
	// Boarding
	Boarding *CreateBookingRequestRequest_BoardingService `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type CreateBookingRequestRequest_Service_Daycare struct {
	// Daycare
	Daycare *CreateBookingRequestRequest_DaycareService `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"`
}

type CreateBookingRequestRequest_Service_Evaluation struct {
	// Evaluation
	Evaluation *CreateBookingRequestRequest_EvaluationService `protobuf:"bytes,4,opt,name=evaluation,proto3,oneof"`
}

type CreateBookingRequestRequest_Service_DogWalking struct {
	// Dog walking
	DogWalking *CreateBookingRequestRequest_DogWalkingService `protobuf:"bytes,5,opt,name=dog_walking,json=dogWalking,proto3,oneof"`
}

type CreateBookingRequestRequest_Service_GroupClass struct {
	// Group class
	GroupClass *CreateBookingRequestRequest_GroupClassService `protobuf:"bytes,6,opt,name=group_class,json=groupClass,proto3,oneof"`
}

func (*CreateBookingRequestRequest_Service_Grooming) isCreateBookingRequestRequest_Service_Service() {
}

func (*CreateBookingRequestRequest_Service_Boarding) isCreateBookingRequestRequest_Service_Service() {
}

func (*CreateBookingRequestRequest_Service_Daycare) isCreateBookingRequestRequest_Service_Service() {}

func (*CreateBookingRequestRequest_Service_Evaluation) isCreateBookingRequestRequest_Service_Service() {
}

func (*CreateBookingRequestRequest_Service_DogWalking) isCreateBookingRequestRequest_Service_Service() {
}

func (*CreateBookingRequestRequest_Service_GroupClass) isCreateBookingRequestRequest_Service_Service() {
}

// Grooming service
type CreateBookingRequestRequest_GroomingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Grooming service detail
	Service *CreateGroomingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Grooming service addons
	// 参数类型用错了，使用 addons_v2 替换
	//
	// Deprecated: Do not use.
	Addons []*CreateGroomingServiceDetailRequest `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// Grooming service addons
	AddonsV2 []*CreateGroomingAddOnDetailRequest `protobuf:"bytes,3,rep,name=addons_v2,json=addonsV2,proto3" json:"addons_v2,omitempty"`
}

func (x *CreateBookingRequestRequest_GroomingService) Reset() {
	*x = CreateBookingRequestRequest_GroomingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_GroomingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_GroomingService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_GroomingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_GroomingService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_GroomingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CreateBookingRequestRequest_GroomingService) GetService() *CreateGroomingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBookingRequestRequest_GroomingService) GetAddons() []*CreateGroomingServiceDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *CreateBookingRequestRequest_GroomingService) GetAddonsV2() []*CreateGroomingAddOnDetailRequest {
	if x != nil {
		return x.AddonsV2
	}
	return nil
}

// Boarding service
type CreateBookingRequestRequest_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service detail
	Service *CreateBoardingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Boarding service addons
	Addons []*CreateBoardingAddOnDetailRequest `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *CreateFeedingRequest `protobuf:"bytes,3,opt,name=feeding,proto3,oneof" json:"feeding,omitempty"`
	// Medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *CreateMedicationRequest `protobuf:"bytes,4,opt,name=medication,proto3,oneof" json:"medication,omitempty"`
	// Feedings
	Feedings []*CreateFeedingRequest `protobuf:"bytes,5,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*CreateMedicationRequest `protobuf:"bytes,6,rep,name=medications,proto3" json:"medications,omitempty"`
	// Boarding waitlist
	Waitlist *CreateBoardingServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *CreateBookingRequestRequest_BoardingService) Reset() {
	*x = CreateBookingRequestRequest_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_BoardingService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_BoardingService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 2}
}

func (x *CreateBookingRequestRequest_BoardingService) GetService() *CreateBoardingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CreateBookingRequestRequest_BoardingService) GetAddons() []*CreateBoardingAddOnDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBookingRequestRequest_BoardingService) GetFeeding() *CreateFeedingRequest {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBookingRequestRequest_BoardingService) GetMedication() *CreateMedicationRequest {
	if x != nil {
		return x.Medication
	}
	return nil
}

func (x *CreateBookingRequestRequest_BoardingService) GetFeedings() []*CreateFeedingRequest {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *CreateBookingRequestRequest_BoardingService) GetMedications() []*CreateMedicationRequest {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *CreateBookingRequestRequest_BoardingService) GetWaitlist() *CreateBoardingServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Daycare service
type CreateBookingRequestRequest_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare service detail
	Service *CreateDaycareServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Daycare service addons
	Addons []*CreateDaycareAddOnDetailRequest `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *CreateFeedingRequest `protobuf:"bytes,3,opt,name=feeding,proto3,oneof" json:"feeding,omitempty"`
	// Medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *CreateMedicationRequest `protobuf:"bytes,4,opt,name=medication,proto3,oneof" json:"medication,omitempty"`
	// Feedings
	Feedings []*CreateFeedingRequest `protobuf:"bytes,5,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*CreateMedicationRequest `protobuf:"bytes,6,rep,name=medications,proto3" json:"medications,omitempty"`
	// Daycare waitlist
	Waitlist *CreateDaycareServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *CreateBookingRequestRequest_DaycareService) Reset() {
	*x = CreateBookingRequestRequest_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_DaycareService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_DaycareService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 3}
}

func (x *CreateBookingRequestRequest_DaycareService) GetService() *CreateDaycareServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *CreateBookingRequestRequest_DaycareService) GetAddons() []*CreateDaycareAddOnDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBookingRequestRequest_DaycareService) GetFeeding() *CreateFeedingRequest {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBookingRequestRequest_DaycareService) GetMedication() *CreateMedicationRequest {
	if x != nil {
		return x.Medication
	}
	return nil
}

func (x *CreateBookingRequestRequest_DaycareService) GetFeedings() []*CreateFeedingRequest {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *CreateBookingRequestRequest_DaycareService) GetMedications() []*CreateMedicationRequest {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *CreateBookingRequestRequest_DaycareService) GetWaitlist() *CreateDaycareServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Evaluation service
type CreateBookingRequestRequest_EvaluationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Evaluation service detail
	Service *CreateEvaluationTestDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateBookingRequestRequest_EvaluationService) Reset() {
	*x = CreateBookingRequestRequest_EvaluationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_EvaluationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_EvaluationService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_EvaluationService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_EvaluationService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_EvaluationService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 4}
}

func (x *CreateBookingRequestRequest_EvaluationService) GetService() *CreateEvaluationTestDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

// Dog walking service
type CreateBookingRequestRequest_DogWalkingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dog walking service detail
	Service *CreateDogWalkingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateBookingRequestRequest_DogWalkingService) Reset() {
	*x = CreateBookingRequestRequest_DogWalkingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_DogWalkingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_DogWalkingService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_DogWalkingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_DogWalkingService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_DogWalkingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 5}
}

func (x *CreateBookingRequestRequest_DogWalkingService) GetService() *CreateDogWalkingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

// Group class service
type CreateBookingRequestRequest_GroupClassService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group class service detail
	Service *CreateGroupClassServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateBookingRequestRequest_GroupClassService) Reset() {
	*x = CreateBookingRequestRequest_GroupClassService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_GroupClassService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_GroupClassService) ProtoMessage() {}

func (x *CreateBookingRequestRequest_GroupClassService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_GroupClassService.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_GroupClassService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 6}
}

func (x *CreateBookingRequestRequest_GroupClassService) GetService() *CreateGroupClassServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

// Membership apply Info
type CreateBookingRequestRequest_Membership struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership id
	MembershipIds []int64 `protobuf:"varint,1,rep,packed,name=membership_ids,json=membershipIds,proto3" json:"membership_ids,omitempty"`
}

func (x *CreateBookingRequestRequest_Membership) Reset() {
	*x = CreateBookingRequestRequest_Membership{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest_Membership) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest_Membership) ProtoMessage() {}

func (x *CreateBookingRequestRequest_Membership) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest_Membership.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest_Membership) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{0, 7}
}

func (x *CreateBookingRequestRequest_Membership) GetMembershipIds() []int64 {
	if x != nil {
		return x.MembershipIds
	}
	return nil
}

// Service
type UpdateBookingRequestRequest_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	//
	// Types that are assignable to Service:
	//
	//	*UpdateBookingRequestRequest_Service_Grooming
	//	*UpdateBookingRequestRequest_Service_Boarding
	//	*UpdateBookingRequestRequest_Service_Daycare
	Service isUpdateBookingRequestRequest_Service_Service `protobuf_oneof:"service"`
}

func (x *UpdateBookingRequestRequest_Service) Reset() {
	*x = UpdateBookingRequestRequest_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_Service) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_Service.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_Service) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 0}
}

func (m *UpdateBookingRequestRequest_Service) GetService() isUpdateBookingRequestRequest_Service_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *UpdateBookingRequestRequest_Service) GetGrooming() *UpdateBookingRequestRequest_GroomingService {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_Service_Grooming); ok {
		return x.Grooming
	}
	return nil
}

func (x *UpdateBookingRequestRequest_Service) GetBoarding() *UpdateBookingRequestRequest_BoardingService {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_Service_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *UpdateBookingRequestRequest_Service) GetDaycare() *UpdateBookingRequestRequest_DaycareService {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_Service_Daycare); ok {
		return x.Daycare
	}
	return nil
}

type isUpdateBookingRequestRequest_Service_Service interface {
	isUpdateBookingRequestRequest_Service_Service()
}

type UpdateBookingRequestRequest_Service_Grooming struct {
	// Grooming
	Grooming *UpdateBookingRequestRequest_GroomingService `protobuf:"bytes,1,opt,name=grooming,proto3,oneof"`
}

type UpdateBookingRequestRequest_Service_Boarding struct {
	// Boarding
	Boarding *UpdateBookingRequestRequest_BoardingService `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type UpdateBookingRequestRequest_Service_Daycare struct {
	// Daycare
	Daycare *UpdateBookingRequestRequest_DaycareService `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"` // TODO add other service item types
}

func (*UpdateBookingRequestRequest_Service_Grooming) isUpdateBookingRequestRequest_Service_Service() {
}

func (*UpdateBookingRequestRequest_Service_Boarding) isUpdateBookingRequestRequest_Service_Service() {
}

func (*UpdateBookingRequestRequest_Service_Daycare) isUpdateBookingRequestRequest_Service_Service() {}

// Service detail
type UpdateBookingRequestRequest_ServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action
	//
	// Types that are assignable to Action:
	//
	//	*UpdateBookingRequestRequest_ServiceDetail_Add
	//	*UpdateBookingRequestRequest_ServiceDetail_Update
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_
	Action isUpdateBookingRequestRequest_ServiceDetail_Action `protobuf_oneof:"action"`
}

func (x *UpdateBookingRequestRequest_ServiceDetail) Reset() {
	*x = UpdateBookingRequestRequest_ServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_ServiceDetail) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_ServiceDetail.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_ServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 1}
}

func (m *UpdateBookingRequestRequest_ServiceDetail) GetAction() isUpdateBookingRequestRequest_ServiceDetail_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UpdateBookingRequestRequest_ServiceDetail) GetAdd() *CreateBookingRequestRequest_Service {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_ServiceDetail_Add); ok {
		return x.Add
	}
	return nil
}

func (x *UpdateBookingRequestRequest_ServiceDetail) GetUpdate() *UpdateBookingRequestRequest_Service {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_ServiceDetail_Update); ok {
		return x.Update
	}
	return nil
}

func (x *UpdateBookingRequestRequest_ServiceDetail) GetDelete() *UpdateBookingRequestRequest_ServiceDetail_Delete {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_ServiceDetail_Delete_); ok {
		return x.Delete
	}
	return nil
}

type isUpdateBookingRequestRequest_ServiceDetail_Action interface {
	isUpdateBookingRequestRequest_ServiceDetail_Action()
}

type UpdateBookingRequestRequest_ServiceDetail_Add struct {
	// add service
	Add *CreateBookingRequestRequest_Service `protobuf:"bytes,1,opt,name=add,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Update struct {
	// update service
	Update *UpdateBookingRequestRequest_Service `protobuf:"bytes,2,opt,name=update,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_ struct {
	// delete service
	Delete *UpdateBookingRequestRequest_ServiceDetail_Delete `protobuf:"bytes,3,opt,name=delete,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_ServiceDetail_Add) isUpdateBookingRequestRequest_ServiceDetail_Action() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Update) isUpdateBookingRequestRequest_ServiceDetail_Action() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_) isUpdateBookingRequestRequest_ServiceDetail_Action() {
}

// Grooming service
type UpdateBookingRequestRequest_GroomingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Grooming service detail
	Service *UpdateGroomingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Grooming service addons
	// deprecated by Freeman since 2025/6/30, use addons_v2 instead
	//
	// Deprecated: Do not use.
	Addons []*UpdateGroomingAddOnDetailRequest `protobuf:"bytes,2,rep,name=addons,proto3" json:"addons,omitempty"`
	// Grooming service addons operation: add, update, delete
	AddonsV2 []*UpdateBookingRequestRequest_GroomingService_Addon `protobuf:"bytes,4,rep,name=addons_v2,json=addonsV2,proto3" json:"addons_v2,omitempty"`
	// Grooming auto assign detail
	AutoAssign *UpsertGroomingAutoAssignRequest `protobuf:"bytes,3,opt,name=auto_assign,json=autoAssign,proto3,oneof" json:"auto_assign,omitempty"`
}

func (x *UpdateBookingRequestRequest_GroomingService) Reset() {
	*x = UpdateBookingRequestRequest_GroomingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_GroomingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_GroomingService) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_GroomingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_GroomingService.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_GroomingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 2}
}

func (x *UpdateBookingRequestRequest_GroomingService) GetService() *UpdateGroomingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

// Deprecated: Do not use.
func (x *UpdateBookingRequestRequest_GroomingService) GetAddons() []*UpdateGroomingAddOnDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *UpdateBookingRequestRequest_GroomingService) GetAddonsV2() []*UpdateBookingRequestRequest_GroomingService_Addon {
	if x != nil {
		return x.AddonsV2
	}
	return nil
}

func (x *UpdateBookingRequestRequest_GroomingService) GetAutoAssign() *UpsertGroomingAutoAssignRequest {
	if x != nil {
		return x.AutoAssign
	}
	return nil
}

// Boarding service
type UpdateBookingRequestRequest_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service detail
	Service *UpdateBoardingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Feedings
	FeedingsUpsert *CreateFeedingRequestList `protobuf:"bytes,2,opt,name=feedings_upsert,json=feedingsUpsert,proto3,oneof" json:"feedings_upsert,omitempty"`
	// Medications
	MedicationsUpsert *CreateMedicationRequestList `protobuf:"bytes,3,opt,name=medications_upsert,json=medicationsUpsert,proto3,oneof" json:"medications_upsert,omitempty"`
	// Boarding service addons
	// deprecated by Freeman since 2025/6/26, use addons_v2 instead
	//
	// Deprecated: Do not use.
	Addons []*UpdateBoardingAddOnDetailRequest `protobuf:"bytes,4,rep,name=addons,proto3" json:"addons,omitempty"`
	// Boarding service addons operation: add, update, delete
	AddonsV2 []*UpdateBookingRequestRequest_BoardingService_Addon `protobuf:"bytes,5,rep,name=addons_v2,json=addonsV2,proto3" json:"addons_v2,omitempty"`
	// Boarding waitlist
	Waitlist *UpdateBoardingServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *UpdateBookingRequestRequest_BoardingService) Reset() {
	*x = UpdateBookingRequestRequest_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_BoardingService) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_BoardingService.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 3}
}

func (x *UpdateBookingRequestRequest_BoardingService) GetService() *UpdateBoardingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService) GetFeedingsUpsert() *CreateFeedingRequestList {
	if x != nil {
		return x.FeedingsUpsert
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService) GetMedicationsUpsert() *CreateMedicationRequestList {
	if x != nil {
		return x.MedicationsUpsert
	}
	return nil
}

// Deprecated: Do not use.
func (x *UpdateBookingRequestRequest_BoardingService) GetAddons() []*UpdateBoardingAddOnDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService) GetAddonsV2() []*UpdateBookingRequestRequest_BoardingService_Addon {
	if x != nil {
		return x.AddonsV2
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService) GetWaitlist() *UpdateBoardingServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Daycare service
type UpdateBookingRequestRequest_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare service detail
	Service *UpdateDaycareServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Feedings
	FeedingsUpsert *CreateFeedingRequestList `protobuf:"bytes,2,opt,name=feedings_upsert,json=feedingsUpsert,proto3,oneof" json:"feedings_upsert,omitempty"`
	// Medications
	MedicationsUpsert *CreateMedicationRequestList `protobuf:"bytes,3,opt,name=medications_upsert,json=medicationsUpsert,proto3,oneof" json:"medications_upsert,omitempty"`
	// Daycare service addons
	// deprecated by Freeman since 2025/6/26, use addons_v2 instead
	//
	// Deprecated: Do not use.
	Addons []*UpdateDaycareAddOnDetailRequest `protobuf:"bytes,4,rep,name=addons,proto3" json:"addons,omitempty"`
	// Daycare service addons operation: add, update, delete
	AddonsV2 []*UpdateBookingRequestRequest_DaycareService_Addon `protobuf:"bytes,5,rep,name=addons_v2,json=addonsV2,proto3" json:"addons_v2,omitempty"`
	// Daycare waitlist
	Waitlist *UpdateDaycareServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *UpdateBookingRequestRequest_DaycareService) Reset() {
	*x = UpdateBookingRequestRequest_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_DaycareService) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_DaycareService.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 4}
}

func (x *UpdateBookingRequestRequest_DaycareService) GetService() *UpdateDaycareServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService) GetFeedingsUpsert() *CreateFeedingRequestList {
	if x != nil {
		return x.FeedingsUpsert
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService) GetMedicationsUpsert() *CreateMedicationRequestList {
	if x != nil {
		return x.MedicationsUpsert
	}
	return nil
}

// Deprecated: Do not use.
func (x *UpdateBookingRequestRequest_DaycareService) GetAddons() []*UpdateDaycareAddOnDetailRequest {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService) GetAddonsV2() []*UpdateBookingRequestRequest_DaycareService_Addon {
	if x != nil {
		return x.AddonsV2
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService) GetWaitlist() *UpdateDaycareServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Delete service
type UpdateBookingRequestRequest_ServiceDetail_Delete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	//
	// Types that are assignable to Service:
	//
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_Grooming
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_Boarding
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_Daycare
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_Evaluation
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_DogWalking
	//	*UpdateBookingRequestRequest_ServiceDetail_Delete_GroupClass
	Service isUpdateBookingRequestRequest_ServiceDetail_Delete_Service `protobuf_oneof:"service"`
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) Reset() {
	*x = UpdateBookingRequestRequest_ServiceDetail_Delete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_ServiceDetail_Delete.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_ServiceDetail_Delete) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 1, 0}
}

func (m *UpdateBookingRequestRequest_ServiceDetail_Delete) GetService() isUpdateBookingRequestRequest_ServiceDetail_Delete_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetGrooming() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_Grooming); ok {
		return x.Grooming
	}
	return 0
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetBoarding() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_Boarding); ok {
		return x.Boarding
	}
	return 0
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetDaycare() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_Daycare); ok {
		return x.Daycare
	}
	return 0
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetEvaluation() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_Evaluation); ok {
		return x.Evaluation
	}
	return 0
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetDogWalking() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_DogWalking); ok {
		return x.DogWalking
	}
	return 0
}

func (x *UpdateBookingRequestRequest_ServiceDetail_Delete) GetGroupClass() int64 {
	if x, ok := x.GetService().(*UpdateBookingRequestRequest_ServiceDetail_Delete_GroupClass); ok {
		return x.GroupClass
	}
	return 0
}

type isUpdateBookingRequestRequest_ServiceDetail_Delete_Service interface {
	isUpdateBookingRequestRequest_ServiceDetail_Delete_Service()
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_Grooming struct {
	// grooming_service_detail id
	Grooming int64 `protobuf:"varint,1,opt,name=grooming,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_Boarding struct {
	// boarding_service_detail id
	Boarding int64 `protobuf:"varint,2,opt,name=boarding,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_Daycare struct {
	// daycare_service_detail id
	Daycare int64 `protobuf:"varint,3,opt,name=daycare,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_Evaluation struct {
	// evaluation_test_detail id
	Evaluation int64 `protobuf:"varint,4,opt,name=evaluation,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_DogWalking struct {
	// dog_walking_service_detail id
	DogWalking int64 `protobuf:"varint,5,opt,name=dog_walking,json=dogWalking,proto3,oneof"`
}

type UpdateBookingRequestRequest_ServiceDetail_Delete_GroupClass struct {
	// group_class_service_detail id
	GroupClass int64 `protobuf:"varint,6,opt,name=group_class,json=groupClass,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_Grooming) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_Boarding) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_Daycare) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_Evaluation) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_DogWalking) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

func (*UpdateBookingRequestRequest_ServiceDetail_Delete_GroupClass) isUpdateBookingRequestRequest_ServiceDetail_Delete_Service() {
}

// Addon
type UpdateBookingRequestRequest_GroomingService_Addon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action
	//
	// Types that are assignable to Action:
	//
	//	*UpdateBookingRequestRequest_GroomingService_Addon_Add_
	//	*UpdateBookingRequestRequest_GroomingService_Addon_Update_
	//	*UpdateBookingRequestRequest_GroomingService_Addon_Delete_
	Action isUpdateBookingRequestRequest_GroomingService_Addon_Action `protobuf_oneof:"action"`
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) Reset() {
	*x = UpdateBookingRequestRequest_GroomingService_Addon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_GroomingService_Addon) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_GroomingService_Addon.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_GroomingService_Addon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 2, 0}
}

func (m *UpdateBookingRequestRequest_GroomingService_Addon) GetAction() isUpdateBookingRequestRequest_GroomingService_Addon_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) GetAdd() *UpdateBookingRequestRequest_GroomingService_Addon_Add {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_GroomingService_Addon_Add_); ok {
		return x.Add
	}
	return nil
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) GetUpdate() *UpdateBookingRequestRequest_GroomingService_Addon_Update {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_GroomingService_Addon_Update_); ok {
		return x.Update
	}
	return nil
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon) GetDelete() *UpdateBookingRequestRequest_GroomingService_Addon_Delete {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_GroomingService_Addon_Delete_); ok {
		return x.Delete
	}
	return nil
}

type isUpdateBookingRequestRequest_GroomingService_Addon_Action interface {
	isUpdateBookingRequestRequest_GroomingService_Addon_Action()
}

type UpdateBookingRequestRequest_GroomingService_Addon_Add_ struct {
	// add
	Add *UpdateBookingRequestRequest_GroomingService_Addon_Add `protobuf:"bytes,1,opt,name=add,proto3,oneof"`
}

type UpdateBookingRequestRequest_GroomingService_Addon_Update_ struct {
	// update
	Update *UpdateBookingRequestRequest_GroomingService_Addon_Update `protobuf:"bytes,2,opt,name=update,proto3,oneof"`
}

type UpdateBookingRequestRequest_GroomingService_Addon_Delete_ struct {
	// delete
	Delete *UpdateBookingRequestRequest_GroomingService_Addon_Delete `protobuf:"bytes,3,opt,name=delete,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Add_) isUpdateBookingRequestRequest_GroomingService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Update_) isUpdateBookingRequestRequest_GroomingService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Delete_) isUpdateBookingRequestRequest_GroomingService_Addon_Action() {
}

// Add
type UpdateBookingRequestRequest_GroomingService_Addon_Add struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *CreateGroomingAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Add) Reset() {
	*x = UpdateBookingRequestRequest_GroomingService_Addon_Add{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Add) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Add) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Add) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_GroomingService_Addon_Add.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_GroomingService_Addon_Add) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 2, 0, 0}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Add) GetAddon() *CreateGroomingAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Update
type UpdateBookingRequestRequest_GroomingService_Addon_Update struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *UpdateGroomingAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Update) Reset() {
	*x = UpdateBookingRequestRequest_GroomingService_Addon_Update{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Update) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Update) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Update) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_GroomingService_Addon_Update.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_GroomingService_Addon_Update) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 2, 0, 1}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Update) GetAddon() *UpdateGroomingAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Delete
type UpdateBookingRequestRequest_GroomingService_Addon_Delete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming addon detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Delete) Reset() {
	*x = UpdateBookingRequestRequest_GroomingService_Addon_Delete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Delete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_GroomingService_Addon_Delete) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Delete) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_GroomingService_Addon_Delete.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_GroomingService_Addon_Delete) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 2, 0, 2}
}

func (x *UpdateBookingRequestRequest_GroomingService_Addon_Delete) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Addon
type UpdateBookingRequestRequest_BoardingService_Addon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action
	//
	// Types that are assignable to Action:
	//
	//	*UpdateBookingRequestRequest_BoardingService_Addon_Add_
	//	*UpdateBookingRequestRequest_BoardingService_Addon_Update_
	//	*UpdateBookingRequestRequest_BoardingService_Addon_Delete_
	Action isUpdateBookingRequestRequest_BoardingService_Addon_Action `protobuf_oneof:"action"`
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) Reset() {
	*x = UpdateBookingRequestRequest_BoardingService_Addon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_BoardingService_Addon) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_BoardingService_Addon.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_BoardingService_Addon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 3, 0}
}

func (m *UpdateBookingRequestRequest_BoardingService_Addon) GetAction() isUpdateBookingRequestRequest_BoardingService_Addon_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) GetAdd() *UpdateBookingRequestRequest_BoardingService_Addon_Add {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_BoardingService_Addon_Add_); ok {
		return x.Add
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) GetUpdate() *UpdateBookingRequestRequest_BoardingService_Addon_Update {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_BoardingService_Addon_Update_); ok {
		return x.Update
	}
	return nil
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon) GetDelete() *UpdateBookingRequestRequest_BoardingService_Addon_Delete {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_BoardingService_Addon_Delete_); ok {
		return x.Delete
	}
	return nil
}

type isUpdateBookingRequestRequest_BoardingService_Addon_Action interface {
	isUpdateBookingRequestRequest_BoardingService_Addon_Action()
}

type UpdateBookingRequestRequest_BoardingService_Addon_Add_ struct {
	// add
	Add *UpdateBookingRequestRequest_BoardingService_Addon_Add `protobuf:"bytes,1,opt,name=add,proto3,oneof"`
}

type UpdateBookingRequestRequest_BoardingService_Addon_Update_ struct {
	// update
	Update *UpdateBookingRequestRequest_BoardingService_Addon_Update `protobuf:"bytes,2,opt,name=update,proto3,oneof"`
}

type UpdateBookingRequestRequest_BoardingService_Addon_Delete_ struct {
	// delete
	Delete *UpdateBookingRequestRequest_BoardingService_Addon_Delete `protobuf:"bytes,3,opt,name=delete,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Add_) isUpdateBookingRequestRequest_BoardingService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Update_) isUpdateBookingRequestRequest_BoardingService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Delete_) isUpdateBookingRequestRequest_BoardingService_Addon_Action() {
}

// Add
type UpdateBookingRequestRequest_BoardingService_Addon_Add struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *CreateBoardingAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Add) Reset() {
	*x = UpdateBookingRequestRequest_BoardingService_Addon_Add{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Add) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Add) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Add) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_BoardingService_Addon_Add.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_BoardingService_Addon_Add) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 3, 0, 0}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Add) GetAddon() *CreateBoardingAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Update
type UpdateBookingRequestRequest_BoardingService_Addon_Update struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *UpdateBoardingAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Update) Reset() {
	*x = UpdateBookingRequestRequest_BoardingService_Addon_Update{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Update) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Update) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Update) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_BoardingService_Addon_Update.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_BoardingService_Addon_Update) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 3, 0, 1}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Update) GetAddon() *UpdateBoardingAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Delete
type UpdateBookingRequestRequest_BoardingService_Addon_Delete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding addon detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Delete) Reset() {
	*x = UpdateBookingRequestRequest_BoardingService_Addon_Delete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Delete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_BoardingService_Addon_Delete) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Delete) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_BoardingService_Addon_Delete.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_BoardingService_Addon_Delete) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 3, 0, 2}
}

func (x *UpdateBookingRequestRequest_BoardingService_Addon_Delete) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Addon
type UpdateBookingRequestRequest_DaycareService_Addon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action
	//
	// Types that are assignable to Action:
	//
	//	*UpdateBookingRequestRequest_DaycareService_Addon_Add_
	//	*UpdateBookingRequestRequest_DaycareService_Addon_Update_
	//	*UpdateBookingRequestRequest_DaycareService_Addon_Delete_
	Action isUpdateBookingRequestRequest_DaycareService_Addon_Action `protobuf_oneof:"action"`
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) Reset() {
	*x = UpdateBookingRequestRequest_DaycareService_Addon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_DaycareService_Addon) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_DaycareService_Addon.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_DaycareService_Addon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 4, 0}
}

func (m *UpdateBookingRequestRequest_DaycareService_Addon) GetAction() isUpdateBookingRequestRequest_DaycareService_Addon_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) GetAdd() *UpdateBookingRequestRequest_DaycareService_Addon_Add {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_DaycareService_Addon_Add_); ok {
		return x.Add
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) GetUpdate() *UpdateBookingRequestRequest_DaycareService_Addon_Update {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_DaycareService_Addon_Update_); ok {
		return x.Update
	}
	return nil
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon) GetDelete() *UpdateBookingRequestRequest_DaycareService_Addon_Delete {
	if x, ok := x.GetAction().(*UpdateBookingRequestRequest_DaycareService_Addon_Delete_); ok {
		return x.Delete
	}
	return nil
}

type isUpdateBookingRequestRequest_DaycareService_Addon_Action interface {
	isUpdateBookingRequestRequest_DaycareService_Addon_Action()
}

type UpdateBookingRequestRequest_DaycareService_Addon_Add_ struct {
	// add
	Add *UpdateBookingRequestRequest_DaycareService_Addon_Add `protobuf:"bytes,1,opt,name=add,proto3,oneof"`
}

type UpdateBookingRequestRequest_DaycareService_Addon_Update_ struct {
	// update
	Update *UpdateBookingRequestRequest_DaycareService_Addon_Update `protobuf:"bytes,2,opt,name=update,proto3,oneof"`
}

type UpdateBookingRequestRequest_DaycareService_Addon_Delete_ struct {
	// delete
	Delete *UpdateBookingRequestRequest_DaycareService_Addon_Delete `protobuf:"bytes,3,opt,name=delete,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Add_) isUpdateBookingRequestRequest_DaycareService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Update_) isUpdateBookingRequestRequest_DaycareService_Addon_Action() {
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Delete_) isUpdateBookingRequestRequest_DaycareService_Addon_Action() {
}

// Add
type UpdateBookingRequestRequest_DaycareService_Addon_Add struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *CreateDaycareAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Add) Reset() {
	*x = UpdateBookingRequestRequest_DaycareService_Addon_Add{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Add) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Add) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Add) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_DaycareService_Addon_Add.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_DaycareService_Addon_Add) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 4, 0, 0}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Add) GetAddon() *CreateDaycareAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Update
type UpdateBookingRequestRequest_DaycareService_Addon_Update struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// addon
	Addon *UpdateDaycareAddOnDetailRequest `protobuf:"bytes,1,opt,name=addon,proto3" json:"addon,omitempty"`
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Update) Reset() {
	*x = UpdateBookingRequestRequest_DaycareService_Addon_Update{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Update) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Update) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Update) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_DaycareService_Addon_Update.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_DaycareService_Addon_Update) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 4, 0, 1}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Update) GetAddon() *UpdateDaycareAddOnDetailRequest {
	if x != nil {
		return x.Addon
	}
	return nil
}

// Delete
type UpdateBookingRequestRequest_DaycareService_Addon_Delete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare addon detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Delete) Reset() {
	*x = UpdateBookingRequestRequest_DaycareService_Addon_Delete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Delete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest_DaycareService_Addon_Delete) ProtoMessage() {}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Delete) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest_DaycareService_Addon_Delete.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest_DaycareService_Addon_Delete) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{14, 4, 0, 2}
}

func (x *UpdateBookingRequestRequest_DaycareService_Addon_Delete) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Service
type ReplaceBookingRequestRequest_Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	//
	// Types that are assignable to Service:
	//
	//	*ReplaceBookingRequestRequest_Service_Boarding
	//	*ReplaceBookingRequestRequest_Service_Daycare
	Service isReplaceBookingRequestRequest_Service_Service `protobuf_oneof:"service"`
}

func (x *ReplaceBookingRequestRequest_Service) Reset() {
	*x = ReplaceBookingRequestRequest_Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReplaceBookingRequestRequest_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplaceBookingRequestRequest_Service) ProtoMessage() {}

func (x *ReplaceBookingRequestRequest_Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplaceBookingRequestRequest_Service.ProtoReflect.Descriptor instead.
func (*ReplaceBookingRequestRequest_Service) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{16, 0}
}

func (m *ReplaceBookingRequestRequest_Service) GetService() isReplaceBookingRequestRequest_Service_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *ReplaceBookingRequestRequest_Service) GetBoarding() *ReplaceBookingRequestRequest_BoardingService {
	if x, ok := x.GetService().(*ReplaceBookingRequestRequest_Service_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *ReplaceBookingRequestRequest_Service) GetDaycare() *ReplaceBookingRequestRequest_DaycareService {
	if x, ok := x.GetService().(*ReplaceBookingRequestRequest_Service_Daycare); ok {
		return x.Daycare
	}
	return nil
}

type isReplaceBookingRequestRequest_Service_Service interface {
	isReplaceBookingRequestRequest_Service_Service()
}

type ReplaceBookingRequestRequest_Service_Boarding struct {
	// Boarding
	Boarding *ReplaceBookingRequestRequest_BoardingService `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type ReplaceBookingRequestRequest_Service_Daycare struct {
	// Daycare
	Daycare *ReplaceBookingRequestRequest_DaycareService `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"` // TODO add other service item types
}

func (*ReplaceBookingRequestRequest_Service_Boarding) isReplaceBookingRequestRequest_Service_Service() {
}

func (*ReplaceBookingRequestRequest_Service_Daycare) isReplaceBookingRequestRequest_Service_Service() {
}

// Boarding service
type ReplaceBookingRequestRequest_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service detail
	Service *CreateBoardingServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Boarding waitlist
	Waitlist *CreateBoardingServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *ReplaceBookingRequestRequest_BoardingService) Reset() {
	*x = ReplaceBookingRequestRequest_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReplaceBookingRequestRequest_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplaceBookingRequestRequest_BoardingService) ProtoMessage() {}

func (x *ReplaceBookingRequestRequest_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplaceBookingRequestRequest_BoardingService.ProtoReflect.Descriptor instead.
func (*ReplaceBookingRequestRequest_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{16, 1}
}

func (x *ReplaceBookingRequestRequest_BoardingService) GetService() *CreateBoardingServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ReplaceBookingRequestRequest_BoardingService) GetWaitlist() *CreateBoardingServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Daycare service
type ReplaceBookingRequestRequest_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare service detail
	Service *CreateDaycareServiceDetailRequest `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Daycare waitlist
	Waitlist *CreateDaycareServiceWaitlistRequest `protobuf:"bytes,7,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *ReplaceBookingRequestRequest_DaycareService) Reset() {
	*x = ReplaceBookingRequestRequest_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReplaceBookingRequestRequest_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplaceBookingRequestRequest_DaycareService) ProtoMessage() {}

func (x *ReplaceBookingRequestRequest_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplaceBookingRequestRequest_DaycareService.ProtoReflect.Descriptor instead.
func (*ReplaceBookingRequestRequest_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{16, 2}
}

func (x *ReplaceBookingRequestRequest_DaycareService) GetService() *CreateDaycareServiceDetailRequest {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ReplaceBookingRequestRequest_DaycareService) GetWaitlist() *CreateDaycareServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// lodging assign info require
type GetAutoAssignResponse_AssignRequire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// start date
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// specific dates
	SpecificDates []string `protobuf:"bytes,5,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
}

func (x *GetAutoAssignResponse_AssignRequire) Reset() {
	*x = GetAutoAssignResponse_AssignRequire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoAssignResponse_AssignRequire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoAssignResponse_AssignRequire) ProtoMessage() {}

func (x *GetAutoAssignResponse_AssignRequire) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoAssignResponse_AssignRequire.ProtoReflect.Descriptor instead.
func (*GetAutoAssignResponse_AssignRequire) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *GetAutoAssignResponse_AssignRequire) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetAutoAssignResponse_AssignRequire) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GetAutoAssignResponse_AssignRequire) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *GetAutoAssignResponse_AssignRequire) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *GetAutoAssignResponse_AssignRequire) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// lodging detail for auto assigned
type GetAutoAssignResponse_LodgingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging id
	LodgingId int64 `protobuf:"varint,1,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,2,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,3,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
}

func (x *GetAutoAssignResponse_LodgingDetail) Reset() {
	*x = GetAutoAssignResponse_LodgingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutoAssignResponse_LodgingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoAssignResponse_LodgingDetail) ProtoMessage() {}

func (x *GetAutoAssignResponse_LodgingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoAssignResponse_LodgingDetail.ProtoReflect.Descriptor instead.
func (*GetAutoAssignResponse_LodgingDetail) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{19, 1}
}

func (x *GetAutoAssignResponse_LodgingDetail) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *GetAutoAssignResponse_LodgingDetail) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *GetAutoAssignResponse_LodgingDetail) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

// filters
type CountBookingRequestsRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created time range
	CreatedTimeRange *interval.Interval `protobuf:"bytes,1,opt,name=created_time_range,json=createdTimeRange,proto3,oneof" json:"created_time_range,omitempty"`
	// statuses, used to filter status, if empty, it means all statuses
	Statuses []v1.BookingRequestStatus `protobuf:"varint,2,rep,packed,name=statuses,proto3,enum=moego.models.online_booking.v1.BookingRequestStatus" json:"statuses,omitempty"`
	// payment statuses
	PaymentStatuses []v1.BookingRequestModel_PaymentStatus `protobuf:"varint,3,rep,packed,name=payment_statuses,json=paymentStatuses,proto3,enum=moego.models.online_booking.v1.BookingRequestModel_PaymentStatus" json:"payment_statuses,omitempty"`
}

func (x *CountBookingRequestsRequest_Filters) Reset() {
	*x = CountBookingRequestsRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountBookingRequestsRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountBookingRequestsRequest_Filters) ProtoMessage() {}

func (x *CountBookingRequestsRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountBookingRequestsRequest_Filters.ProtoReflect.Descriptor instead.
func (*CountBookingRequestsRequest_Filters) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *CountBookingRequestsRequest_Filters) GetCreatedTimeRange() *interval.Interval {
	if x != nil {
		return x.CreatedTimeRange
	}
	return nil
}

func (x *CountBookingRequestsRequest_Filters) GetStatuses() []v1.BookingRequestStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *CountBookingRequestsRequest_Filters) GetPaymentStatuses() []v1.BookingRequestModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatuses
	}
	return nil
}

// Grooming service
type AcceptBookingRequestV2Request_GroomingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// start time
	StartTime *int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
}

func (x *AcceptBookingRequestV2Request_GroomingService) Reset() {
	*x = AcceptBookingRequestV2Request_GroomingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_GroomingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_GroomingService) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_GroomingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_GroomingService.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_GroomingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *AcceptBookingRequestV2Request_GroomingService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_GroomingService) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_GroomingService) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

// Boarding service
type AcceptBookingRequestV2Request_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging unit id
	// 使用 lodging id 是为了和 boarding_service_detail 保持一致
	LodgingId *int64 `protobuf:"varint,4,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
}

func (x *AcceptBookingRequestV2Request_BoardingService) Reset() {
	*x = AcceptBookingRequestV2Request_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_BoardingService) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_BoardingService.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 1}
}

func (x *AcceptBookingRequestV2Request_BoardingService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_BoardingService) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

// Daycare service
type AcceptBookingRequestV2Request_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging unit id
	LodgingId *int64 `protobuf:"varint,4,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
}

func (x *AcceptBookingRequestV2Request_DaycareService) Reset() {
	*x = AcceptBookingRequestV2Request_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_DaycareService) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_DaycareService.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 2}
}

func (x *AcceptBookingRequestV2Request_DaycareService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_DaycareService) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

// Evaluation service
type AcceptBookingRequestV2Request_EvaluationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// evaluation id
	EvaluationId *int64 `protobuf:"varint,3,opt,name=evaluation_id,json=evaluationId,proto3,oneof" json:"evaluation_id,omitempty"`
}

func (x *AcceptBookingRequestV2Request_EvaluationService) Reset() {
	*x = AcceptBookingRequestV2Request_EvaluationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_EvaluationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_EvaluationService) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_EvaluationService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_EvaluationService.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_EvaluationService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 3}
}

func (x *AcceptBookingRequestV2Request_EvaluationService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_EvaluationService) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_EvaluationService) GetEvaluationId() int64 {
	if x != nil && x.EvaluationId != nil {
		return *x.EvaluationId
	}
	return 0
}

// Grooming addon
type AcceptBookingRequestV2Request_GroomingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// start time
	StartTime *int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
}

func (x *AcceptBookingRequestV2Request_GroomingAddon) Reset() {
	*x = AcceptBookingRequestV2Request_GroomingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_GroomingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_GroomingAddon) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_GroomingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_GroomingAddon.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_GroomingAddon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 4}
}

func (x *AcceptBookingRequestV2Request_GroomingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_GroomingAddon) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_GroomingAddon) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

// Boarding addon
type AcceptBookingRequestV2Request_BoardingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// start time
	StartTime *int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
}

func (x *AcceptBookingRequestV2Request_BoardingAddon) Reset() {
	*x = AcceptBookingRequestV2Request_BoardingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_BoardingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_BoardingAddon) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_BoardingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_BoardingAddon.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_BoardingAddon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 5}
}

func (x *AcceptBookingRequestV2Request_BoardingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_BoardingAddon) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_BoardingAddon) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

// Daycare addon
type AcceptBookingRequestV2Request_DaycareAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// start time
	StartTime *int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
}

func (x *AcceptBookingRequestV2Request_DaycareAddon) Reset() {
	*x = AcceptBookingRequestV2Request_DaycareAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_DaycareAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_DaycareAddon) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_DaycareAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_DaycareAddon.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_DaycareAddon) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 6}
}

func (x *AcceptBookingRequestV2Request_DaycareAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_DaycareAddon) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_DaycareAddon) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

// Create evaluation request
type AcceptBookingRequestV2Request_CreateEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation id
	EvaluationId int64 `protobuf:"varint,1,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// start time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// lodging id
	LodgingId *int64 `protobuf:"varint,6,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) Reset() {
	*x = AcceptBookingRequestV2Request_CreateEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequestV2Request_CreateEvaluationRequest) ProtoMessage() {}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequestV2Request_CreateEvaluationRequest.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequestV2Request_CreateEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{32, 7}
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AcceptBookingRequestV2Request_CreateEvaluationRequest) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

// boarding service
type AutoAssignResponse_BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding_service_detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging id
	// 没有返回值表示不能 auto assign 到 lodging
	LodgingId *int64 `protobuf:"varint,2,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	// 这个 pet 选择的 boarding service 所缺失的 evaluation
	MissingEvaluation *v11.EvaluationBriefView `protobuf:"bytes,3,opt,name=missing_evaluation,json=missingEvaluation,proto3,oneof" json:"missing_evaluation,omitempty"`
}

func (x *AutoAssignResponse_BoardingService) Reset() {
	*x = AutoAssignResponse_BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_BoardingService) ProtoMessage() {}

func (x *AutoAssignResponse_BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_BoardingService.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{35, 0}
}

func (x *AutoAssignResponse_BoardingService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutoAssignResponse_BoardingService) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

func (x *AutoAssignResponse_BoardingService) GetMissingEvaluation() *v11.EvaluationBriefView {
	if x != nil {
		return x.MissingEvaluation
	}
	return nil
}

// evaluation service
type AutoAssignResponse_EvaluationService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation_test_detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	// 没有返回值表示不能 auto assign 到 staff
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// 选择的 evaluation 信息
	SelectedEvaluation *v11.EvaluationBriefView `protobuf:"bytes,3,opt,name=selected_evaluation,json=selectedEvaluation,proto3" json:"selected_evaluation,omitempty"`
	// 所有的 evaluation 信息
	AllEvaluations []*v11.EvaluationBriefView `protobuf:"bytes,4,rep,name=all_evaluations,json=allEvaluations,proto3" json:"all_evaluations,omitempty"`
}

func (x *AutoAssignResponse_EvaluationService) Reset() {
	*x = AutoAssignResponse_EvaluationService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_EvaluationService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_EvaluationService) ProtoMessage() {}

func (x *AutoAssignResponse_EvaluationService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_EvaluationService.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_EvaluationService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{35, 1}
}

func (x *AutoAssignResponse_EvaluationService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutoAssignResponse_EvaluationService) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *AutoAssignResponse_EvaluationService) GetSelectedEvaluation() *v11.EvaluationBriefView {
	if x != nil {
		return x.SelectedEvaluation
	}
	return nil
}

func (x *AutoAssignResponse_EvaluationService) GetAllEvaluations() []*v11.EvaluationBriefView {
	if x != nil {
		return x.AllEvaluations
	}
	return nil
}

// daycare service
type AutoAssignResponse_DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare_service_detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 这个 pet 选择的 daycare service 所缺失的 evaluation
	MissingEvaluation *v11.EvaluationBriefView `protobuf:"bytes,2,opt,name=missing_evaluation,json=missingEvaluation,proto3,oneof" json:"missing_evaluation,omitempty"`
}

func (x *AutoAssignResponse_DaycareService) Reset() {
	*x = AutoAssignResponse_DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoAssignResponse_DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoAssignResponse_DaycareService) ProtoMessage() {}

func (x *AutoAssignResponse_DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoAssignResponse_DaycareService.ProtoReflect.Descriptor instead.
func (*AutoAssignResponse_DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{35, 2}
}

func (x *AutoAssignResponse_DaycareService) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutoAssignResponse_DaycareService) GetMissingEvaluation() *v11.EvaluationBriefView {
	if x != nil {
		return x.MissingEvaluation
	}
	return nil
}

// Line item
type PreviewBookingRequestPricingResponse_LineItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id, virtual pet id or real pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	Service *v11.CustomizedServiceView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// unit price
	UnitPrice *money.Money `protobuf:"bytes,3,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// quantity
	Quantity int32 `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// total price
	TotalPrice *money.Money `protobuf:"bytes,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// Associated service ID, if this service is add-on. 0 if not associated.
	AssociatedServiceId int64 `protobuf:"varint,6,opt,name=associated_service_id,json=associatedServiceId,proto3" json:"associated_service_id,omitempty"`
}

func (x *PreviewBookingRequestPricingResponse_LineItem) Reset() {
	*x = PreviewBookingRequestPricingResponse_LineItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewBookingRequestPricingResponse_LineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewBookingRequestPricingResponse_LineItem) ProtoMessage() {}

func (x *PreviewBookingRequestPricingResponse_LineItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewBookingRequestPricingResponse_LineItem.ProtoReflect.Descriptor instead.
func (*PreviewBookingRequestPricingResponse_LineItem) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP(), []int{45, 0}
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetService() *v11.CustomizedServiceView {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *PreviewBookingRequestPricingResponse_LineItem) GetAssociatedServiceId() int64 {
	if x != nil {
		return x.AssociatedServiceId
	}
	return 0
}

var File_moego_service_online_booking_v1_booking_request_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_booking_request_service_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xcf, 0x22, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x48, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x06, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x36, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x07, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x08, 0x52,
	0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x88,
	0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x0a, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0b, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x51, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x48, 0x0c, 0x52, 0x04, 0x61, 0x74,
	0x74, 0x72, 0x88, 0x01, 0x01, 0x12, 0x79, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0d, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6e, 0x65, 0x65,
	0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x6c, 0x0a, 0x0a,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x48, 0x0e, 0x52, 0x0a, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x0f, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48,
	0x10, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x11, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x1a, 0xad,
	0x05, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x6a, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x67, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x12, 0x70, 0x0a, 0x0a, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48,
	0x00, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x71, 0x0a,
	0x0b, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67,
	0x12, 0x71, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xb1,
	0x02, 0x0a, 0x0f, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x5d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x5f, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f,
	0x6e, 0x73, 0x12, 0x5e, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x32, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73,
	0x56, 0x32, 0x1a, 0xc7, 0x05, 0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x59, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73,
	0x12, 0x58, 0x0a, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07,
	0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x0a, 0x6d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x0a,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a,
	0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x5a, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x66, 0x0a, 0x08,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xc3, 0x05, 0x0a,
	0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x5c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x58, 0x0a,
	0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64,
	0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x58, 0x0a, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01,
	0x01, 0x12, 0x61, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x66,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x5a, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x65, 0x0a, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x08, 0x77,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x1a, 0x71, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x74, 0x0a, 0x11, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x74, 0x0a, 0x11, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x5f, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x1a, 0x46, 0x0a, 0x0a, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12,
	0x38, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10,
	0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x22, 0xaa, 0x03, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x7b, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x10, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x12, 0x7d, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x22, 0xd2, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c,
	0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x57, 0x0a, 0x0f,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x73, 0x22, 0xe7, 0x0c, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04,
	0x20, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x7b, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x61, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x12, 0x35, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x00,
	0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x02,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x28, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x03, 0x52, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x88, 0x01, 0x01, 0x12, 0x63, 0x0a, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x13, 0xfa, 0x42, 0x10,
	0x92, 0x01, 0x0d, 0x08, 0x00, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3e,
	0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x18, 0x01, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x12, 0x2b,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x04, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x7d, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x48, 0x05, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x33, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x11,
	0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x13, 0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0f,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x07,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xae, 0x02, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5e, 0x0a, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12,
	0x57, 0x0a, 0x0f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x98, 0x0a, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x7b, 0x0a, 0x11, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42,
	0x0f, 0x92, 0x01, 0x0c, 0x08, 0x00, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x46, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32,
	0x48, 0x03, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x63, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x00, 0x18, 0x01, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x3e, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x18, 0x01, 0x52,
	0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x57, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x05,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x69,
	0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x11, 0x69, 0x73, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x07, 0x52, 0x13, 0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x08, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x48, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x45, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x42, 0x16, 0x0a, 0x14, 0x5f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x69, 0x73, 0x5f,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xa8, 0x02, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x0e,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x47,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x61, 0x0a, 0x0d, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x22, 0x87, 0x02, 0x0a, 0x19, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x6c, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x64, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x83, 0x01,
	0x0a, 0x18, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x08, 0x01, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x16, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0x1c, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x96, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x58, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4b, 0x0a, 0x22, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1a, 0x0a, 0x18, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x1b, 0x0a, 0x19, 0x52, 0x65, 0x74, 0x72, 0x79, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xb1, 0x2c, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a,
	0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x06,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x07,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x64, 0x0a, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x73, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x51, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x48, 0x08,
	0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x88, 0x01, 0x01, 0x12, 0x79, 0x0a, 0x0e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48,
	0x0a, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x1a, 0xd5, 0x02,
	0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x6a, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x67, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48,
	0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x96, 0x04, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x58, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x03, 0x61, 0x64,
	0x64, 0x12, 0x5e, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x6b, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x1a, 0xd3,
	0x01, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x12, 0x20, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0b, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x67, 0x57,
	0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x9c,
	0x08, 0x0a, 0x0f, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x5d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x5d, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73,
	0x12, 0x6f, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x08, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x56,
	0x32, 0x12, 0x66, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x1a, 0xe1, 0x04, 0x0a, 0x05, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x12, 0x6a, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x56, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41,
	0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x48, 0x00, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12,
	0x73, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x73, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48,
	0x00, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x1a, 0x68, 0x0a, 0x03, 0x41, 0x64, 0x64,
	0x12, 0x61, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x1a, 0x6b, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x61, 0x0a,
	0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e,
	0x1a, 0x21, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x1a, 0x9f, 0x0a,
	0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x5d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x67, 0x0a, 0x0f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x75, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x70, 0x0a, 0x12, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x48, 0x01, 0x52, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a, 0x06, 0x61,
	0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x6f, 0x0a, 0x09, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f,
	0x6e, 0x52, 0x08, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x56, 0x32, 0x12, 0x66, 0x0a, 0x08, 0x77,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x88, 0x01, 0x01, 0x1a, 0xe1, 0x04, 0x0a, 0x05, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x6a, 0x0a,
	0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x41,
	0x64, 0x64, 0x48, 0x00, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x73, 0x0a, 0x06, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x73,
	0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x59,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64,
	0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x1a, 0x68, 0x0a, 0x03, 0x41, 0x64, 0x64, 0x12, 0x61, 0x0a, 0x05, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x1a, 0x6b, 0x0a,
	0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x61, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x1a, 0x21, 0x0a, 0x06, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x42, 0x08, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x42, 0x15, 0x0a, 0x13, 0x5f,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x1a,
	0x95, 0x0a, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x5c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x67, 0x0a, 0x0f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x75, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x70, 0x0a, 0x12, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x48, 0x01, 0x52, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x06, 0x61,
	0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x6e, 0x0a, 0x09, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52,
	0x08, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x56, 0x32, 0x12, 0x65, 0x0a, 0x08, 0x77, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x02, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01,
	0x1a, 0xdc, 0x04, 0x0a, 0x05, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x69, 0x0a, 0x03, 0x61, 0x64,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x48, 0x00,
	0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x72, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48,
	0x00, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x72, 0x0a, 0x06, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x1a, 0x67, 0x0a,
	0x03, 0x41, 0x64, 0x64, 0x12, 0x60, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x1a, 0x6a, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x60, 0x0a, 0x05, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41,
	0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x1a, 0x21, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x75, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x61, 0x74, 0x74, 0x72, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x22, 0x1e, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfe, 0x09, 0x0a, 0x1c, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x48, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x5d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x61, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x05, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x1a, 0xeb, 0x01, 0x0a, 0x07,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x68, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x42, 0x09,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xe5, 0x01, 0x0a, 0x0f, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x66, 0x0a, 0x08,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x1a, 0xe2, 0x01, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x65, 0x0a, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x77, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x1f, 0x0a, 0x1d, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x95, 0x08, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x18, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52, 0x16, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x60, 0x0a,
	0x08, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x08, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x82, 0x01, 0x0a, 0x1a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52, 0x18, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x18, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x15, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x7c, 0x0a, 0x17,
	0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x52, 0x15, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x1a, 0xcc, 0x01, 0x0a, 0x0d, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x86, 0x01, 0x0a, 0x0d, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xab, 0x04, 0x0a, 0x1b, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x57, 0x0a, 0x0f, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x54, 0x6f, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x54,
	0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x57, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x70, 0x65,
	0x74, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x18, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x15, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x22, 0x5d, 0x0a, 0x1c, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0xbf, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x22, 0x5e, 0x0a, 0x1d, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xa5, 0x04, 0x0a, 0x1b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x48, 0x00, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x88, 0x01, 0x01, 0x1a, 0xcc,
	0x02, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x48, 0x0a, 0x12, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52,
	0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42,
	0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x7d, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x34, 0x0a, 0x1c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x57, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38,
	0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10,
	0x90, 0x4e, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xa6, 0x02, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x24, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x62, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0x52, 0x0a,
	0x24, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x54, 0x6f,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x62, 0x0a, 0x28, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x29, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x43, 0x0a, 0x28, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x55, 0x0a, 0x29, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x22, 0x89,
	0x12, 0x0a, 0x1d, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x7b, 0x0a, 0x11, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x7b, 0x0a, 0x11,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x78, 0x0a, 0x10, 0x64, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x0f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x81, 0x01, 0x0a, 0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x12, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x75,
	0x0a, 0x0f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x72, 0x0a, 0x0e, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x18, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x1a, 0x81, 0x01, 0x0a, 0x0f, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x1a, 0x54, 0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x1a, 0x53, 0x0a, 0x0e, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x00, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x1a,
	0x8c, 0x01, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0c,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x1a, 0x7f,
	0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x1a,
	0x7f, 0x0a, 0x0d, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x1a, 0x7e, 0x0a, 0x0c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x1a, 0xab, 0x02, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x5f, 0x0a, 0x1e, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x11,
	0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x35, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x22, 0xe6, 0x07, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x11, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x10, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x76, 0x0a,
	0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74,
	0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x12, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x6d, 0x0a, 0x10, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x0f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x1a, 0xce, 0x01, 0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x12,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72,
	0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x48, 0x01, 0x52, 0x11, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x88, 0x02, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x13, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72,
	0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x0f, 0x61,
	0x6c, 0x6c, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0e, 0x61, 0x6c, 0x6c, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x1a, 0x9a, 0x01, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x61, 0x0a, 0x12, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00,
	0x52, 0x11, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9a, 0x01,
	0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x69, 0x0a, 0x23, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x42, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x22, 0x7e, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x88, 0x01,
	0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x22, 0x23, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x24, 0x0a, 0x22, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xd7, 0x01, 0x0a, 0x23, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x26, 0x0a, 0x24, 0x4d, 0x6f, 0x76,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x96, 0x02, 0x0a, 0x23, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x0c, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x70,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xbc, 0x03, 0x0a, 0x24, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x1a, 0xa4, 0x02, 0x0a, 0x08, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x31, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x22, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xf1, 0x01, 0x0a, 0x23, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x79, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01,
	0x0a, 0x11, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f,
	0x75, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x55, 0x73, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x55, 0x73, 0x65, 0x1a, 0x42, 0x0a, 0x14, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x55, 0x73, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xc9, 0x1a, 0x0a, 0x15, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x73, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x80, 0x01, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x8d, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa5, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01,
	0x0a, 0x15, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x75, 0x0a, 0x0a, 0x41, 0x75, 0x74,
	0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x98, 0x01, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x99, 0x01, 0x0a, 0x16,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x93, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xba, 0x01, 0x0a,
	0x21, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xba, 0x01, 0x0a, 0x21, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x12,
	0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x57, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xab,
	0x01, 0x0a, 0x1c, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a,
	0x1b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xab, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x67, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_booking_request_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_booking_request_service_proto_rawDescData = file_moego_service_online_booking_v1_booking_request_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_booking_request_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_booking_request_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_booking_request_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_booking_request_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_booking_request_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes = make([]protoimpl.MessageInfo, 94)
var file_moego_service_online_booking_v1_booking_request_service_proto_goTypes = []interface{}{
	(*CreateBookingRequestRequest)(nil),                              // 0: moego.service.online_booking.v1.CreateBookingRequestRequest
	(*GetBookingRequestRequest)(nil),                                 // 1: moego.service.online_booking.v1.GetBookingRequestRequest
	(*GetBookingRequestResponse)(nil),                                // 2: moego.service.online_booking.v1.GetBookingRequestResponse
	(*ListBookingRequestsRequest)(nil),                               // 3: moego.service.online_booking.v1.ListBookingRequestsRequest
	(*ListBookingRequestsResponse)(nil),                              // 4: moego.service.online_booking.v1.ListBookingRequestsResponse
	(*ListWaitlistsRequest)(nil),                                     // 5: moego.service.online_booking.v1.ListWaitlistsRequest
	(*ListWaitlistsResponse)(nil),                                    // 6: moego.service.online_booking.v1.ListWaitlistsResponse
	(*WaitlistExtra)(nil),                                            // 7: moego.service.online_booking.v1.WaitlistExtra
	(*CreateGroomingOnlyRequest)(nil),                                // 8: moego.service.online_booking.v1.CreateGroomingOnlyRequest
	(*CreateGroomingOnlyResponse)(nil),                               // 9: moego.service.online_booking.v1.CreateGroomingOnlyResponse
	(*UpdateBookingRequestStatusRequest)(nil),                        // 10: moego.service.online_booking.v1.UpdateBookingRequestStatusRequest
	(*UpdateBookingRequestStatusResponse)(nil),                       // 11: moego.service.online_booking.v1.UpdateBookingRequestStatusResponse
	(*RetryFailedEventsRequest)(nil),                                 // 12: moego.service.online_booking.v1.RetryFailedEventsRequest
	(*RetryFailedEventsResponse)(nil),                                // 13: moego.service.online_booking.v1.RetryFailedEventsResponse
	(*UpdateBookingRequestRequest)(nil),                              // 14: moego.service.online_booking.v1.UpdateBookingRequestRequest
	(*UpdateBookingRequestResponse)(nil),                             // 15: moego.service.online_booking.v1.UpdateBookingRequestResponse
	(*ReplaceBookingRequestRequest)(nil),                             // 16: moego.service.online_booking.v1.ReplaceBookingRequestRequest
	(*ReplaceBookingRequestResponse)(nil),                            // 17: moego.service.online_booking.v1.ReplaceBookingRequestResponse
	(*GetAutoAssignRequest)(nil),                                     // 18: moego.service.online_booking.v1.GetAutoAssignRequest
	(*GetAutoAssignResponse)(nil),                                    // 19: moego.service.online_booking.v1.GetAutoAssignResponse
	(*AcceptBookingRequestRequest)(nil),                              // 20: moego.service.online_booking.v1.AcceptBookingRequestRequest
	(*AcceptBookingRequestResponse)(nil),                             // 21: moego.service.online_booking.v1.AcceptBookingRequestResponse
	(*DeclineBookingRequestRequest)(nil),                             // 22: moego.service.online_booking.v1.DeclineBookingRequestRequest
	(*DeclineBookingRequestResponse)(nil),                            // 23: moego.service.online_booking.v1.DeclineBookingRequestResponse
	(*CountBookingRequestsRequest)(nil),                              // 24: moego.service.online_booking.v1.CountBookingRequestsRequest
	(*CountBookingRequestsResponse)(nil),                             // 25: moego.service.online_booking.v1.CountBookingRequestsResponse
	(*ListBookingRequestIdRequest)(nil),                              // 26: moego.service.online_booking.v1.ListBookingRequestIdRequest
	(*ListBookingRequestIdResponse)(nil),                             // 27: moego.service.online_booking.v1.ListBookingRequestIdResponse
	(*SyncBookingRequestFromAppointmentRequest)(nil),                 // 28: moego.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest
	(*SyncBookingRequestFromAppointmentResponse)(nil),                // 29: moego.service.online_booking.v1.SyncBookingRequestFromAppointmentResponse
	(*TriggerBookingRequestAutoAcceptedRequest)(nil),                 // 30: moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest
	(*TriggerBookingRequestAutoAcceptedResponse)(nil),                // 31: moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedResponse
	(*AcceptBookingRequestV2Request)(nil),                            // 32: moego.service.online_booking.v1.AcceptBookingRequestV2Request
	(*AcceptBookingRequestV2Response)(nil),                           // 33: moego.service.online_booking.v1.AcceptBookingRequestV2Response
	(*AutoAssignRequest)(nil),                                        // 34: moego.service.online_booking.v1.AutoAssignRequest
	(*AutoAssignResponse)(nil),                                       // 35: moego.service.online_booking.v1.AutoAssignResponse
	(*CreateBoardingServiceWaitlistRequest)(nil),                     // 36: moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest
	(*CreateDaycareServiceWaitlistRequest)(nil),                      // 37: moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest
	(*UpdateBoardingServiceWaitlistRequest)(nil),                     // 38: moego.service.online_booking.v1.UpdateBoardingServiceWaitlistRequest
	(*UpdateDaycareServiceWaitlistRequest)(nil),                      // 39: moego.service.online_booking.v1.UpdateDaycareServiceWaitlistRequest
	(*CheckWaitlistAvailableTaskRequest)(nil),                        // 40: moego.service.online_booking.v1.CheckWaitlistAvailableTaskRequest
	(*CheckWaitlistAvailableTaskResponse)(nil),                       // 41: moego.service.online_booking.v1.CheckWaitlistAvailableTaskResponse
	(*MoveBookingRequestToWaitlistRequest)(nil),                      // 42: moego.service.online_booking.v1.MoveBookingRequestToWaitlistRequest
	(*MoveBookingRequestToWaitlistResponse)(nil),                     // 43: moego.service.online_booking.v1.MoveBookingRequestToWaitlistResponse
	(*PreviewBookingRequestPricingRequest)(nil),                      // 44: moego.service.online_booking.v1.PreviewBookingRequestPricingRequest
	(*PreviewBookingRequestPricingResponse)(nil),                     // 45: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse
	(*CountBookingRequestByFilterRequest)(nil),                       // 46: moego.service.online_booking.v1.CountBookingRequestByFilterRequest
	(*CountBookingRequestByFilterResponse)(nil),                      // 47: moego.service.online_booking.v1.CountBookingRequestByFilterResponse
	(*CreateBookingRequestRequest_Service)(nil),                      // 48: moego.service.online_booking.v1.CreateBookingRequestRequest.Service
	(*CreateBookingRequestRequest_GroomingService)(nil),              // 49: moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingService
	(*CreateBookingRequestRequest_BoardingService)(nil),              // 50: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService
	(*CreateBookingRequestRequest_DaycareService)(nil),               // 51: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService
	(*CreateBookingRequestRequest_EvaluationService)(nil),            // 52: moego.service.online_booking.v1.CreateBookingRequestRequest.EvaluationService
	(*CreateBookingRequestRequest_DogWalkingService)(nil),            // 53: moego.service.online_booking.v1.CreateBookingRequestRequest.DogWalkingService
	(*CreateBookingRequestRequest_GroupClassService)(nil),            // 54: moego.service.online_booking.v1.CreateBookingRequestRequest.GroupClassService
	(*CreateBookingRequestRequest_Membership)(nil),                   // 55: moego.service.online_booking.v1.CreateBookingRequestRequest.Membership
	(*UpdateBookingRequestRequest_Service)(nil),                      // 56: moego.service.online_booking.v1.UpdateBookingRequestRequest.Service
	(*UpdateBookingRequestRequest_ServiceDetail)(nil),                // 57: moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail
	(*UpdateBookingRequestRequest_GroomingService)(nil),              // 58: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService
	(*UpdateBookingRequestRequest_BoardingService)(nil),              // 59: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService
	(*UpdateBookingRequestRequest_DaycareService)(nil),               // 60: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService
	(*UpdateBookingRequestRequest_ServiceDetail_Delete)(nil),         // 61: moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail.Delete
	(*UpdateBookingRequestRequest_GroomingService_Addon)(nil),        // 62: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon
	(*UpdateBookingRequestRequest_GroomingService_Addon_Add)(nil),    // 63: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Add
	(*UpdateBookingRequestRequest_GroomingService_Addon_Update)(nil), // 64: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Update
	(*UpdateBookingRequestRequest_GroomingService_Addon_Delete)(nil), // 65: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Delete
	(*UpdateBookingRequestRequest_BoardingService_Addon)(nil),        // 66: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon
	(*UpdateBookingRequestRequest_BoardingService_Addon_Add)(nil),    // 67: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Add
	(*UpdateBookingRequestRequest_BoardingService_Addon_Update)(nil), // 68: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Update
	(*UpdateBookingRequestRequest_BoardingService_Addon_Delete)(nil), // 69: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Delete
	(*UpdateBookingRequestRequest_DaycareService_Addon)(nil),         // 70: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon
	(*UpdateBookingRequestRequest_DaycareService_Addon_Add)(nil),     // 71: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Add
	(*UpdateBookingRequestRequest_DaycareService_Addon_Update)(nil),  // 72: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Update
	(*UpdateBookingRequestRequest_DaycareService_Addon_Delete)(nil),  // 73: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Delete
	(*ReplaceBookingRequestRequest_Service)(nil),                     // 74: moego.service.online_booking.v1.ReplaceBookingRequestRequest.Service
	(*ReplaceBookingRequestRequest_BoardingService)(nil),             // 75: moego.service.online_booking.v1.ReplaceBookingRequestRequest.BoardingService
	(*ReplaceBookingRequestRequest_DaycareService)(nil),              // 76: moego.service.online_booking.v1.ReplaceBookingRequestRequest.DaycareService
	(*GetAutoAssignResponse_AssignRequire)(nil),                      // 77: moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequire
	(*GetAutoAssignResponse_LodgingDetail)(nil),                      // 78: moego.service.online_booking.v1.GetAutoAssignResponse.LodgingDetail
	(*CountBookingRequestsRequest_Filters)(nil),                      // 79: moego.service.online_booking.v1.CountBookingRequestsRequest.Filters
	nil, // 80: moego.service.online_booking.v1.ListBookingRequestIdResponse.AppointmentIdToBookingRequestIdEntry
	(*AcceptBookingRequestV2Request_GroomingService)(nil),         // 81: moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingService
	(*AcceptBookingRequestV2Request_BoardingService)(nil),         // 82: moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingService
	(*AcceptBookingRequestV2Request_DaycareService)(nil),          // 83: moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareService
	(*AcceptBookingRequestV2Request_EvaluationService)(nil),       // 84: moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationService
	(*AcceptBookingRequestV2Request_GroomingAddon)(nil),           // 85: moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddon
	(*AcceptBookingRequestV2Request_BoardingAddon)(nil),           // 86: moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddon
	(*AcceptBookingRequestV2Request_DaycareAddon)(nil),            // 87: moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddon
	(*AcceptBookingRequestV2Request_CreateEvaluationRequest)(nil), // 88: moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest
	(*AutoAssignResponse_BoardingService)(nil),                    // 89: moego.service.online_booking.v1.AutoAssignResponse.BoardingService
	(*AutoAssignResponse_EvaluationService)(nil),                  // 90: moego.service.online_booking.v1.AutoAssignResponse.EvaluationService
	(*AutoAssignResponse_DaycareService)(nil),                     // 91: moego.service.online_booking.v1.AutoAssignResponse.DaycareService
	(*PreviewBookingRequestPricingResponse_LineItem)(nil),         // 92: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem
	nil,                                 // 93: moego.service.online_booking.v1.CountBookingRequestByFilterResponse.EvaluationInUseEntry
	(*timestamppb.Timestamp)(nil),       // 94: google.protobuf.Timestamp
	(*v1.BookingRequestModel_Attr)(nil), // 95: moego.models.online_booking.v1.BookingRequestModel.Attr
	(v1.BookingRequestModel_PaymentStatus)(0),    // 96: moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	(v1.BookingRequestModel_Source)(0),           // 97: moego.models.online_booking.v1.BookingRequestModel.Source
	(v1.BookingRequestAssociatedModel)(0),        // 98: moego.models.online_booking.v1.BookingRequestAssociatedModel
	(*v1.BookingRequestModel)(nil),               // 99: moego.models.online_booking.v1.BookingRequestModel
	(v1.BookingRequestStatus)(0),                 // 100: moego.models.online_booking.v1.BookingRequestStatus
	(*date.Date)(nil),                            // 101: google.type.Date
	(*v2.OrderBy)(nil),                           // 102: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),                 // 103: moego.utils.v2.PaginationRequest
	(v11.ServiceItemType)(0),                     // 104: moego.models.offering.v1.ServiceItemType
	(*v2.PaginationResponse)(nil),                // 105: moego.utils.v2.PaginationResponse
	(*v1.BookingRequestDef)(nil),                 // 106: moego.models.online_booking.v1.BookingRequestDef
	(*v1.GroomingServiceDetailDef)(nil),          // 107: moego.models.online_booking.v1.GroomingServiceDetailDef
	(*v1.PetToLodgingDef)(nil),                   // 108: moego.models.online_booking.v1.PetToLodgingDef
	(*v1.PetToStaffDef)(nil),                     // 109: moego.models.online_booking.v1.PetToStaffDef
	(*v1.PetToServiceDef)(nil),                   // 110: moego.models.online_booking.v1.PetToServiceDef
	(*v12.Tenant)(nil),                           // 111: moego.models.organization.v1.Tenant
	(*v2.DateList)(nil),                          // 112: moego.utils.v2.DateList
	(*v1.PetServiceDetails)(nil),                 // 113: moego.models.online_booking.v1.PetServiceDetails
	(*CreateGroomingServiceDetailRequest)(nil),   // 114: moego.service.online_booking.v1.CreateGroomingServiceDetailRequest
	(*CreateGroomingAddOnDetailRequest)(nil),     // 115: moego.service.online_booking.v1.CreateGroomingAddOnDetailRequest
	(*CreateBoardingServiceDetailRequest)(nil),   // 116: moego.service.online_booking.v1.CreateBoardingServiceDetailRequest
	(*CreateBoardingAddOnDetailRequest)(nil),     // 117: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest
	(*CreateFeedingRequest)(nil),                 // 118: moego.service.online_booking.v1.CreateFeedingRequest
	(*CreateMedicationRequest)(nil),              // 119: moego.service.online_booking.v1.CreateMedicationRequest
	(*CreateDaycareServiceDetailRequest)(nil),    // 120: moego.service.online_booking.v1.CreateDaycareServiceDetailRequest
	(*CreateDaycareAddOnDetailRequest)(nil),      // 121: moego.service.online_booking.v1.CreateDaycareAddOnDetailRequest
	(*CreateEvaluationTestDetailRequest)(nil),    // 122: moego.service.online_booking.v1.CreateEvaluationTestDetailRequest
	(*CreateDogWalkingServiceDetailRequest)(nil), // 123: moego.service.online_booking.v1.CreateDogWalkingServiceDetailRequest
	(*CreateGroupClassServiceDetailRequest)(nil), // 124: moego.service.online_booking.v1.CreateGroupClassServiceDetailRequest
	(*UpdateGroomingServiceDetailRequest)(nil),   // 125: moego.service.online_booking.v1.UpdateGroomingServiceDetailRequest
	(*UpdateGroomingAddOnDetailRequest)(nil),     // 126: moego.service.online_booking.v1.UpdateGroomingAddOnDetailRequest
	(*UpsertGroomingAutoAssignRequest)(nil),      // 127: moego.service.online_booking.v1.UpsertGroomingAutoAssignRequest
	(*UpdateBoardingServiceDetailRequest)(nil),   // 128: moego.service.online_booking.v1.UpdateBoardingServiceDetailRequest
	(*CreateFeedingRequestList)(nil),             // 129: moego.service.online_booking.v1.CreateFeedingRequestList
	(*CreateMedicationRequestList)(nil),          // 130: moego.service.online_booking.v1.CreateMedicationRequestList
	(*UpdateBoardingAddOnDetailRequest)(nil),     // 131: moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest
	(*UpdateDaycareServiceDetailRequest)(nil),    // 132: moego.service.online_booking.v1.UpdateDaycareServiceDetailRequest
	(*UpdateDaycareAddOnDetailRequest)(nil),      // 133: moego.service.online_booking.v1.UpdateDaycareAddOnDetailRequest
	(*interval.Interval)(nil),                    // 134: google.type.Interval
	(*v11.EvaluationBriefView)(nil),              // 135: moego.models.offering.v1.EvaluationBriefView
	(*v11.CustomizedServiceView)(nil),            // 136: moego.models.offering.v1.CustomizedServiceView
	(*money.Money)(nil),                          // 137: google.type.Money
	(*wrapperspb.Int64Value)(nil),                // 138: google.protobuf.Int64Value
}
var file_moego_service_online_booking_v1_booking_request_service_proto_depIdxs = []int32{
	94,  // 0: moego.service.online_booking.v1.CreateBookingRequestRequest.created_at:type_name -> google.protobuf.Timestamp
	94,  // 1: moego.service.online_booking.v1.CreateBookingRequestRequest.updated_at:type_name -> google.protobuf.Timestamp
	48,  // 2: moego.service.online_booking.v1.CreateBookingRequestRequest.services:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.Service
	95,  // 3: moego.service.online_booking.v1.CreateBookingRequestRequest.attr:type_name -> moego.models.online_booking.v1.BookingRequestModel.Attr
	96,  // 4: moego.service.online_booking.v1.CreateBookingRequestRequest.payment_status:type_name -> moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	55,  // 5: moego.service.online_booking.v1.CreateBookingRequestRequest.membership:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.Membership
	97,  // 6: moego.service.online_booking.v1.CreateBookingRequestRequest.source:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	98,  // 7: moego.service.online_booking.v1.GetBookingRequestRequest.associated_models:type_name -> moego.models.online_booking.v1.BookingRequestAssociatedModel
	96,  // 8: moego.service.online_booking.v1.GetBookingRequestRequest.payment_statuses:type_name -> moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	99,  // 9: moego.service.online_booking.v1.GetBookingRequestResponse.booking_request:type_name -> moego.models.online_booking.v1.BookingRequestModel
	7,   // 10: moego.service.online_booking.v1.GetBookingRequestResponse.waitlist_extras:type_name -> moego.service.online_booking.v1.WaitlistExtra
	98,  // 11: moego.service.online_booking.v1.ListBookingRequestsRequest.associated_models:type_name -> moego.models.online_booking.v1.BookingRequestAssociatedModel
	100, // 12: moego.service.online_booking.v1.ListBookingRequestsRequest.statuses:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	101, // 13: moego.service.online_booking.v1.ListBookingRequestsRequest.start_date:type_name -> google.type.Date
	101, // 14: moego.service.online_booking.v1.ListBookingRequestsRequest.end_date:type_name -> google.type.Date
	102, // 15: moego.service.online_booking.v1.ListBookingRequestsRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	103, // 16: moego.service.online_booking.v1.ListBookingRequestsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	104, // 17: moego.service.online_booking.v1.ListBookingRequestsRequest.service_items:type_name -> moego.models.offering.v1.ServiceItemType
	96,  // 18: moego.service.online_booking.v1.ListBookingRequestsRequest.payment_statuses:type_name -> moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	97,  // 19: moego.service.online_booking.v1.ListBookingRequestsRequest.source:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	101, // 20: moego.service.online_booking.v1.ListBookingRequestsRequest.latest_end_date:type_name -> google.type.Date
	97,  // 21: moego.service.online_booking.v1.ListBookingRequestsRequest.sources:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	99,  // 22: moego.service.online_booking.v1.ListBookingRequestsResponse.booking_requests:type_name -> moego.models.online_booking.v1.BookingRequestModel
	7,   // 23: moego.service.online_booking.v1.ListBookingRequestsResponse.waitlist_extras:type_name -> moego.service.online_booking.v1.WaitlistExtra
	105, // 24: moego.service.online_booking.v1.ListBookingRequestsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	98,  // 25: moego.service.online_booking.v1.ListWaitlistsRequest.associated_models:type_name -> moego.models.online_booking.v1.BookingRequestAssociatedModel
	101, // 26: moego.service.online_booking.v1.ListWaitlistsRequest.start_date:type_name -> google.type.Date
	101, // 27: moego.service.online_booking.v1.ListWaitlistsRequest.end_date:type_name -> google.type.Date
	102, // 28: moego.service.online_booking.v1.ListWaitlistsRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	103, // 29: moego.service.online_booking.v1.ListWaitlistsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	104, // 30: moego.service.online_booking.v1.ListWaitlistsRequest.service_items:type_name -> moego.models.offering.v1.ServiceItemType
	97,  // 31: moego.service.online_booking.v1.ListWaitlistsRequest.source:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	101, // 32: moego.service.online_booking.v1.ListWaitlistsRequest.latest_end_date:type_name -> google.type.Date
	97,  // 33: moego.service.online_booking.v1.ListWaitlistsRequest.sources:type_name -> moego.models.online_booking.v1.BookingRequestModel.Source
	99,  // 34: moego.service.online_booking.v1.ListWaitlistsResponse.booking_requests:type_name -> moego.models.online_booking.v1.BookingRequestModel
	7,   // 35: moego.service.online_booking.v1.ListWaitlistsResponse.waitlist_extras:type_name -> moego.service.online_booking.v1.WaitlistExtra
	105, // 36: moego.service.online_booking.v1.ListWaitlistsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	106, // 37: moego.service.online_booking.v1.CreateGroomingOnlyRequest.booking_request:type_name -> moego.models.online_booking.v1.BookingRequestDef
	107, // 38: moego.service.online_booking.v1.CreateGroomingOnlyRequest.grooming_service_details:type_name -> moego.models.online_booking.v1.GroomingServiceDetailDef
	100, // 39: moego.service.online_booking.v1.UpdateBookingRequestStatusRequest.status:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	100, // 40: moego.service.online_booking.v1.UpdateBookingRequestRequest.status:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	56,  // 41: moego.service.online_booking.v1.UpdateBookingRequestRequest.services:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.Service
	57,  // 42: moego.service.online_booking.v1.UpdateBookingRequestRequest.service_details:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail
	95,  // 43: moego.service.online_booking.v1.UpdateBookingRequestRequest.attr:type_name -> moego.models.online_booking.v1.BookingRequestModel.Attr
	96,  // 44: moego.service.online_booking.v1.UpdateBookingRequestRequest.payment_status:type_name -> moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	100, // 45: moego.service.online_booking.v1.ReplaceBookingRequestRequest.status:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	74,  // 46: moego.service.online_booking.v1.ReplaceBookingRequestRequest.services:type_name -> moego.service.online_booking.v1.ReplaceBookingRequestRequest.Service
	77,  // 47: moego.service.online_booking.v1.GetAutoAssignResponse.boarding_assign_requires:type_name -> moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequire
	108, // 48: moego.service.online_booking.v1.GetAutoAssignResponse.pet_to_lodgings:type_name -> moego.models.online_booking.v1.PetToLodgingDef
	78,  // 49: moego.service.online_booking.v1.GetAutoAssignResponse.lodgings:type_name -> moego.service.online_booking.v1.GetAutoAssignResponse.LodgingDetail
	77,  // 50: moego.service.online_booking.v1.GetAutoAssignResponse.evaluation_assign_requires:type_name -> moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequire
	109, // 51: moego.service.online_booking.v1.GetAutoAssignResponse.evaluation_pet_to_staffs:type_name -> moego.models.online_booking.v1.PetToStaffDef
	77,  // 52: moego.service.online_booking.v1.GetAutoAssignResponse.daycare_assign_requires:type_name -> moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequire
	108, // 53: moego.service.online_booking.v1.AcceptBookingRequestRequest.pet_to_lodgings:type_name -> moego.models.online_booking.v1.PetToLodgingDef
	109, // 54: moego.service.online_booking.v1.AcceptBookingRequestRequest.pet_to_staffs:type_name -> moego.models.online_booking.v1.PetToStaffDef
	110, // 55: moego.service.online_booking.v1.AcceptBookingRequestRequest.pet_to_services:type_name -> moego.models.online_booking.v1.PetToServiceDef
	109, // 56: moego.service.online_booking.v1.AcceptBookingRequestRequest.evaluation_pet_to_staffs:type_name -> moego.models.online_booking.v1.PetToStaffDef
	111, // 57: moego.service.online_booking.v1.CountBookingRequestsRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	79,  // 58: moego.service.online_booking.v1.CountBookingRequestsRequest.filters:type_name -> moego.service.online_booking.v1.CountBookingRequestsRequest.Filters
	80,  // 59: moego.service.online_booking.v1.ListBookingRequestIdResponse.appointment_id_to_booking_request_id:type_name -> moego.service.online_booking.v1.ListBookingRequestIdResponse.AppointmentIdToBookingRequestIdEntry
	81,  // 60: moego.service.online_booking.v1.AcceptBookingRequestV2Request.grooming_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingService
	82,  // 61: moego.service.online_booking.v1.AcceptBookingRequestV2Request.boarding_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingService
	83,  // 62: moego.service.online_booking.v1.AcceptBookingRequestV2Request.daycare_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareService
	84,  // 63: moego.service.online_booking.v1.AcceptBookingRequestV2Request.evaluation_services:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationService
	85,  // 64: moego.service.online_booking.v1.AcceptBookingRequestV2Request.grooming_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddon
	86,  // 65: moego.service.online_booking.v1.AcceptBookingRequestV2Request.boarding_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddon
	87,  // 66: moego.service.online_booking.v1.AcceptBookingRequestV2Request.daycare_addons:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddon
	88,  // 67: moego.service.online_booking.v1.AcceptBookingRequestV2Request.create_evaluation_requests:type_name -> moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest
	89,  // 68: moego.service.online_booking.v1.AutoAssignResponse.boarding_services:type_name -> moego.service.online_booking.v1.AutoAssignResponse.BoardingService
	90,  // 69: moego.service.online_booking.v1.AutoAssignResponse.evaluation_services:type_name -> moego.service.online_booking.v1.AutoAssignResponse.EvaluationService
	91,  // 70: moego.service.online_booking.v1.AutoAssignResponse.daycare_services:type_name -> moego.service.online_booking.v1.AutoAssignResponse.DaycareService
	101, // 71: moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest.start_date:type_name -> google.type.Date
	101, // 72: moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest.end_date:type_name -> google.type.Date
	101, // 73: moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest.specific_dates:type_name -> google.type.Date
	101, // 74: moego.service.online_booking.v1.UpdateBoardingServiceWaitlistRequest.start_date:type_name -> google.type.Date
	101, // 75: moego.service.online_booking.v1.UpdateBoardingServiceWaitlistRequest.end_date:type_name -> google.type.Date
	112, // 76: moego.service.online_booking.v1.UpdateDaycareServiceWaitlistRequest.specific_dates:type_name -> moego.utils.v2.DateList
	113, // 77: moego.service.online_booking.v1.PreviewBookingRequestPricingRequest.pet_services:type_name -> moego.models.online_booking.v1.PetServiceDetails
	92,  // 78: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.line_items:type_name -> moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem
	93,  // 79: moego.service.online_booking.v1.CountBookingRequestByFilterResponse.evaluation_in_use:type_name -> moego.service.online_booking.v1.CountBookingRequestByFilterResponse.EvaluationInUseEntry
	49,  // 80: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.grooming:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingService
	50,  // 81: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.boarding:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService
	51,  // 82: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.daycare:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService
	52,  // 83: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.evaluation:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.EvaluationService
	53,  // 84: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.dog_walking:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.DogWalkingService
	54,  // 85: moego.service.online_booking.v1.CreateBookingRequestRequest.Service.group_class:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.GroupClassService
	114, // 86: moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingService.service:type_name -> moego.service.online_booking.v1.CreateGroomingServiceDetailRequest
	114, // 87: moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingService.addons:type_name -> moego.service.online_booking.v1.CreateGroomingServiceDetailRequest
	115, // 88: moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingService.addons_v2:type_name -> moego.service.online_booking.v1.CreateGroomingAddOnDetailRequest
	116, // 89: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.service:type_name -> moego.service.online_booking.v1.CreateBoardingServiceDetailRequest
	117, // 90: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.addons:type_name -> moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest
	118, // 91: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.feeding:type_name -> moego.service.online_booking.v1.CreateFeedingRequest
	119, // 92: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.medication:type_name -> moego.service.online_booking.v1.CreateMedicationRequest
	118, // 93: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.feedings:type_name -> moego.service.online_booking.v1.CreateFeedingRequest
	119, // 94: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.medications:type_name -> moego.service.online_booking.v1.CreateMedicationRequest
	36,  // 95: moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingService.waitlist:type_name -> moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest
	120, // 96: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.service:type_name -> moego.service.online_booking.v1.CreateDaycareServiceDetailRequest
	121, // 97: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.addons:type_name -> moego.service.online_booking.v1.CreateDaycareAddOnDetailRequest
	118, // 98: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.feeding:type_name -> moego.service.online_booking.v1.CreateFeedingRequest
	119, // 99: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.medication:type_name -> moego.service.online_booking.v1.CreateMedicationRequest
	118, // 100: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.feedings:type_name -> moego.service.online_booking.v1.CreateFeedingRequest
	119, // 101: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.medications:type_name -> moego.service.online_booking.v1.CreateMedicationRequest
	37,  // 102: moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareService.waitlist:type_name -> moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest
	122, // 103: moego.service.online_booking.v1.CreateBookingRequestRequest.EvaluationService.service:type_name -> moego.service.online_booking.v1.CreateEvaluationTestDetailRequest
	123, // 104: moego.service.online_booking.v1.CreateBookingRequestRequest.DogWalkingService.service:type_name -> moego.service.online_booking.v1.CreateDogWalkingServiceDetailRequest
	124, // 105: moego.service.online_booking.v1.CreateBookingRequestRequest.GroupClassService.service:type_name -> moego.service.online_booking.v1.CreateGroupClassServiceDetailRequest
	58,  // 106: moego.service.online_booking.v1.UpdateBookingRequestRequest.Service.grooming:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService
	59,  // 107: moego.service.online_booking.v1.UpdateBookingRequestRequest.Service.boarding:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService
	60,  // 108: moego.service.online_booking.v1.UpdateBookingRequestRequest.Service.daycare:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService
	48,  // 109: moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail.add:type_name -> moego.service.online_booking.v1.CreateBookingRequestRequest.Service
	56,  // 110: moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail.update:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.Service
	61,  // 111: moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail.delete:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceDetail.Delete
	125, // 112: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.service:type_name -> moego.service.online_booking.v1.UpdateGroomingServiceDetailRequest
	126, // 113: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.addons:type_name -> moego.service.online_booking.v1.UpdateGroomingAddOnDetailRequest
	62,  // 114: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.addons_v2:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon
	127, // 115: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.auto_assign:type_name -> moego.service.online_booking.v1.UpsertGroomingAutoAssignRequest
	128, // 116: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.service:type_name -> moego.service.online_booking.v1.UpdateBoardingServiceDetailRequest
	129, // 117: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.feedings_upsert:type_name -> moego.service.online_booking.v1.CreateFeedingRequestList
	130, // 118: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.medications_upsert:type_name -> moego.service.online_booking.v1.CreateMedicationRequestList
	131, // 119: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.addons:type_name -> moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest
	66,  // 120: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.addons_v2:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon
	38,  // 121: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.waitlist:type_name -> moego.service.online_booking.v1.UpdateBoardingServiceWaitlistRequest
	132, // 122: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.service:type_name -> moego.service.online_booking.v1.UpdateDaycareServiceDetailRequest
	129, // 123: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.feedings_upsert:type_name -> moego.service.online_booking.v1.CreateFeedingRequestList
	130, // 124: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.medications_upsert:type_name -> moego.service.online_booking.v1.CreateMedicationRequestList
	133, // 125: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.addons:type_name -> moego.service.online_booking.v1.UpdateDaycareAddOnDetailRequest
	70,  // 126: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.addons_v2:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon
	39,  // 127: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.waitlist:type_name -> moego.service.online_booking.v1.UpdateDaycareServiceWaitlistRequest
	63,  // 128: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.add:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Add
	64,  // 129: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.update:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Update
	65,  // 130: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.delete:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Delete
	115, // 131: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Add.addon:type_name -> moego.service.online_booking.v1.CreateGroomingAddOnDetailRequest
	126, // 132: moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingService.Addon.Update.addon:type_name -> moego.service.online_booking.v1.UpdateGroomingAddOnDetailRequest
	67,  // 133: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.add:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Add
	68,  // 134: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.update:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Update
	69,  // 135: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.delete:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Delete
	117, // 136: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Add.addon:type_name -> moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest
	131, // 137: moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingService.Addon.Update.addon:type_name -> moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest
	71,  // 138: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.add:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Add
	72,  // 139: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.update:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Update
	73,  // 140: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.delete:type_name -> moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Delete
	121, // 141: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Add.addon:type_name -> moego.service.online_booking.v1.CreateDaycareAddOnDetailRequest
	133, // 142: moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareService.Addon.Update.addon:type_name -> moego.service.online_booking.v1.UpdateDaycareAddOnDetailRequest
	75,  // 143: moego.service.online_booking.v1.ReplaceBookingRequestRequest.Service.boarding:type_name -> moego.service.online_booking.v1.ReplaceBookingRequestRequest.BoardingService
	76,  // 144: moego.service.online_booking.v1.ReplaceBookingRequestRequest.Service.daycare:type_name -> moego.service.online_booking.v1.ReplaceBookingRequestRequest.DaycareService
	116, // 145: moego.service.online_booking.v1.ReplaceBookingRequestRequest.BoardingService.service:type_name -> moego.service.online_booking.v1.CreateBoardingServiceDetailRequest
	36,  // 146: moego.service.online_booking.v1.ReplaceBookingRequestRequest.BoardingService.waitlist:type_name -> moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest
	120, // 147: moego.service.online_booking.v1.ReplaceBookingRequestRequest.DaycareService.service:type_name -> moego.service.online_booking.v1.CreateDaycareServiceDetailRequest
	37,  // 148: moego.service.online_booking.v1.ReplaceBookingRequestRequest.DaycareService.waitlist:type_name -> moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest
	134, // 149: moego.service.online_booking.v1.CountBookingRequestsRequest.Filters.created_time_range:type_name -> google.type.Interval
	100, // 150: moego.service.online_booking.v1.CountBookingRequestsRequest.Filters.statuses:type_name -> moego.models.online_booking.v1.BookingRequestStatus
	96,  // 151: moego.service.online_booking.v1.CountBookingRequestsRequest.Filters.payment_statuses:type_name -> moego.models.online_booking.v1.BookingRequestModel.PaymentStatus
	101, // 152: moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest.start_date:type_name -> google.type.Date
	135, // 153: moego.service.online_booking.v1.AutoAssignResponse.BoardingService.missing_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	135, // 154: moego.service.online_booking.v1.AutoAssignResponse.EvaluationService.selected_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	135, // 155: moego.service.online_booking.v1.AutoAssignResponse.EvaluationService.all_evaluations:type_name -> moego.models.offering.v1.EvaluationBriefView
	135, // 156: moego.service.online_booking.v1.AutoAssignResponse.DaycareService.missing_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	136, // 157: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem.service:type_name -> moego.models.offering.v1.CustomizedServiceView
	137, // 158: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem.unit_price:type_name -> google.type.Money
	137, // 159: moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem.total_price:type_name -> google.type.Money
	0,   // 160: moego.service.online_booking.v1.BookingRequestService.CreateBookingRequest:input_type -> moego.service.online_booking.v1.CreateBookingRequestRequest
	1,   // 161: moego.service.online_booking.v1.BookingRequestService.GetBookingRequest:input_type -> moego.service.online_booking.v1.GetBookingRequestRequest
	3,   // 162: moego.service.online_booking.v1.BookingRequestService.ListBookingRequests:input_type -> moego.service.online_booking.v1.ListBookingRequestsRequest
	5,   // 163: moego.service.online_booking.v1.BookingRequestService.ListWaitlists:input_type -> moego.service.online_booking.v1.ListWaitlistsRequest
	8,   // 164: moego.service.online_booking.v1.BookingRequestService.CreateGroomingOnly:input_type -> moego.service.online_booking.v1.CreateGroomingOnlyRequest
	10,  // 165: moego.service.online_booking.v1.BookingRequestService.UpdateBookingRequestStatus:input_type -> moego.service.online_booking.v1.UpdateBookingRequestStatusRequest
	14,  // 166: moego.service.online_booking.v1.BookingRequestService.UpdateBookingRequest:input_type -> moego.service.online_booking.v1.UpdateBookingRequestRequest
	16,  // 167: moego.service.online_booking.v1.BookingRequestService.ReplaceBookingRequest:input_type -> moego.service.online_booking.v1.ReplaceBookingRequestRequest
	12,  // 168: moego.service.online_booking.v1.BookingRequestService.RetryFailedEvents:input_type -> moego.service.online_booking.v1.RetryFailedEventsRequest
	18,  // 169: moego.service.online_booking.v1.BookingRequestService.GetAutoAssign:input_type -> moego.service.online_booking.v1.GetAutoAssignRequest
	34,  // 170: moego.service.online_booking.v1.BookingRequestService.AutoAssign:input_type -> moego.service.online_booking.v1.AutoAssignRequest
	20,  // 171: moego.service.online_booking.v1.BookingRequestService.AcceptBookingRequest:input_type -> moego.service.online_booking.v1.AcceptBookingRequestRequest
	32,  // 172: moego.service.online_booking.v1.BookingRequestService.AcceptBookingRequestV2:input_type -> moego.service.online_booking.v1.AcceptBookingRequestV2Request
	22,  // 173: moego.service.online_booking.v1.BookingRequestService.DeclineBookingRequest:input_type -> moego.service.online_booking.v1.DeclineBookingRequestRequest
	24,  // 174: moego.service.online_booking.v1.BookingRequestService.CountBookingRequests:input_type -> moego.service.online_booking.v1.CountBookingRequestsRequest
	26,  // 175: moego.service.online_booking.v1.BookingRequestService.ListBookingRequestId:input_type -> moego.service.online_booking.v1.ListBookingRequestIdRequest
	28,  // 176: moego.service.online_booking.v1.BookingRequestService.SyncBookingRequestFromAppointment:input_type -> moego.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest
	30,  // 177: moego.service.online_booking.v1.BookingRequestService.TriggerBookingRequestAutoAccepted:input_type -> moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest
	40,  // 178: moego.service.online_booking.v1.BookingRequestService.CheckWaitlistAvailableTask:input_type -> moego.service.online_booking.v1.CheckWaitlistAvailableTaskRequest
	42,  // 179: moego.service.online_booking.v1.BookingRequestService.MoveBookingRequestToWaitlist:input_type -> moego.service.online_booking.v1.MoveBookingRequestToWaitlistRequest
	46,  // 180: moego.service.online_booking.v1.BookingRequestService.CountBookingRequestByFilter:input_type -> moego.service.online_booking.v1.CountBookingRequestByFilterRequest
	44,  // 181: moego.service.online_booking.v1.BookingRequestService.PreviewBookingRequestPricing:input_type -> moego.service.online_booking.v1.PreviewBookingRequestPricingRequest
	138, // 182: moego.service.online_booking.v1.BookingRequestService.CreateBookingRequest:output_type -> google.protobuf.Int64Value
	2,   // 183: moego.service.online_booking.v1.BookingRequestService.GetBookingRequest:output_type -> moego.service.online_booking.v1.GetBookingRequestResponse
	4,   // 184: moego.service.online_booking.v1.BookingRequestService.ListBookingRequests:output_type -> moego.service.online_booking.v1.ListBookingRequestsResponse
	6,   // 185: moego.service.online_booking.v1.BookingRequestService.ListWaitlists:output_type -> moego.service.online_booking.v1.ListWaitlistsResponse
	9,   // 186: moego.service.online_booking.v1.BookingRequestService.CreateGroomingOnly:output_type -> moego.service.online_booking.v1.CreateGroomingOnlyResponse
	11,  // 187: moego.service.online_booking.v1.BookingRequestService.UpdateBookingRequestStatus:output_type -> moego.service.online_booking.v1.UpdateBookingRequestStatusResponse
	15,  // 188: moego.service.online_booking.v1.BookingRequestService.UpdateBookingRequest:output_type -> moego.service.online_booking.v1.UpdateBookingRequestResponse
	17,  // 189: moego.service.online_booking.v1.BookingRequestService.ReplaceBookingRequest:output_type -> moego.service.online_booking.v1.ReplaceBookingRequestResponse
	13,  // 190: moego.service.online_booking.v1.BookingRequestService.RetryFailedEvents:output_type -> moego.service.online_booking.v1.RetryFailedEventsResponse
	19,  // 191: moego.service.online_booking.v1.BookingRequestService.GetAutoAssign:output_type -> moego.service.online_booking.v1.GetAutoAssignResponse
	35,  // 192: moego.service.online_booking.v1.BookingRequestService.AutoAssign:output_type -> moego.service.online_booking.v1.AutoAssignResponse
	21,  // 193: moego.service.online_booking.v1.BookingRequestService.AcceptBookingRequest:output_type -> moego.service.online_booking.v1.AcceptBookingRequestResponse
	33,  // 194: moego.service.online_booking.v1.BookingRequestService.AcceptBookingRequestV2:output_type -> moego.service.online_booking.v1.AcceptBookingRequestV2Response
	23,  // 195: moego.service.online_booking.v1.BookingRequestService.DeclineBookingRequest:output_type -> moego.service.online_booking.v1.DeclineBookingRequestResponse
	25,  // 196: moego.service.online_booking.v1.BookingRequestService.CountBookingRequests:output_type -> moego.service.online_booking.v1.CountBookingRequestsResponse
	27,  // 197: moego.service.online_booking.v1.BookingRequestService.ListBookingRequestId:output_type -> moego.service.online_booking.v1.ListBookingRequestIdResponse
	29,  // 198: moego.service.online_booking.v1.BookingRequestService.SyncBookingRequestFromAppointment:output_type -> moego.service.online_booking.v1.SyncBookingRequestFromAppointmentResponse
	31,  // 199: moego.service.online_booking.v1.BookingRequestService.TriggerBookingRequestAutoAccepted:output_type -> moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedResponse
	41,  // 200: moego.service.online_booking.v1.BookingRequestService.CheckWaitlistAvailableTask:output_type -> moego.service.online_booking.v1.CheckWaitlistAvailableTaskResponse
	43,  // 201: moego.service.online_booking.v1.BookingRequestService.MoveBookingRequestToWaitlist:output_type -> moego.service.online_booking.v1.MoveBookingRequestToWaitlistResponse
	47,  // 202: moego.service.online_booking.v1.BookingRequestService.CountBookingRequestByFilter:output_type -> moego.service.online_booking.v1.CountBookingRequestByFilterResponse
	45,  // 203: moego.service.online_booking.v1.BookingRequestService.PreviewBookingRequestPricing:output_type -> moego.service.online_booking.v1.PreviewBookingRequestPricingResponse
	182, // [182:204] is the sub-list for method output_type
	160, // [160:182] is the sub-list for method input_type
	160, // [160:160] is the sub-list for extension type_name
	160, // [160:160] is the sub-list for extension extendee
	0,   // [0:160] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_booking_request_service_proto_init() }
func file_moego_service_online_booking_v1_booking_request_service_proto_init() {
	if File_moego_service_online_booking_v1_booking_request_service_proto != nil {
		return
	}
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_init()
	file_moego_service_online_booking_v1_boarding_service_detail_service_proto_init()
	file_moego_service_online_booking_v1_daycare_add_on_detail_service_proto_init()
	file_moego_service_online_booking_v1_daycare_service_detail_service_proto_init()
	file_moego_service_online_booking_v1_dog_walking_detail_service_proto_init()
	file_moego_service_online_booking_v1_evaluation_test_detail_service_proto_init()
	file_moego_service_online_booking_v1_feeding_service_proto_init()
	file_moego_service_online_booking_v1_grooming_add_on_detail_service_proto_init()
	file_moego_service_online_booking_v1_grooming_auto_assign_service_proto_init()
	file_moego_service_online_booking_v1_grooming_service_detail_service_proto_init()
	file_moego_service_online_booking_v1_group_class_service_detail_service_proto_init()
	file_moego_service_online_booking_v1_medication_service_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingRequestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingRequestsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWaitlistsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWaitlistsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGroomingOnlyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGroomingOnlyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryFailedEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryFailedEventsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReplaceBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReplaceBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoAssignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoAssignResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountBookingRequestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountBookingRequestsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingRequestIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingRequestIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBookingRequestFromAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBookingRequestFromAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerBookingRequestAutoAcceptedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerBookingRequestAutoAcceptedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBoardingServiceWaitlistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDaycareServiceWaitlistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingServiceWaitlistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDaycareServiceWaitlistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckWaitlistAvailableTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckWaitlistAvailableTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveBookingRequestToWaitlistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveBookingRequestToWaitlistResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewBookingRequestPricingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewBookingRequestPricingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountBookingRequestByFilterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountBookingRequestByFilterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_GroomingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_EvaluationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_DogWalkingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_GroupClassService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest_Membership); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_ServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_GroomingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_ServiceDetail_Delete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_GroomingService_Addon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_GroomingService_Addon_Add); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_GroomingService_Addon_Update); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_GroomingService_Addon_Delete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_BoardingService_Addon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_BoardingService_Addon_Add); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_BoardingService_Addon_Update); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_BoardingService_Addon_Delete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_DaycareService_Addon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_DaycareService_Addon_Add); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_DaycareService_Addon_Update); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest_DaycareService_Addon_Delete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReplaceBookingRequestRequest_Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReplaceBookingRequestRequest_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReplaceBookingRequestRequest_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoAssignResponse_AssignRequire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutoAssignResponse_LodgingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountBookingRequestsRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_GroomingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_EvaluationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_GroomingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_BoardingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_DaycareAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequestV2Request_CreateEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_EvaluationService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoAssignResponse_DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewBookingRequestPricingResponse_LineItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[34].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[38].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[39].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[42].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[44].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[48].OneofWrappers = []interface{}{
		(*CreateBookingRequestRequest_Service_Grooming)(nil),
		(*CreateBookingRequestRequest_Service_Boarding)(nil),
		(*CreateBookingRequestRequest_Service_Daycare)(nil),
		(*CreateBookingRequestRequest_Service_Evaluation)(nil),
		(*CreateBookingRequestRequest_Service_DogWalking)(nil),
		(*CreateBookingRequestRequest_Service_GroupClass)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[50].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[51].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[56].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_Service_Grooming)(nil),
		(*UpdateBookingRequestRequest_Service_Boarding)(nil),
		(*UpdateBookingRequestRequest_Service_Daycare)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[57].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_ServiceDetail_Add)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Update)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[58].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[59].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[60].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[61].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_Grooming)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_Boarding)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_Daycare)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_Evaluation)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_DogWalking)(nil),
		(*UpdateBookingRequestRequest_ServiceDetail_Delete_GroupClass)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[62].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_GroomingService_Addon_Add_)(nil),
		(*UpdateBookingRequestRequest_GroomingService_Addon_Update_)(nil),
		(*UpdateBookingRequestRequest_GroomingService_Addon_Delete_)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[66].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_BoardingService_Addon_Add_)(nil),
		(*UpdateBookingRequestRequest_BoardingService_Addon_Update_)(nil),
		(*UpdateBookingRequestRequest_BoardingService_Addon_Delete_)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[70].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_DaycareService_Addon_Add_)(nil),
		(*UpdateBookingRequestRequest_DaycareService_Addon_Update_)(nil),
		(*UpdateBookingRequestRequest_DaycareService_Addon_Delete_)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[74].OneofWrappers = []interface{}{
		(*ReplaceBookingRequestRequest_Service_Boarding)(nil),
		(*ReplaceBookingRequestRequest_Service_Daycare)(nil),
	}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[75].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[76].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[77].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[79].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[81].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[82].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[83].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[84].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[85].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[86].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[87].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[88].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[89].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[90].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes[91].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_booking_request_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   94,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_booking_request_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_booking_request_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_booking_request_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_booking_request_service_proto = out.File
	file_moego_service_online_booking_v1_booking_request_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_booking_request_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_booking_request_service_proto_depIdxs = nil
}
