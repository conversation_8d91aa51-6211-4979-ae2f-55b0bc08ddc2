// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_customer_contact_service.proto

package businesscustomersvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get customer primary contact request
type GetCustomerPrimaryContactRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// tenant, optional
	Tenant *v1.Tenant `protobuf:"bytes,3,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *GetCustomerPrimaryContactRequest) Reset() {
	*x = GetCustomerPrimaryContactRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerPrimaryContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerPrimaryContactRequest) ProtoMessage() {}

func (x *GetCustomerPrimaryContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerPrimaryContactRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerPrimaryContactRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Do not use.
func (x *GetCustomerPrimaryContactRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCustomerPrimaryContactRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *GetCustomerPrimaryContactRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// get customer primary contact response
type GetCustomerPrimaryContactResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer's primary contact.
	// It should exist in most cases. In extreme cases (e.g.: imported by DM), it may not exist.
	Contact *v11.BusinessCustomerContactModel `protobuf:"bytes,1,opt,name=contact,proto3,oneof" json:"contact,omitempty"`
}

func (x *GetCustomerPrimaryContactResponse) Reset() {
	*x = GetCustomerPrimaryContactResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerPrimaryContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerPrimaryContactResponse) ProtoMessage() {}

func (x *GetCustomerPrimaryContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerPrimaryContactResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerPrimaryContactResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerPrimaryContactResponse) GetContact() *v11.BusinessCustomerContactModel {
	if x != nil {
		return x.Contact
	}
	return nil
}

// list customer contact request
type ListCustomerContactRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// tenant, optional if customer_id is set, otherwise required
	Tenant *v1.Tenant `protobuf:"bytes,3,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
	// customer id, optional
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// phone number, optional
	// if not empty, list customer with the phone number (main contact)
	PhoneNumber *string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// contact type, optional
	// if not set, both main and additional contacts will be included
	ContactType *ListCustomerContactRequest_ContactType `protobuf:"bytes,5,opt,name=contact_type,json=contactType,proto3,oneof" json:"contact_type,omitempty"`
	// pagination, required
	Pagination *v2.PaginationRequest `protobuf:"bytes,10,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCustomerContactRequest) Reset() {
	*x = ListCustomerContactRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerContactRequest) ProtoMessage() {}

func (x *ListCustomerContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerContactRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerContactRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{2}
}

// Deprecated: Do not use.
func (x *ListCustomerContactRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCustomerContactRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *ListCustomerContactRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListCustomerContactRequest) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *ListCustomerContactRequest) GetContactType() *ListCustomerContactRequest_ContactType {
	if x != nil {
		return x.ContactType
	}
	return nil
}

func (x *ListCustomerContactRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list customer contact response
type ListCustomerContactResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contacts
	Contacts []*v11.BusinessCustomerContactModel `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCustomerContactResponse) Reset() {
	*x = ListCustomerContactResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerContactResponse) ProtoMessage() {}

func (x *ListCustomerContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerContactResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerContactResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListCustomerContactResponse) GetContacts() []*v11.BusinessCustomerContactModel {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *ListCustomerContactResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list customer contact by phone number request
type ListCustomerContactByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number, required
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// tenant, optional
	Tenant *v1.Tenant `protobuf:"bytes,2,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
}

func (x *ListCustomerContactByPhoneNumberRequest) Reset() {
	*x = ListCustomerContactByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerContactByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerContactByPhoneNumberRequest) ProtoMessage() {}

func (x *ListCustomerContactByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerContactByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerContactByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCustomerContactByPhoneNumberRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ListCustomerContactByPhoneNumberRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// list customer contact by phone number response
type ListCustomerContactByPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contacts
	Contacts []*v11.BusinessCustomerContactModel `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *ListCustomerContactByPhoneNumberResponse) Reset() {
	*x = ListCustomerContactByPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerContactByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerContactByPhoneNumberResponse) ProtoMessage() {}

func (x *ListCustomerContactByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerContactByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerContactByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCustomerContactByPhoneNumberResponse) GetContacts() []*v11.BusinessCustomerContactModel {
	if x != nil {
		return x.Contacts
	}
	return nil
}

// fill up E164 phone number request
type FillE164PhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// e164 phone number def
	E164Defs []*FillE164PhoneNumberRequest_E164Def `protobuf:"bytes,1,rep,name=e164_defs,json=e164Defs,proto3" json:"e164_defs,omitempty"`
}

func (x *FillE164PhoneNumberRequest) Reset() {
	*x = FillE164PhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FillE164PhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FillE164PhoneNumberRequest) ProtoMessage() {}

func (x *FillE164PhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FillE164PhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*FillE164PhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{6}
}

func (x *FillE164PhoneNumberRequest) GetE164Defs() []*FillE164PhoneNumberRequest_E164Def {
	if x != nil {
		return x.E164Defs
	}
	return nil
}

// fill up E164 phone number response
type FillE164PhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer contacts
	Contacts []*v11.BusinessCustomerContactModel `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *FillE164PhoneNumberResponse) Reset() {
	*x = FillE164PhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FillE164PhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FillE164PhoneNumberResponse) ProtoMessage() {}

func (x *FillE164PhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FillE164PhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*FillE164PhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{7}
}

func (x *FillE164PhoneNumberResponse) GetContacts() []*v11.BusinessCustomerContactModel {
	if x != nil {
		return x.Contacts
	}
	return nil
}

// contact type
type ListCustomerContactRequest_ContactType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// include main contact, default is true
	Main *bool `protobuf:"varint,1,opt,name=main,proto3,oneof" json:"main,omitempty"`
	// include additional contact, default is true
	Additional *bool `protobuf:"varint,2,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
}

func (x *ListCustomerContactRequest_ContactType) Reset() {
	*x = ListCustomerContactRequest_ContactType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerContactRequest_ContactType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerContactRequest_ContactType) ProtoMessage() {}

func (x *ListCustomerContactRequest_ContactType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerContactRequest_ContactType.ProtoReflect.Descriptor instead.
func (*ListCustomerContactRequest_ContactType) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListCustomerContactRequest_ContactType) GetMain() bool {
	if x != nil && x.Main != nil {
		return *x.Main
	}
	return false
}

func (x *ListCustomerContactRequest_ContactType) GetAdditional() bool {
	if x != nil && x.Additional != nil {
		return *x.Additional
	}
	return false
}

// e164 phone number def
type FillE164PhoneNumberRequest_E164Def struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer contact id, required
	CustomerContactId int64 `protobuf:"varint,1,opt,name=customer_contact_id,json=customerContactId,proto3" json:"customer_contact_id,omitempty"`
	// phone number, required
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *FillE164PhoneNumberRequest_E164Def) Reset() {
	*x = FillE164PhoneNumberRequest_E164Def{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FillE164PhoneNumberRequest_E164Def) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FillE164PhoneNumberRequest_E164Def) ProtoMessage() {}

func (x *FillE164PhoneNumberRequest_E164Def) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FillE164PhoneNumberRequest_E164Def.ProtoReflect.Descriptor instead.
func (*FillE164PhoneNumberRequest_E164Def) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *FillE164PhoneNumberRequest_E164Def) GetCustomerContactId() int64 {
	if x != nil {
		return x.CustomerContactId
	}
	return 0
}

func (x *FillE164PhoneNumberRequest_E164Def) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

var File_moego_service_business_customer_v1_business_customer_contact_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x22,
	0x8f, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x22, 0xaf, 0x04, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x48, 0x01, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x72, 0x0a, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x63, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x17, 0x0a, 0x04, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x04, 0x6d, 0x61, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52,
	0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa5, 0x01, 0x0a, 0x27, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41,
	0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x22, 0x87, 0x01, 0x0a,
	0x28, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x22, 0xf3, 0x01, 0x0a, 0x1a, 0x46, 0x69, 0x6c, 0x6c, 0x45,
	0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x63, 0x0a, 0x09, 0x65, 0x31, 0x36, 0x34, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6c, 0x6c, 0x45, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x31, 0x36, 0x34, 0x44, 0x65, 0x66,
	0x52, 0x08, 0x65, 0x31, 0x36, 0x34, 0x44, 0x65, 0x66, 0x73, 0x1a, 0x70, 0x0a, 0x07, 0x45, 0x31,
	0x36, 0x34, 0x44, 0x65, 0x66, 0x12, 0x37, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x1b,
	0x46, 0x69, 0x6c, 0x6c, 0x45, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x32, 0xbd, 0x05, 0x0a, 0x1e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xbd, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x96, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6c, 0x6c, 0x45, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c,
	0x6c, 0x45, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c,
	0x6c, 0x45, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d, 0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x6d, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescData = file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_business_customer_v1_business_customer_contact_service_proto_goTypes = []interface{}{
	(*GetCustomerPrimaryContactRequest)(nil),         // 0: moego.service.business_customer.v1.GetCustomerPrimaryContactRequest
	(*GetCustomerPrimaryContactResponse)(nil),        // 1: moego.service.business_customer.v1.GetCustomerPrimaryContactResponse
	(*ListCustomerContactRequest)(nil),               // 2: moego.service.business_customer.v1.ListCustomerContactRequest
	(*ListCustomerContactResponse)(nil),              // 3: moego.service.business_customer.v1.ListCustomerContactResponse
	(*ListCustomerContactByPhoneNumberRequest)(nil),  // 4: moego.service.business_customer.v1.ListCustomerContactByPhoneNumberRequest
	(*ListCustomerContactByPhoneNumberResponse)(nil), // 5: moego.service.business_customer.v1.ListCustomerContactByPhoneNumberResponse
	(*FillE164PhoneNumberRequest)(nil),               // 6: moego.service.business_customer.v1.FillE164PhoneNumberRequest
	(*FillE164PhoneNumberResponse)(nil),              // 7: moego.service.business_customer.v1.FillE164PhoneNumberResponse
	(*ListCustomerContactRequest_ContactType)(nil),   // 8: moego.service.business_customer.v1.ListCustomerContactRequest.ContactType
	(*FillE164PhoneNumberRequest_E164Def)(nil),       // 9: moego.service.business_customer.v1.FillE164PhoneNumberRequest.E164Def
	(*v1.Tenant)(nil),                                // 10: moego.models.organization.v1.Tenant
	(*v11.BusinessCustomerContactModel)(nil),         // 11: moego.models.business_customer.v1.BusinessCustomerContactModel
	(*v2.PaginationRequest)(nil),                     // 12: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                    // 13: moego.utils.v2.PaginationResponse
}
var file_moego_service_business_customer_v1_business_customer_contact_service_proto_depIdxs = []int32{
	10, // 0: moego.service.business_customer.v1.GetCustomerPrimaryContactRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	11, // 1: moego.service.business_customer.v1.GetCustomerPrimaryContactResponse.contact:type_name -> moego.models.business_customer.v1.BusinessCustomerContactModel
	10, // 2: moego.service.business_customer.v1.ListCustomerContactRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	8,  // 3: moego.service.business_customer.v1.ListCustomerContactRequest.contact_type:type_name -> moego.service.business_customer.v1.ListCustomerContactRequest.ContactType
	12, // 4: moego.service.business_customer.v1.ListCustomerContactRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	11, // 5: moego.service.business_customer.v1.ListCustomerContactResponse.contacts:type_name -> moego.models.business_customer.v1.BusinessCustomerContactModel
	13, // 6: moego.service.business_customer.v1.ListCustomerContactResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	10, // 7: moego.service.business_customer.v1.ListCustomerContactByPhoneNumberRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	11, // 8: moego.service.business_customer.v1.ListCustomerContactByPhoneNumberResponse.contacts:type_name -> moego.models.business_customer.v1.BusinessCustomerContactModel
	9,  // 9: moego.service.business_customer.v1.FillE164PhoneNumberRequest.e164_defs:type_name -> moego.service.business_customer.v1.FillE164PhoneNumberRequest.E164Def
	11, // 10: moego.service.business_customer.v1.FillE164PhoneNumberResponse.contacts:type_name -> moego.models.business_customer.v1.BusinessCustomerContactModel
	0,  // 11: moego.service.business_customer.v1.BusinessCustomerContactService.GetCustomerPrimaryContact:input_type -> moego.service.business_customer.v1.GetCustomerPrimaryContactRequest
	2,  // 12: moego.service.business_customer.v1.BusinessCustomerContactService.ListCustomerContact:input_type -> moego.service.business_customer.v1.ListCustomerContactRequest
	4,  // 13: moego.service.business_customer.v1.BusinessCustomerContactService.ListCustomerContactByPhoneNumber:input_type -> moego.service.business_customer.v1.ListCustomerContactByPhoneNumberRequest
	6,  // 14: moego.service.business_customer.v1.BusinessCustomerContactService.FillE164PhoneNumber:input_type -> moego.service.business_customer.v1.FillE164PhoneNumberRequest
	1,  // 15: moego.service.business_customer.v1.BusinessCustomerContactService.GetCustomerPrimaryContact:output_type -> moego.service.business_customer.v1.GetCustomerPrimaryContactResponse
	3,  // 16: moego.service.business_customer.v1.BusinessCustomerContactService.ListCustomerContact:output_type -> moego.service.business_customer.v1.ListCustomerContactResponse
	5,  // 17: moego.service.business_customer.v1.BusinessCustomerContactService.ListCustomerContactByPhoneNumber:output_type -> moego.service.business_customer.v1.ListCustomerContactByPhoneNumberResponse
	7,  // 18: moego.service.business_customer.v1.BusinessCustomerContactService.FillE164PhoneNumber:output_type -> moego.service.business_customer.v1.FillE164PhoneNumberResponse
	15, // [15:19] is the sub-list for method output_type
	11, // [11:15] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_customer_contact_service_proto_init() }
func file_moego_service_business_customer_v1_business_customer_contact_service_proto_init() {
	if File_moego_service_business_customer_v1_business_customer_contact_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerPrimaryContactRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerPrimaryContactResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerContactRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerContactResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerContactByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerContactByPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FillE164PhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FillE164PhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerContactRequest_ContactType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FillE164PhoneNumberRequest_E164Def); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_customer_contact_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_customer_contact_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_customer_contact_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_customer_contact_service_proto = out.File
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_customer_contact_service_proto_depIdxs = nil
}
