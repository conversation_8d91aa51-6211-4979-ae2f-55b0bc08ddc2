// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/service_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServiceManagementServiceClient is the client API for ServiceManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceManagementServiceClient interface {
	// create service, only for b-setting
	CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error)
	// update service, only for b-setting
	UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error)
	// get service list, only for b-setting
	GetServiceList(ctx context.Context, in *GetServiceListRequest, opts ...grpc.CallOption) (*GetServiceListResponse, error)
	// get service by pet and service id
	GetServiceByPetAndServiceId(ctx context.Context, in *GetServiceByPetAndServiceIdRequest, opts ...grpc.CallOption) (*GetServiceByPetAndServiceIdResponse, error)
	// override service
	OverrideService(ctx context.Context, in *OverrideServiceRequest, opts ...grpc.CallOption) (*OverrideServiceResponse, error)
	// get applicable service list
	GetApplicableServiceList(ctx context.Context, in *GetApplicableServiceListRequest, opts ...grpc.CallOption) (*GetApplicableServiceListResponse, error)
	// get customized service by pet
	CustomizedServiceByPet(ctx context.Context, in *CustomizedServiceByPetRequest, opts ...grpc.CallOption) (*CustomizedServiceByPetResponse, error)
	// get service detail, only for b-setting
	GetServiceDetail(ctx context.Context, in *GetServiceDetailRequest, opts ...grpc.CallOption) (*GetServiceDetailResponse, error)
	// get service list by ids
	GetServiceListByIds(ctx context.Context, in *GetServiceListByIdsRequest, opts ...grpc.CallOption) (*GetServiceListByIdsResponse, error)
	// remove service filter
	RemoveServiceFilter(ctx context.Context, in *RemoveServiceFilterRequest, opts ...grpc.CallOption) (*RemoveServiceFilterResponse, error)
	// get all service item types by services
	GetServiceItemTypes(ctx context.Context, in *GetServiceItemTypesRequest, opts ...grpc.CallOption) (*GetServiceItemTypesResponse, error)
	// list service
	ListService(ctx context.Context, in *ListServiceRequest, opts ...grpc.CallOption) (*ListServiceResponse, error)
	// batch get customized service
	BatchGetCustomizedService(ctx context.Context, in *BatchGetCustomizedServiceRequest, opts ...grpc.CallOption) (*BatchGetCustomizedServiceResponse, error)
	// List available staff ids for service
	ListAvailableStaffId(ctx context.Context, in *ListAvailableStaffIdRequest, opts ...grpc.CallOption) (*ListAvailableStaffIdResponse, error)
	// list bundle services
	ListBundleServices(ctx context.Context, in *ListBundleServicesRequest, opts ...grpc.CallOption) (*ListBundleServicesResponse, error)
	// list categories
	ListCategories(ctx context.Context, in *ListCategoriesRequest, opts ...grpc.CallOption) (*ListCategoriesResponse, error)
	// create categories
	CreateCategories(ctx context.Context, in *CreateCategoriesRequest, opts ...grpc.CallOption) (*CreateCategoriesResponse, error)
	// Get max service price by lodging type
	GetMaxServicePriceByLodgingType(ctx context.Context, in *GetMaxServicePriceByLodgingTypeRequest, opts ...grpc.CallOption) (*GetMaxServicePriceByLodgingTypeResponse, error)
}

type serviceManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceManagementServiceClient(cc grpc.ClientConnInterface) ServiceManagementServiceClient {
	return &serviceManagementServiceClient{cc}
}

func (c *serviceManagementServiceClient) CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error) {
	out := new(CreateServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/CreateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error) {
	out := new(UpdateServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/UpdateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetServiceList(ctx context.Context, in *GetServiceListRequest, opts ...grpc.CallOption) (*GetServiceListResponse, error) {
	out := new(GetServiceListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetServiceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetServiceByPetAndServiceId(ctx context.Context, in *GetServiceByPetAndServiceIdRequest, opts ...grpc.CallOption) (*GetServiceByPetAndServiceIdResponse, error) {
	out := new(GetServiceByPetAndServiceIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetServiceByPetAndServiceId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) OverrideService(ctx context.Context, in *OverrideServiceRequest, opts ...grpc.CallOption) (*OverrideServiceResponse, error) {
	out := new(OverrideServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/OverrideService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetApplicableServiceList(ctx context.Context, in *GetApplicableServiceListRequest, opts ...grpc.CallOption) (*GetApplicableServiceListResponse, error) {
	out := new(GetApplicableServiceListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetApplicableServiceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) CustomizedServiceByPet(ctx context.Context, in *CustomizedServiceByPetRequest, opts ...grpc.CallOption) (*CustomizedServiceByPetResponse, error) {
	out := new(CustomizedServiceByPetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/CustomizedServiceByPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetServiceDetail(ctx context.Context, in *GetServiceDetailRequest, opts ...grpc.CallOption) (*GetServiceDetailResponse, error) {
	out := new(GetServiceDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetServiceDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetServiceListByIds(ctx context.Context, in *GetServiceListByIdsRequest, opts ...grpc.CallOption) (*GetServiceListByIdsResponse, error) {
	out := new(GetServiceListByIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetServiceListByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) RemoveServiceFilter(ctx context.Context, in *RemoveServiceFilterRequest, opts ...grpc.CallOption) (*RemoveServiceFilterResponse, error) {
	out := new(RemoveServiceFilterResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/RemoveServiceFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetServiceItemTypes(ctx context.Context, in *GetServiceItemTypesRequest, opts ...grpc.CallOption) (*GetServiceItemTypesResponse, error) {
	out := new(GetServiceItemTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetServiceItemTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) ListService(ctx context.Context, in *ListServiceRequest, opts ...grpc.CallOption) (*ListServiceResponse, error) {
	out := new(ListServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/ListService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) BatchGetCustomizedService(ctx context.Context, in *BatchGetCustomizedServiceRequest, opts ...grpc.CallOption) (*BatchGetCustomizedServiceResponse, error) {
	out := new(BatchGetCustomizedServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/BatchGetCustomizedService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) ListAvailableStaffId(ctx context.Context, in *ListAvailableStaffIdRequest, opts ...grpc.CallOption) (*ListAvailableStaffIdResponse, error) {
	out := new(ListAvailableStaffIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/ListAvailableStaffId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) ListBundleServices(ctx context.Context, in *ListBundleServicesRequest, opts ...grpc.CallOption) (*ListBundleServicesResponse, error) {
	out := new(ListBundleServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/ListBundleServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) ListCategories(ctx context.Context, in *ListCategoriesRequest, opts ...grpc.CallOption) (*ListCategoriesResponse, error) {
	out := new(ListCategoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/ListCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) CreateCategories(ctx context.Context, in *CreateCategoriesRequest, opts ...grpc.CallOption) (*CreateCategoriesResponse, error) {
	out := new(CreateCategoriesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/CreateCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceManagementServiceClient) GetMaxServicePriceByLodgingType(ctx context.Context, in *GetMaxServicePriceByLodgingTypeRequest, opts ...grpc.CallOption) (*GetMaxServicePriceByLodgingTypeResponse, error) {
	out := new(GetMaxServicePriceByLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.ServiceManagementService/GetMaxServicePriceByLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceManagementServiceServer is the server API for ServiceManagementService service.
// All implementations must embed UnimplementedServiceManagementServiceServer
// for forward compatibility
type ServiceManagementServiceServer interface {
	// create service, only for b-setting
	CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error)
	// update service, only for b-setting
	UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error)
	// get service list, only for b-setting
	GetServiceList(context.Context, *GetServiceListRequest) (*GetServiceListResponse, error)
	// get service by pet and service id
	GetServiceByPetAndServiceId(context.Context, *GetServiceByPetAndServiceIdRequest) (*GetServiceByPetAndServiceIdResponse, error)
	// override service
	OverrideService(context.Context, *OverrideServiceRequest) (*OverrideServiceResponse, error)
	// get applicable service list
	GetApplicableServiceList(context.Context, *GetApplicableServiceListRequest) (*GetApplicableServiceListResponse, error)
	// get customized service by pet
	CustomizedServiceByPet(context.Context, *CustomizedServiceByPetRequest) (*CustomizedServiceByPetResponse, error)
	// get service detail, only for b-setting
	GetServiceDetail(context.Context, *GetServiceDetailRequest) (*GetServiceDetailResponse, error)
	// get service list by ids
	GetServiceListByIds(context.Context, *GetServiceListByIdsRequest) (*GetServiceListByIdsResponse, error)
	// remove service filter
	RemoveServiceFilter(context.Context, *RemoveServiceFilterRequest) (*RemoveServiceFilterResponse, error)
	// get all service item types by services
	GetServiceItemTypes(context.Context, *GetServiceItemTypesRequest) (*GetServiceItemTypesResponse, error)
	// list service
	ListService(context.Context, *ListServiceRequest) (*ListServiceResponse, error)
	// batch get customized service
	BatchGetCustomizedService(context.Context, *BatchGetCustomizedServiceRequest) (*BatchGetCustomizedServiceResponse, error)
	// List available staff ids for service
	ListAvailableStaffId(context.Context, *ListAvailableStaffIdRequest) (*ListAvailableStaffIdResponse, error)
	// list bundle services
	ListBundleServices(context.Context, *ListBundleServicesRequest) (*ListBundleServicesResponse, error)
	// list categories
	ListCategories(context.Context, *ListCategoriesRequest) (*ListCategoriesResponse, error)
	// create categories
	CreateCategories(context.Context, *CreateCategoriesRequest) (*CreateCategoriesResponse, error)
	// Get max service price by lodging type
	GetMaxServicePriceByLodgingType(context.Context, *GetMaxServicePriceByLodgingTypeRequest) (*GetMaxServicePriceByLodgingTypeResponse, error)
	mustEmbedUnimplementedServiceManagementServiceServer()
}

// UnimplementedServiceManagementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceManagementServiceServer struct {
}

func (UnimplementedServiceManagementServiceServer) CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedServiceManagementServiceServer) UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetServiceList(context.Context, *GetServiceListRequest) (*GetServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceList not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetServiceByPetAndServiceId(context.Context, *GetServiceByPetAndServiceIdRequest) (*GetServiceByPetAndServiceIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceByPetAndServiceId not implemented")
}
func (UnimplementedServiceManagementServiceServer) OverrideService(context.Context, *OverrideServiceRequest) (*OverrideServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OverrideService not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetApplicableServiceList(context.Context, *GetApplicableServiceListRequest) (*GetApplicableServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicableServiceList not implemented")
}
func (UnimplementedServiceManagementServiceServer) CustomizedServiceByPet(context.Context, *CustomizedServiceByPetRequest) (*CustomizedServiceByPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CustomizedServiceByPet not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetServiceDetail(context.Context, *GetServiceDetailRequest) (*GetServiceDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceDetail not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetServiceListByIds(context.Context, *GetServiceListByIdsRequest) (*GetServiceListByIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceListByIds not implemented")
}
func (UnimplementedServiceManagementServiceServer) RemoveServiceFilter(context.Context, *RemoveServiceFilterRequest) (*RemoveServiceFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveServiceFilter not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetServiceItemTypes(context.Context, *GetServiceItemTypesRequest) (*GetServiceItemTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceItemTypes not implemented")
}
func (UnimplementedServiceManagementServiceServer) ListService(context.Context, *ListServiceRequest) (*ListServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListService not implemented")
}
func (UnimplementedServiceManagementServiceServer) BatchGetCustomizedService(context.Context, *BatchGetCustomizedServiceRequest) (*BatchGetCustomizedServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetCustomizedService not implemented")
}
func (UnimplementedServiceManagementServiceServer) ListAvailableStaffId(context.Context, *ListAvailableStaffIdRequest) (*ListAvailableStaffIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableStaffId not implemented")
}
func (UnimplementedServiceManagementServiceServer) ListBundleServices(context.Context, *ListBundleServicesRequest) (*ListBundleServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBundleServices not implemented")
}
func (UnimplementedServiceManagementServiceServer) ListCategories(context.Context, *ListCategoriesRequest) (*ListCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCategories not implemented")
}
func (UnimplementedServiceManagementServiceServer) CreateCategories(context.Context, *CreateCategoriesRequest) (*CreateCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCategories not implemented")
}
func (UnimplementedServiceManagementServiceServer) GetMaxServicePriceByLodgingType(context.Context, *GetMaxServicePriceByLodgingTypeRequest) (*GetMaxServicePriceByLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaxServicePriceByLodgingType not implemented")
}
func (UnimplementedServiceManagementServiceServer) mustEmbedUnimplementedServiceManagementServiceServer() {
}

// UnsafeServiceManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceManagementServiceServer will
// result in compilation errors.
type UnsafeServiceManagementServiceServer interface {
	mustEmbedUnimplementedServiceManagementServiceServer()
}

func RegisterServiceManagementServiceServer(s grpc.ServiceRegistrar, srv ServiceManagementServiceServer) {
	s.RegisterService(&ServiceManagementService_ServiceDesc, srv)
}

func _ServiceManagementService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/CreateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).CreateService(ctx, req.(*CreateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/UpdateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).UpdateService(ctx, req.(*UpdateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetServiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetServiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetServiceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetServiceList(ctx, req.(*GetServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetServiceByPetAndServiceId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceByPetAndServiceIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetServiceByPetAndServiceId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetServiceByPetAndServiceId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetServiceByPetAndServiceId(ctx, req.(*GetServiceByPetAndServiceIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_OverrideService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverrideServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).OverrideService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/OverrideService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).OverrideService(ctx, req.(*OverrideServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetApplicableServiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicableServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetApplicableServiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetApplicableServiceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetApplicableServiceList(ctx, req.(*GetApplicableServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_CustomizedServiceByPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomizedServiceByPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).CustomizedServiceByPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/CustomizedServiceByPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).CustomizedServiceByPet(ctx, req.(*CustomizedServiceByPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetServiceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetServiceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetServiceDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetServiceDetail(ctx, req.(*GetServiceDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetServiceListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceListByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetServiceListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetServiceListByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetServiceListByIds(ctx, req.(*GetServiceListByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_RemoveServiceFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveServiceFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).RemoveServiceFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/RemoveServiceFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).RemoveServiceFilter(ctx, req.(*RemoveServiceFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetServiceItemTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceItemTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetServiceItemTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetServiceItemTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetServiceItemTypes(ctx, req.(*GetServiceItemTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_ListService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).ListService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/ListService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).ListService(ctx, req.(*ListServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_BatchGetCustomizedService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCustomizedServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).BatchGetCustomizedService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/BatchGetCustomizedService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).BatchGetCustomizedService(ctx, req.(*BatchGetCustomizedServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_ListAvailableStaffId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableStaffIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).ListAvailableStaffId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/ListAvailableStaffId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).ListAvailableStaffId(ctx, req.(*ListAvailableStaffIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_ListBundleServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBundleServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).ListBundleServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/ListBundleServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).ListBundleServices(ctx, req.(*ListBundleServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_ListCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).ListCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/ListCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).ListCategories(ctx, req.(*ListCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_CreateCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).CreateCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/CreateCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).CreateCategories(ctx, req.(*CreateCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceManagementService_GetMaxServicePriceByLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaxServicePriceByLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceManagementServiceServer).GetMaxServicePriceByLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.ServiceManagementService/GetMaxServicePriceByLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceManagementServiceServer).GetMaxServicePriceByLodgingType(ctx, req.(*GetMaxServicePriceByLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceManagementService_ServiceDesc is the grpc.ServiceDesc for ServiceManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.ServiceManagementService",
	HandlerType: (*ServiceManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateService",
			Handler:    _ServiceManagementService_CreateService_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _ServiceManagementService_UpdateService_Handler,
		},
		{
			MethodName: "GetServiceList",
			Handler:    _ServiceManagementService_GetServiceList_Handler,
		},
		{
			MethodName: "GetServiceByPetAndServiceId",
			Handler:    _ServiceManagementService_GetServiceByPetAndServiceId_Handler,
		},
		{
			MethodName: "OverrideService",
			Handler:    _ServiceManagementService_OverrideService_Handler,
		},
		{
			MethodName: "GetApplicableServiceList",
			Handler:    _ServiceManagementService_GetApplicableServiceList_Handler,
		},
		{
			MethodName: "CustomizedServiceByPet",
			Handler:    _ServiceManagementService_CustomizedServiceByPet_Handler,
		},
		{
			MethodName: "GetServiceDetail",
			Handler:    _ServiceManagementService_GetServiceDetail_Handler,
		},
		{
			MethodName: "GetServiceListByIds",
			Handler:    _ServiceManagementService_GetServiceListByIds_Handler,
		},
		{
			MethodName: "RemoveServiceFilter",
			Handler:    _ServiceManagementService_RemoveServiceFilter_Handler,
		},
		{
			MethodName: "GetServiceItemTypes",
			Handler:    _ServiceManagementService_GetServiceItemTypes_Handler,
		},
		{
			MethodName: "ListService",
			Handler:    _ServiceManagementService_ListService_Handler,
		},
		{
			MethodName: "BatchGetCustomizedService",
			Handler:    _ServiceManagementService_BatchGetCustomizedService_Handler,
		},
		{
			MethodName: "ListAvailableStaffId",
			Handler:    _ServiceManagementService_ListAvailableStaffId_Handler,
		},
		{
			MethodName: "ListBundleServices",
			Handler:    _ServiceManagementService_ListBundleServices_Handler,
		},
		{
			MethodName: "ListCategories",
			Handler:    _ServiceManagementService_ListCategories_Handler,
		},
		{
			MethodName: "CreateCategories",
			Handler:    _ServiceManagementService_CreateCategories_Handler,
		},
		{
			MethodName: "GetMaxServicePriceByLodgingType",
			Handler:    _ServiceManagementService_GetMaxServicePriceByLodgingType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/service_service.proto",
}
