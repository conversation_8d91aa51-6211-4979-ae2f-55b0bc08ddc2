// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/appointment_note_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentNoteServiceClient is the client API for AppointmentNoteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentNoteServiceClient interface {
	// get appointment note list
	GetAppointmentNoteList(ctx context.Context, in *GetAppointmentNoteListRequest, opts ...grpc.CallOption) (*GetAppointmentNoteListResponse, error)
	// get customer last note
	GetCustomerLastNote(ctx context.Context, in *GetCustomerLastNoteRequest, opts ...grpc.CallOption) (*GetCustomerLastNoteResponse, error)
	// get appointment note
	GetAppointmentNote(ctx context.Context, in *GetAppointmentNoteRequest, opts ...grpc.CallOption) (*GetAppointmentNoteResponse, error)
	// Create appointment note
	CreateAppointmentNote(ctx context.Context, in *CreateAppointmentNoteRequest, opts ...grpc.CallOption) (*CreateAppointmentNoteResponse, error)
	// Update appointment note
	UpdateAppointmentNote(ctx context.Context, in *UpdateAppointmentNoteRequest, opts ...grpc.CallOption) (*UpdateAppointmentNoteResponse, error)
	// List appointment notes
	ListAppointmentNotes(ctx context.Context, in *ListAppointmentNotesRequest, opts ...grpc.CallOption) (*ListAppointmentNotesResponse, error)
}

type appointmentNoteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentNoteServiceClient(cc grpc.ClientConnInterface) AppointmentNoteServiceClient {
	return &appointmentNoteServiceClient{cc}
}

func (c *appointmentNoteServiceClient) GetAppointmentNoteList(ctx context.Context, in *GetAppointmentNoteListRequest, opts ...grpc.CallOption) (*GetAppointmentNoteListResponse, error) {
	out := new(GetAppointmentNoteListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/GetAppointmentNoteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentNoteServiceClient) GetCustomerLastNote(ctx context.Context, in *GetCustomerLastNoteRequest, opts ...grpc.CallOption) (*GetCustomerLastNoteResponse, error) {
	out := new(GetCustomerLastNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/GetCustomerLastNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentNoteServiceClient) GetAppointmentNote(ctx context.Context, in *GetAppointmentNoteRequest, opts ...grpc.CallOption) (*GetAppointmentNoteResponse, error) {
	out := new(GetAppointmentNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/GetAppointmentNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentNoteServiceClient) CreateAppointmentNote(ctx context.Context, in *CreateAppointmentNoteRequest, opts ...grpc.CallOption) (*CreateAppointmentNoteResponse, error) {
	out := new(CreateAppointmentNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/CreateAppointmentNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentNoteServiceClient) UpdateAppointmentNote(ctx context.Context, in *UpdateAppointmentNoteRequest, opts ...grpc.CallOption) (*UpdateAppointmentNoteResponse, error) {
	out := new(UpdateAppointmentNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/UpdateAppointmentNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentNoteServiceClient) ListAppointmentNotes(ctx context.Context, in *ListAppointmentNotesRequest, opts ...grpc.CallOption) (*ListAppointmentNotesResponse, error) {
	out := new(ListAppointmentNotesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.AppointmentNoteService/ListAppointmentNotes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentNoteServiceServer is the server API for AppointmentNoteService service.
// All implementations must embed UnimplementedAppointmentNoteServiceServer
// for forward compatibility
type AppointmentNoteServiceServer interface {
	// get appointment note list
	GetAppointmentNoteList(context.Context, *GetAppointmentNoteListRequest) (*GetAppointmentNoteListResponse, error)
	// get customer last note
	GetCustomerLastNote(context.Context, *GetCustomerLastNoteRequest) (*GetCustomerLastNoteResponse, error)
	// get appointment note
	GetAppointmentNote(context.Context, *GetAppointmentNoteRequest) (*GetAppointmentNoteResponse, error)
	// Create appointment note
	CreateAppointmentNote(context.Context, *CreateAppointmentNoteRequest) (*CreateAppointmentNoteResponse, error)
	// Update appointment note
	UpdateAppointmentNote(context.Context, *UpdateAppointmentNoteRequest) (*UpdateAppointmentNoteResponse, error)
	// List appointment notes
	ListAppointmentNotes(context.Context, *ListAppointmentNotesRequest) (*ListAppointmentNotesResponse, error)
	mustEmbedUnimplementedAppointmentNoteServiceServer()
}

// UnimplementedAppointmentNoteServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentNoteServiceServer struct {
}

func (UnimplementedAppointmentNoteServiceServer) GetAppointmentNoteList(context.Context, *GetAppointmentNoteListRequest) (*GetAppointmentNoteListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentNoteList not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) GetCustomerLastNote(context.Context, *GetCustomerLastNoteRequest) (*GetCustomerLastNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerLastNote not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) GetAppointmentNote(context.Context, *GetAppointmentNoteRequest) (*GetAppointmentNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentNote not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) CreateAppointmentNote(context.Context, *CreateAppointmentNoteRequest) (*CreateAppointmentNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppointmentNote not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) UpdateAppointmentNote(context.Context, *UpdateAppointmentNoteRequest) (*UpdateAppointmentNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentNote not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) ListAppointmentNotes(context.Context, *ListAppointmentNotesRequest) (*ListAppointmentNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentNotes not implemented")
}
func (UnimplementedAppointmentNoteServiceServer) mustEmbedUnimplementedAppointmentNoteServiceServer() {
}

// UnsafeAppointmentNoteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentNoteServiceServer will
// result in compilation errors.
type UnsafeAppointmentNoteServiceServer interface {
	mustEmbedUnimplementedAppointmentNoteServiceServer()
}

func RegisterAppointmentNoteServiceServer(s grpc.ServiceRegistrar, srv AppointmentNoteServiceServer) {
	s.RegisterService(&AppointmentNoteService_ServiceDesc, srv)
}

func _AppointmentNoteService_GetAppointmentNoteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentNoteListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).GetAppointmentNoteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/GetAppointmentNoteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).GetAppointmentNoteList(ctx, req.(*GetAppointmentNoteListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentNoteService_GetCustomerLastNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerLastNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).GetCustomerLastNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/GetCustomerLastNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).GetCustomerLastNote(ctx, req.(*GetCustomerLastNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentNoteService_GetAppointmentNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).GetAppointmentNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/GetAppointmentNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).GetAppointmentNote(ctx, req.(*GetAppointmentNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentNoteService_CreateAppointmentNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppointmentNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).CreateAppointmentNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/CreateAppointmentNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).CreateAppointmentNote(ctx, req.(*CreateAppointmentNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentNoteService_UpdateAppointmentNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).UpdateAppointmentNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/UpdateAppointmentNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).UpdateAppointmentNote(ctx, req.(*UpdateAppointmentNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentNoteService_ListAppointmentNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentNoteServiceServer).ListAppointmentNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.AppointmentNoteService/ListAppointmentNotes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentNoteServiceServer).ListAppointmentNotes(ctx, req.(*ListAppointmentNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentNoteService_ServiceDesc is the grpc.ServiceDesc for AppointmentNoteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentNoteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.AppointmentNoteService",
	HandlerType: (*AppointmentNoteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAppointmentNoteList",
			Handler:    _AppointmentNoteService_GetAppointmentNoteList_Handler,
		},
		{
			MethodName: "GetCustomerLastNote",
			Handler:    _AppointmentNoteService_GetCustomerLastNote_Handler,
		},
		{
			MethodName: "GetAppointmentNote",
			Handler:    _AppointmentNoteService_GetAppointmentNote_Handler,
		},
		{
			MethodName: "CreateAppointmentNote",
			Handler:    _AppointmentNoteService_CreateAppointmentNote_Handler,
		},
		{
			MethodName: "UpdateAppointmentNote",
			Handler:    _AppointmentNoteService_UpdateAppointmentNote_Handler,
		},
		{
			MethodName: "ListAppointmentNotes",
			Handler:    _AppointmentNoteService_ListAppointmentNotes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/appointment_note_service.proto",
}
