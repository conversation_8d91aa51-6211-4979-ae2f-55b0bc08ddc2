// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/todo/v1/todo_service.proto

package todosvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/todo/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/universal/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TodoServiceClient is the client API for TodoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TodoServiceClient interface {
	// 添加 Todo
	AddTodo(ctx context.Context, in *AddTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// 根据 id 获取单个 Todo
	GetTodo(ctx context.Context, in *v11.OwnId, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// 查询 Todo 列表
	ListTodo(ctx context.Context, in *ListTodoRequest, opts ...grpc.CallOption) (*v12.EntityListModel, error)
	// 更新 Todo
	UpdateTodo(ctx context.Context, in *UpdateTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// 删除 Todo
	DeleteTodo(ctx context.Context, in *v11.OwnId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// hello world api by jett
	HelloJett(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloJettResponse, error)
	// echo api by haozhi
	EchoHz(ctx context.Context, in *EchoHzRequest, opts ...grpc.CallOption) (*EchoHzResponse, error)
	// hello world api by ark
	HelloArk(ctx context.Context, in *HelloArkRequest, opts ...grpc.CallOption) (*HelloArkResponse, error)
	// hello world api by better
	HelloBetter(ctx context.Context, in *HelloBetterRequest, opts ...grpc.CallOption) (*HelloBetterResponse, error)
	// hello world api by perqin
	HelloPerqin(ctx context.Context, in *HelloPerqinRequest, opts ...grpc.CallOption) (*HelloPerqinResponse, error)
	// hello world api by yueyue
	HelloYueyue(ctx context.Context, in *HelloYueyueRequest, opts ...grpc.CallOption) (*HelloYueyueResponse, error)
	// hello world api by kai
	HelloKai(ctx context.Context, in *HelloKaiRequest, opts ...grpc.CallOption) (*HelloKaiResponse, error)
	// hello world api by kuroko
	HelloKuroko(ctx context.Context, in *HelloKurokoRequest, opts ...grpc.CallOption) (*HelloKurokoResponse, error)
	// hello world api by Bryson
	HelloBryson(ctx context.Context, in *HelloBrysonRequest, opts ...grpc.CallOption) (*HelloBrysonResponse, error)
	// hello world api by Harvie
	HelloHarvie(ctx context.Context, in *HelloHarvieRequest, opts ...grpc.CallOption) (*HelloHarvieResponse, error)
}

type todoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTodoServiceClient(cc grpc.ClientConnInterface) TodoServiceClient {
	return &todoServiceClient{cc}
}

func (c *todoServiceClient) AddTodo(ctx context.Context, in *AddTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/AddTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) GetTodo(ctx context.Context, in *v11.OwnId, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/GetTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) ListTodo(ctx context.Context, in *ListTodoRequest, opts ...grpc.CallOption) (*v12.EntityListModel, error) {
	out := new(v12.EntityListModel)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/ListTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) UpdateTodo(ctx context.Context, in *UpdateTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/UpdateTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) DeleteTodo(ctx context.Context, in *v11.OwnId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/DeleteTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloJett(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloJettResponse, error) {
	out := new(HelloJettResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloJett", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) EchoHz(ctx context.Context, in *EchoHzRequest, opts ...grpc.CallOption) (*EchoHzResponse, error) {
	out := new(EchoHzResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/EchoHz", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloArk(ctx context.Context, in *HelloArkRequest, opts ...grpc.CallOption) (*HelloArkResponse, error) {
	out := new(HelloArkResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloArk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloBetter(ctx context.Context, in *HelloBetterRequest, opts ...grpc.CallOption) (*HelloBetterResponse, error) {
	out := new(HelloBetterResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloBetter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloPerqin(ctx context.Context, in *HelloPerqinRequest, opts ...grpc.CallOption) (*HelloPerqinResponse, error) {
	out := new(HelloPerqinResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloPerqin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloYueyue(ctx context.Context, in *HelloYueyueRequest, opts ...grpc.CallOption) (*HelloYueyueResponse, error) {
	out := new(HelloYueyueResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloYueyue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloKai(ctx context.Context, in *HelloKaiRequest, opts ...grpc.CallOption) (*HelloKaiResponse, error) {
	out := new(HelloKaiResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloKai", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloKuroko(ctx context.Context, in *HelloKurokoRequest, opts ...grpc.CallOption) (*HelloKurokoResponse, error) {
	out := new(HelloKurokoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloKuroko", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloBryson(ctx context.Context, in *HelloBrysonRequest, opts ...grpc.CallOption) (*HelloBrysonResponse, error) {
	out := new(HelloBrysonResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloBryson", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloHarvie(ctx context.Context, in *HelloHarvieRequest, opts ...grpc.CallOption) (*HelloHarvieResponse, error) {
	out := new(HelloHarvieResponse)
	err := c.cc.Invoke(ctx, "/moego.service.todo.v1.TodoService/HelloHarvie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TodoServiceServer is the server API for TodoService service.
// All implementations must embed UnimplementedTodoServiceServer
// for forward compatibility
type TodoServiceServer interface {
	// 添加 Todo
	AddTodo(context.Context, *AddTodoRequest) (*v1.TodoModel, error)
	// 根据 id 获取单个 Todo
	GetTodo(context.Context, *v11.OwnId) (*v1.TodoModel, error)
	// 查询 Todo 列表
	ListTodo(context.Context, *ListTodoRequest) (*v12.EntityListModel, error)
	// 更新 Todo
	UpdateTodo(context.Context, *UpdateTodoRequest) (*v1.TodoModel, error)
	// 删除 Todo
	DeleteTodo(context.Context, *v11.OwnId) (*emptypb.Empty, error)
	// hello world api by jett
	HelloJett(context.Context, *emptypb.Empty) (*HelloJettResponse, error)
	// echo api by haozhi
	EchoHz(context.Context, *EchoHzRequest) (*EchoHzResponse, error)
	// hello world api by ark
	HelloArk(context.Context, *HelloArkRequest) (*HelloArkResponse, error)
	// hello world api by better
	HelloBetter(context.Context, *HelloBetterRequest) (*HelloBetterResponse, error)
	// hello world api by perqin
	HelloPerqin(context.Context, *HelloPerqinRequest) (*HelloPerqinResponse, error)
	// hello world api by yueyue
	HelloYueyue(context.Context, *HelloYueyueRequest) (*HelloYueyueResponse, error)
	// hello world api by kai
	HelloKai(context.Context, *HelloKaiRequest) (*HelloKaiResponse, error)
	// hello world api by kuroko
	HelloKuroko(context.Context, *HelloKurokoRequest) (*HelloKurokoResponse, error)
	// hello world api by Bryson
	HelloBryson(context.Context, *HelloBrysonRequest) (*HelloBrysonResponse, error)
	// hello world api by Harvie
	HelloHarvie(context.Context, *HelloHarvieRequest) (*HelloHarvieResponse, error)
	mustEmbedUnimplementedTodoServiceServer()
}

// UnimplementedTodoServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTodoServiceServer struct {
}

func (UnimplementedTodoServiceServer) AddTodo(context.Context, *AddTodoRequest) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTodo not implemented")
}
func (UnimplementedTodoServiceServer) GetTodo(context.Context, *v11.OwnId) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTodo not implemented")
}
func (UnimplementedTodoServiceServer) ListTodo(context.Context, *ListTodoRequest) (*v12.EntityListModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTodo not implemented")
}
func (UnimplementedTodoServiceServer) UpdateTodo(context.Context, *UpdateTodoRequest) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTodo not implemented")
}
func (UnimplementedTodoServiceServer) DeleteTodo(context.Context, *v11.OwnId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTodo not implemented")
}
func (UnimplementedTodoServiceServer) HelloJett(context.Context, *emptypb.Empty) (*HelloJettResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloJett not implemented")
}
func (UnimplementedTodoServiceServer) EchoHz(context.Context, *EchoHzRequest) (*EchoHzResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EchoHz not implemented")
}
func (UnimplementedTodoServiceServer) HelloArk(context.Context, *HelloArkRequest) (*HelloArkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloArk not implemented")
}
func (UnimplementedTodoServiceServer) HelloBetter(context.Context, *HelloBetterRequest) (*HelloBetterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloBetter not implemented")
}
func (UnimplementedTodoServiceServer) HelloPerqin(context.Context, *HelloPerqinRequest) (*HelloPerqinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloPerqin not implemented")
}
func (UnimplementedTodoServiceServer) HelloYueyue(context.Context, *HelloYueyueRequest) (*HelloYueyueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloYueyue not implemented")
}
func (UnimplementedTodoServiceServer) HelloKai(context.Context, *HelloKaiRequest) (*HelloKaiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloKai not implemented")
}
func (UnimplementedTodoServiceServer) HelloKuroko(context.Context, *HelloKurokoRequest) (*HelloKurokoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloKuroko not implemented")
}
func (UnimplementedTodoServiceServer) HelloBryson(context.Context, *HelloBrysonRequest) (*HelloBrysonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloBryson not implemented")
}
func (UnimplementedTodoServiceServer) HelloHarvie(context.Context, *HelloHarvieRequest) (*HelloHarvieResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloHarvie not implemented")
}
func (UnimplementedTodoServiceServer) mustEmbedUnimplementedTodoServiceServer() {}

// UnsafeTodoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TodoServiceServer will
// result in compilation errors.
type UnsafeTodoServiceServer interface {
	mustEmbedUnimplementedTodoServiceServer()
}

func RegisterTodoServiceServer(s grpc.ServiceRegistrar, srv TodoServiceServer) {
	s.RegisterService(&TodoService_ServiceDesc, srv)
}

func _TodoService_AddTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTodoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).AddTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/AddTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).AddTodo(ctx, req.(*AddTodoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_GetTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.OwnId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).GetTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/GetTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).GetTodo(ctx, req.(*v11.OwnId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_ListTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTodoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).ListTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/ListTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).ListTodo(ctx, req.(*ListTodoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_UpdateTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTodoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).UpdateTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/UpdateTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).UpdateTodo(ctx, req.(*UpdateTodoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_DeleteTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.OwnId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).DeleteTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/DeleteTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).DeleteTodo(ctx, req.(*v11.OwnId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloJett_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloJett(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloJett",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloJett(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_EchoHz_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EchoHzRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).EchoHz(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/EchoHz",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).EchoHz(ctx, req.(*EchoHzRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloArk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloArkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloArk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloArk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloArk(ctx, req.(*HelloArkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloBetter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloBetterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloBetter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloBetter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloBetter(ctx, req.(*HelloBetterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloPerqin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloPerqinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloPerqin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloPerqin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloPerqin(ctx, req.(*HelloPerqinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloYueyue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloYueyueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloYueyue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloYueyue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloYueyue(ctx, req.(*HelloYueyueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloKai_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloKaiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloKai(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloKai",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloKai(ctx, req.(*HelloKaiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloKuroko_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloKurokoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloKuroko(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloKuroko",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloKuroko(ctx, req.(*HelloKurokoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloBryson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloBrysonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloBryson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloBryson",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloBryson(ctx, req.(*HelloBrysonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloHarvie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloHarvieRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloHarvie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.todo.v1.TodoService/HelloHarvie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloHarvie(ctx, req.(*HelloHarvieRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TodoService_ServiceDesc is the grpc.ServiceDesc for TodoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TodoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.todo.v1.TodoService",
	HandlerType: (*TodoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddTodo",
			Handler:    _TodoService_AddTodo_Handler,
		},
		{
			MethodName: "GetTodo",
			Handler:    _TodoService_GetTodo_Handler,
		},
		{
			MethodName: "ListTodo",
			Handler:    _TodoService_ListTodo_Handler,
		},
		{
			MethodName: "UpdateTodo",
			Handler:    _TodoService_UpdateTodo_Handler,
		},
		{
			MethodName: "DeleteTodo",
			Handler:    _TodoService_DeleteTodo_Handler,
		},
		{
			MethodName: "HelloJett",
			Handler:    _TodoService_HelloJett_Handler,
		},
		{
			MethodName: "EchoHz",
			Handler:    _TodoService_EchoHz_Handler,
		},
		{
			MethodName: "HelloArk",
			Handler:    _TodoService_HelloArk_Handler,
		},
		{
			MethodName: "HelloBetter",
			Handler:    _TodoService_HelloBetter_Handler,
		},
		{
			MethodName: "HelloPerqin",
			Handler:    _TodoService_HelloPerqin_Handler,
		},
		{
			MethodName: "HelloYueyue",
			Handler:    _TodoService_HelloYueyue_Handler,
		},
		{
			MethodName: "HelloKai",
			Handler:    _TodoService_HelloKai_Handler,
		},
		{
			MethodName: "HelloKuroko",
			Handler:    _TodoService_HelloKuroko_Handler,
		},
		{
			MethodName: "HelloBryson",
			Handler:    _TodoService_HelloBryson_Handler,
		},
		{
			MethodName: "HelloHarvie",
			Handler:    _TodoService_HelloHarvie_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/todo/v1/todo_service.proto",
}
