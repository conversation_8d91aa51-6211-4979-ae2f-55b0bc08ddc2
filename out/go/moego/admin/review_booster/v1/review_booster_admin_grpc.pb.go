// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/review_booster/v1/review_booster_admin.proto

package reviewboosterapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ReviewBoosterAdminServiceClient is the client API for ReviewBoosterAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReviewBoosterAdminServiceClient interface {
	// query review booster record list
	ListReviewBoosterRecord(ctx context.Context, in *ListReviewBoosterRecordParams, opts ...grpc.CallOption) (*ListReviewBoosterRecordResult, error)
	// update review booster record
	UpdateReviewBoosterRecord(ctx context.Context, in *UpdateReviewBoosterRecordParams, opts ...grpc.CallOption) (*UpdateReviewBoosterRecordParams, error)
}

type reviewBoosterAdminServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReviewBoosterAdminServiceClient(cc grpc.ClientConnInterface) ReviewBoosterAdminServiceClient {
	return &reviewBoosterAdminServiceClient{cc}
}

func (c *reviewBoosterAdminServiceClient) ListReviewBoosterRecord(ctx context.Context, in *ListReviewBoosterRecordParams, opts ...grpc.CallOption) (*ListReviewBoosterRecordResult, error) {
	out := new(ListReviewBoosterRecordResult)
	err := c.cc.Invoke(ctx, "/moego.admin.review_booster.v1.ReviewBoosterAdminService/ListReviewBoosterRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewBoosterAdminServiceClient) UpdateReviewBoosterRecord(ctx context.Context, in *UpdateReviewBoosterRecordParams, opts ...grpc.CallOption) (*UpdateReviewBoosterRecordParams, error) {
	out := new(UpdateReviewBoosterRecordParams)
	err := c.cc.Invoke(ctx, "/moego.admin.review_booster.v1.ReviewBoosterAdminService/UpdateReviewBoosterRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReviewBoosterAdminServiceServer is the server API for ReviewBoosterAdminService service.
// All implementations must embed UnimplementedReviewBoosterAdminServiceServer
// for forward compatibility
type ReviewBoosterAdminServiceServer interface {
	// query review booster record list
	ListReviewBoosterRecord(context.Context, *ListReviewBoosterRecordParams) (*ListReviewBoosterRecordResult, error)
	// update review booster record
	UpdateReviewBoosterRecord(context.Context, *UpdateReviewBoosterRecordParams) (*UpdateReviewBoosterRecordParams, error)
	mustEmbedUnimplementedReviewBoosterAdminServiceServer()
}

// UnimplementedReviewBoosterAdminServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReviewBoosterAdminServiceServer struct {
}

func (UnimplementedReviewBoosterAdminServiceServer) ListReviewBoosterRecord(context.Context, *ListReviewBoosterRecordParams) (*ListReviewBoosterRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListReviewBoosterRecord not implemented")
}
func (UnimplementedReviewBoosterAdminServiceServer) UpdateReviewBoosterRecord(context.Context, *UpdateReviewBoosterRecordParams) (*UpdateReviewBoosterRecordParams, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReviewBoosterRecord not implemented")
}
func (UnimplementedReviewBoosterAdminServiceServer) mustEmbedUnimplementedReviewBoosterAdminServiceServer() {
}

// UnsafeReviewBoosterAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReviewBoosterAdminServiceServer will
// result in compilation errors.
type UnsafeReviewBoosterAdminServiceServer interface {
	mustEmbedUnimplementedReviewBoosterAdminServiceServer()
}

func RegisterReviewBoosterAdminServiceServer(s grpc.ServiceRegistrar, srv ReviewBoosterAdminServiceServer) {
	s.RegisterService(&ReviewBoosterAdminService_ServiceDesc, srv)
}

func _ReviewBoosterAdminService_ListReviewBoosterRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListReviewBoosterRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewBoosterAdminServiceServer).ListReviewBoosterRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.review_booster.v1.ReviewBoosterAdminService/ListReviewBoosterRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewBoosterAdminServiceServer).ListReviewBoosterRecord(ctx, req.(*ListReviewBoosterRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewBoosterAdminService_UpdateReviewBoosterRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReviewBoosterRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewBoosterAdminServiceServer).UpdateReviewBoosterRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.review_booster.v1.ReviewBoosterAdminService/UpdateReviewBoosterRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewBoosterAdminServiceServer).UpdateReviewBoosterRecord(ctx, req.(*UpdateReviewBoosterRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ReviewBoosterAdminService_ServiceDesc is the grpc.ServiceDesc for ReviewBoosterAdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReviewBoosterAdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.review_booster.v1.ReviewBoosterAdminService",
	HandlerType: (*ReviewBoosterAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListReviewBoosterRecord",
			Handler:    _ReviewBoosterAdminService_ListReviewBoosterRecord_Handler,
		},
		{
			MethodName: "UpdateReviewBoosterRecord",
			Handler:    _ReviewBoosterAdminService_UpdateReviewBoosterRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/review_booster/v1/review_booster_admin.proto",
}
