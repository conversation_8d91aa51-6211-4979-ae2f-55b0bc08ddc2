// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/moego_pay_custom_fee_approval.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MoegoPayCustomFeeApprovalApiClient is the client API for MoegoPayCustomFeeApprovalApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MoegoPayCustomFeeApprovalApiClient interface {
	// List custom fee approvals
	ListMoegoPayMoegoPayCustomFeeApproval(ctx context.Context, in *ListMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*ListMoegoPayCustomFeeApprovalResult, error)
	// Approve custom fee approval
	ApproveMoegoPayCustomFeeApproval(ctx context.Context, in *ApproveMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*ApproveMoegoPayCustomFeeApprovalResult, error)
	// Reject custom fee approval
	RejectMoegoPayCustomFeeApproval(ctx context.Context, in *RejectMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*RejectMoegoPayCustomFeeApprovalResult, error)
}

type moegoPayCustomFeeApprovalApiClient struct {
	cc grpc.ClientConnInterface
}

func NewMoegoPayCustomFeeApprovalApiClient(cc grpc.ClientConnInterface) MoegoPayCustomFeeApprovalApiClient {
	return &moegoPayCustomFeeApprovalApiClient{cc}
}

func (c *moegoPayCustomFeeApprovalApiClient) ListMoegoPayMoegoPayCustomFeeApproval(ctx context.Context, in *ListMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*ListMoegoPayCustomFeeApprovalResult, error) {
	out := new(ListMoegoPayCustomFeeApprovalResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/ListMoegoPayMoegoPayCustomFeeApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalApiClient) ApproveMoegoPayCustomFeeApproval(ctx context.Context, in *ApproveMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*ApproveMoegoPayCustomFeeApprovalResult, error) {
	out := new(ApproveMoegoPayCustomFeeApprovalResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/ApproveMoegoPayCustomFeeApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayCustomFeeApprovalApiClient) RejectMoegoPayCustomFeeApproval(ctx context.Context, in *RejectMoegoPayCustomFeeApprovalParams, opts ...grpc.CallOption) (*RejectMoegoPayCustomFeeApprovalResult, error) {
	out := new(RejectMoegoPayCustomFeeApprovalResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/RejectMoegoPayCustomFeeApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoegoPayCustomFeeApprovalApiServer is the server API for MoegoPayCustomFeeApprovalApi service.
// All implementations must embed UnimplementedMoegoPayCustomFeeApprovalApiServer
// for forward compatibility
type MoegoPayCustomFeeApprovalApiServer interface {
	// List custom fee approvals
	ListMoegoPayMoegoPayCustomFeeApproval(context.Context, *ListMoegoPayCustomFeeApprovalParams) (*ListMoegoPayCustomFeeApprovalResult, error)
	// Approve custom fee approval
	ApproveMoegoPayCustomFeeApproval(context.Context, *ApproveMoegoPayCustomFeeApprovalParams) (*ApproveMoegoPayCustomFeeApprovalResult, error)
	// Reject custom fee approval
	RejectMoegoPayCustomFeeApproval(context.Context, *RejectMoegoPayCustomFeeApprovalParams) (*RejectMoegoPayCustomFeeApprovalResult, error)
	mustEmbedUnimplementedMoegoPayCustomFeeApprovalApiServer()
}

// UnimplementedMoegoPayCustomFeeApprovalApiServer must be embedded to have forward compatible implementations.
type UnimplementedMoegoPayCustomFeeApprovalApiServer struct {
}

func (UnimplementedMoegoPayCustomFeeApprovalApiServer) ListMoegoPayMoegoPayCustomFeeApproval(context.Context, *ListMoegoPayCustomFeeApprovalParams) (*ListMoegoPayCustomFeeApprovalResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMoegoPayMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalApiServer) ApproveMoegoPayCustomFeeApproval(context.Context, *ApproveMoegoPayCustomFeeApprovalParams) (*ApproveMoegoPayCustomFeeApprovalResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalApiServer) RejectMoegoPayCustomFeeApproval(context.Context, *RejectMoegoPayCustomFeeApprovalParams) (*RejectMoegoPayCustomFeeApprovalResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectMoegoPayCustomFeeApproval not implemented")
}
func (UnimplementedMoegoPayCustomFeeApprovalApiServer) mustEmbedUnimplementedMoegoPayCustomFeeApprovalApiServer() {
}

// UnsafeMoegoPayCustomFeeApprovalApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MoegoPayCustomFeeApprovalApiServer will
// result in compilation errors.
type UnsafeMoegoPayCustomFeeApprovalApiServer interface {
	mustEmbedUnimplementedMoegoPayCustomFeeApprovalApiServer()
}

func RegisterMoegoPayCustomFeeApprovalApiServer(s grpc.ServiceRegistrar, srv MoegoPayCustomFeeApprovalApiServer) {
	s.RegisterService(&MoegoPayCustomFeeApprovalApi_ServiceDesc, srv)
}

func _MoegoPayCustomFeeApprovalApi_ListMoegoPayMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMoegoPayCustomFeeApprovalParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalApiServer).ListMoegoPayMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/ListMoegoPayMoegoPayCustomFeeApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalApiServer).ListMoegoPayMoegoPayCustomFeeApproval(ctx, req.(*ListMoegoPayCustomFeeApprovalParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalApi_ApproveMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveMoegoPayCustomFeeApprovalParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalApiServer).ApproveMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/ApproveMoegoPayCustomFeeApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalApiServer).ApproveMoegoPayCustomFeeApproval(ctx, req.(*ApproveMoegoPayCustomFeeApprovalParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayCustomFeeApprovalApi_RejectMoegoPayCustomFeeApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectMoegoPayCustomFeeApprovalParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayCustomFeeApprovalApiServer).RejectMoegoPayCustomFeeApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi/RejectMoegoPayCustomFeeApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayCustomFeeApprovalApiServer).RejectMoegoPayCustomFeeApproval(ctx, req.(*RejectMoegoPayCustomFeeApprovalParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MoegoPayCustomFeeApprovalApi_ServiceDesc is the grpc.ServiceDesc for MoegoPayCustomFeeApprovalApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MoegoPayCustomFeeApprovalApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.MoegoPayCustomFeeApprovalApi",
	HandlerType: (*MoegoPayCustomFeeApprovalApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListMoegoPayMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalApi_ListMoegoPayMoegoPayCustomFeeApproval_Handler,
		},
		{
			MethodName: "ApproveMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalApi_ApproveMoegoPayCustomFeeApproval_Handler,
		},
		{
			MethodName: "RejectMoegoPayCustomFeeApproval",
			Handler:    _MoegoPayCustomFeeApprovalApi_RejectMoegoPayCustomFeeApproval_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/moego_pay_custom_fee_approval.proto",
}
