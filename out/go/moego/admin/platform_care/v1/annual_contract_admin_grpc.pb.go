// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/platform_care/v1/annual_contract_admin.proto

package platformcareapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AnnualContractApiClient is the client API for AnnualContractApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnnualContractApiClient interface {
	// create annual contract
	CreateAnnualContract(ctx context.Context, in *CreateAnnualContractParams, opts ...grpc.CallOption) (*CreateAnnualContractResult, error)
	// list annual contracts
	ListAnnualContracts(ctx context.Context, in *ListAnnualContractsParams, opts ...grpc.CallOption) (*ListAnnualContractsResult, error)
}

type annualContractApiClient struct {
	cc grpc.ClientConnInterface
}

func NewAnnualContractApiClient(cc grpc.ClientConnInterface) AnnualContractApiClient {
	return &annualContractApiClient{cc}
}

func (c *annualContractApiClient) CreateAnnualContract(ctx context.Context, in *CreateAnnualContractParams, opts ...grpc.CallOption) (*CreateAnnualContractResult, error) {
	out := new(CreateAnnualContractResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.AnnualContractApi/CreateAnnualContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *annualContractApiClient) ListAnnualContracts(ctx context.Context, in *ListAnnualContractsParams, opts ...grpc.CallOption) (*ListAnnualContractsResult, error) {
	out := new(ListAnnualContractsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.AnnualContractApi/ListAnnualContracts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnnualContractApiServer is the server API for AnnualContractApi service.
// All implementations must embed UnimplementedAnnualContractApiServer
// for forward compatibility
type AnnualContractApiServer interface {
	// create annual contract
	CreateAnnualContract(context.Context, *CreateAnnualContractParams) (*CreateAnnualContractResult, error)
	// list annual contracts
	ListAnnualContracts(context.Context, *ListAnnualContractsParams) (*ListAnnualContractsResult, error)
	mustEmbedUnimplementedAnnualContractApiServer()
}

// UnimplementedAnnualContractApiServer must be embedded to have forward compatible implementations.
type UnimplementedAnnualContractApiServer struct {
}

func (UnimplementedAnnualContractApiServer) CreateAnnualContract(context.Context, *CreateAnnualContractParams) (*CreateAnnualContractResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAnnualContract not implemented")
}
func (UnimplementedAnnualContractApiServer) ListAnnualContracts(context.Context, *ListAnnualContractsParams) (*ListAnnualContractsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAnnualContracts not implemented")
}
func (UnimplementedAnnualContractApiServer) mustEmbedUnimplementedAnnualContractApiServer() {}

// UnsafeAnnualContractApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnnualContractApiServer will
// result in compilation errors.
type UnsafeAnnualContractApiServer interface {
	mustEmbedUnimplementedAnnualContractApiServer()
}

func RegisterAnnualContractApiServer(s grpc.ServiceRegistrar, srv AnnualContractApiServer) {
	s.RegisterService(&AnnualContractApi_ServiceDesc, srv)
}

func _AnnualContractApi_CreateAnnualContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAnnualContractParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractApiServer).CreateAnnualContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.AnnualContractApi/CreateAnnualContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractApiServer).CreateAnnualContract(ctx, req.(*CreateAnnualContractParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnnualContractApi_ListAnnualContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAnnualContractsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnualContractApiServer).ListAnnualContracts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.AnnualContractApi/ListAnnualContracts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnualContractApiServer).ListAnnualContracts(ctx, req.(*ListAnnualContractsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AnnualContractApi_ServiceDesc is the grpc.ServiceDesc for AnnualContractApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnnualContractApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.platform_care.v1.AnnualContractApi",
	HandlerType: (*AnnualContractApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAnnualContract",
			Handler:    _AnnualContractApi_CreateAnnualContract_Handler,
		},
		{
			MethodName: "ListAnnualContracts",
			Handler:    _AnnualContractApi_ListAnnualContracts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/platform_care/v1/annual_contract_admin.proto",
}
