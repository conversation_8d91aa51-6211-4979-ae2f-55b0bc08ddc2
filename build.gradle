plugins {
  id 'java'
  id 'jacoco'
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}" apply false
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'com.diffplug.spotless' version "${spotlessVersion}" apply false
  id "com.github.spotbugs" version "${spotbugsVersion}" apply false
  id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
  id 'net.razvan.jacoco-to-cobertura' version "${jacocoToCoberturaPlugin}"
}

repositories {
  mavenCentral()
  maven { url 'https://jitpack.io' }
}

// dependency management
apply plugin: 'io.spring.dependency-management'
dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  implementation("io.grpc:grpc-services:${grpcVersion}")
  implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
  implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")
  runtimeOnly("com.mysql:mysql-connector-j")
  implementation("org.postgresql:postgresql")

  // lombok
  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.2'
  // aws jdbc driver
  implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.5.3")

  // mapstruct
  implementation 'com.fasterxml.jackson.core:jackson-databind'

  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'
  implementation 'io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.2.1'

  implementation 'org.mapstruct:mapstruct:1.5.3.Final'
  annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
  testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
  annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
  testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
  testImplementation 'org.awaitility:awaitility'
}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile) {
  options.encoding = 'UTF-8'
}

bootJar {
  archiveBaseName = 'moego-server'
}

test {
  useJUnitPlatform()
  testLogging {
    events 'failed'
    exceptionFormat 'full'
  }
}

// spotless
apply plugin: 'com.diffplug.spotless'
spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude(
      "build/generated/**",
      "src/main/java/**/mapper/mysql/*",
      "src/main/java/**/mapper/pg/*",
      "src/main/java/**/entity/*",
      "src/main/java/**/domain/*",
      "src/main/jooq/**"
    )

    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)
  }
}
// spotbugs
apply plugin: 'com.github.spotbugs'
spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll("FindReturnRef", "MethodReturnCheck", "DontReusePublicIdentifiers")
  excludeFilter.set(file("${rootDir}/config/spotbugs/exclude.xml"))
}

configurations {
  mybatisGenerator
}
mybatisGenerator {
  verbose = true
  configFile = "${projectDir}/src/main/resources/MyBatisGeneratorConfig.xml"
  dependencies {
    mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorVersion}"
    mybatisGenerator "com.mysql:mysql-connector-j"
    mybatisGenerator "org.postgresql:postgresql"
    mybatisGenerator "com.moego.lib:moego-lib-mybatis-plugins"
  }
}

// mbGenerator needs moego-lib-mybatis-plugins.jar, so we need to make sure it is built before mbGenerator
mbGenerator.dependsOn gradle.includedBuild("moego-java-lib").task(":moego-lib-mybatis-plugins:jar")

jacocoTestReport {
  reports {
    xml {
      required = true
      destination = file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }

  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, include: ["**/service/**", "**/converter/**"])
    }))
  }
}
